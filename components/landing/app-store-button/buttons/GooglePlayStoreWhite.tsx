import React from 'react';

export const GooglePlayStoreWhite = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      className="h-full w-auto"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 135 40"
      {...props}
    >
      <rect x=".5" y=".5" width="134" height="39" rx="4.5" fill="#000" />
      <rect x=".5" y=".5" width="134" height="39" rx="4.5" stroke="#A6A6A6" />
      <path
        d="M106.935 30h1.866V17.498h-1.866V30Zm16.807-7.998-2.139 5.42h-.064l-2.22-5.42h-2.01l3.33 7.575-1.899 4.214h1.946l5.131-11.789h-2.075Zm-10.583 6.578c-.61 0-1.463-.306-1.463-1.062 0-.965 1.062-1.334 1.978-1.334.82 0 1.207.176 1.705.418a2.262 2.262 0 0 1-2.22 1.978Zm.226-6.851c-1.351 0-2.75.595-3.329 1.914l1.656.691c.354-.691 1.013-.916 1.705-.916.965 0 1.946.578 1.962 1.608v.128c-.338-.193-1.062-.482-1.946-.482-1.786 0-3.603.98-3.603 2.814 0 1.673 1.464 2.75 3.104 2.75 1.254 0 1.947-.562 2.38-1.222h.065v.966h1.802v-4.794c0-2.22-1.658-3.457-3.796-3.457Zm-11.532 1.795h-2.654v-4.285h2.654c1.395 0 2.187 1.154 2.187 2.142 0 .969-.792 2.143-2.187 2.143Zm-.048-6.026h-4.472V30H99.2v-4.736h2.606c2.068 0 4.101-1.497 4.101-3.883 0-2.385-2.033-3.883-4.101-3.883ZM77.423 28.582c-1.289 0-2.368-1.08-2.368-2.56 0-1.5 1.08-2.595 2.368-2.595 1.273 0 2.272 1.095 2.272 2.594 0 1.482-1 2.561-2.272 2.561Zm2.143-5.88H79.5c-.418-.5-1.224-.95-2.239-.95-2.127 0-4.076 1.869-4.076 4.27 0 2.383 1.95 4.236 4.076 4.236 1.015 0 1.82-.451 2.24-.967h.064v.612c0 1.628-.87 2.497-2.272 2.497-1.143 0-1.852-.821-2.142-1.514l-1.627.677c.467 1.127 1.707 2.513 3.77 2.513 2.19 0 4.043-1.289 4.043-4.43v-7.637h-1.772v.693ZM82.627 30h1.869V17.498h-1.868V30Zm4.624-4.124c-.049-1.644 1.273-2.481 2.223-2.481.741 0 1.37.37 1.58.902l-3.803 1.579Zm5.8-1.418c-.355-.95-1.434-2.706-3.642-2.706-2.19 0-4.011 1.723-4.011 4.253 0 2.384 1.805 4.253 4.22 4.253 1.95 0 3.078-1.192 3.545-1.885l-1.45-.967c-.483.71-1.143 1.176-2.094 1.176-.95 0-1.627-.435-2.062-1.289l5.686-2.352-.193-.483Zm-45.309-1.401v1.804h4.318c-.129 1.015-.467 1.756-.983 2.271-.628.628-1.611 1.321-3.335 1.321-2.658 0-4.736-2.142-4.736-4.8 0-2.659 2.078-4.801 4.736-4.801 1.434 0 2.481.564 3.255 1.289l1.273-1.273c-1.08-1.031-2.514-1.82-4.528-1.82-3.641 0-6.702 2.964-6.702 6.605 0 3.64 3.06 6.605 6.702 6.605 1.966 0 3.448-.645 4.608-1.853 1.192-1.192 1.563-2.867 1.563-4.221 0-.418-.033-.805-.097-1.127h-6.074Zm11.08 5.525c-1.29 0-2.402-1.063-2.402-2.577 0-1.53 1.112-2.578 2.401-2.578 1.289 0 2.4 1.047 2.4 2.578 0 1.514-1.111 2.577-2.4 2.577Zm0-6.83c-2.354 0-4.27 1.788-4.27 4.253 0 2.448 1.916 4.253 4.27 4.253 2.351 0 4.268-1.805 4.268-4.253 0-2.465-1.917-4.253-4.269-4.253Zm9.312 6.83c-1.288 0-2.4-1.063-2.4-2.577 0-1.53 1.112-2.578 2.4-2.578 1.29 0 2.4 1.047 2.4 2.578 0 1.514-1.11 2.577-2.4 2.577Zm0-6.83c-2.352 0-4.269 1.788-4.269 4.253 0 2.448 1.917 4.253 4.27 4.253 2.352 0 4.269-1.805 4.269-4.253 0-2.465-1.917-4.253-4.27-4.253ZM44.477 13.234c-.896 0-1.668-.315-2.29-.937-.621-.622-.936-1.4-.936-2.306 0-.906.315-1.684.937-2.307.621-.622 1.393-.937 2.29-.937a3.4 3.4 0 0 1 1.29.243c.407.164.74.392 1.003.69l.062.071-.7.7-.07-.086a1.732 1.732 0 0 0-.675-.501 2.283 2.283 0 0 0-.91-.177c-.632 0-1.156.215-1.594.65v.001c-.428.445-.643.986-.643 1.653s.215 1.208.644 1.653a2.16 2.16 0 0 0 1.592.651c.578 0 1.056-.161 1.432-.48.35-.298.56-.7.635-1.212h-2.167v-.922h3.103l.013.085c.026.162.043.318.043.468 0 .86-.262 1.563-.776 2.08-.581.615-1.345.92-2.283.92Zm26.942 0c-.898 0-1.66-.315-2.274-.937-.615-.615-.92-1.394-.92-2.306 0-.913.305-1.692.92-2.307.614-.622 1.376-.937 2.274-.937.896 0 1.659.315 2.272.946.615.615.92 1.393.92 2.298 0 .912-.305 1.691-.92 2.306-.614.622-1.383.937-2.272.937ZM48.424 13.1V6.881h3.714v.94h-2.74V9.53h2.472v.922h-2.472v1.709h2.74v.939h-3.714Zm5.987 0V7.822h-1.68v-.94h4.335v.94h-1.681V13.1h-.974Zm5.426 0V6.881h.974V13.1h-.974Zm3.424 0V7.822H61.58v-.94h4.336v.94H64.234V13.1h-.973Zm12.214 0V6.881h1.098l2.793 4.473-.024-.85V6.881h.974V13.1h-.963l-2.928-4.698.024.848V13.1h-.974Zm-4.056-.805c.631 0 1.147-.215 1.567-.65v-.001h.001c.427-.427.635-.976.635-1.653 0-.676-.209-1.227-.635-1.653v-.001c-.421-.435-.936-.65-1.568-.65-.633 0-1.148.214-1.576.65-.419.436-.627.978-.627 1.654 0 .677.208 1.217.626 1.654.429.435.944.65 1.577.65Z"
        fill="#fff"
      />
      <path
        d="m20.715 19.423-10.647 11.3.002.007a2.875 2.875 0 0 0 4.236 1.734l.034-.02 11.984-6.915-5.61-6.106Z"
        fill="#EA4335"
      />
      <path
        d="m31.486 17.5-.01-.008-5.174-3-5.83 5.188 5.85 5.848 5.146-2.97a2.877 2.877 0 0 0 .018-5.059Z"
        fill="#FBBC04"
      />
      <path
        d="M10.068 9.276a2.818 2.818 0 0 0-.098.74v19.968c0 .256.033.504.098.74l11.013-11.012L10.068 9.276Z"
        fill="#4285F4"
      />
      <path
        d="m20.793 20 5.51-5.51-11.97-6.94a2.882 2.882 0 0 0-4.265 1.723v.003L20.793 20Z"
        fill="#34A853"
      />
    </svg>
  );
};

export default GooglePlayStoreWhite;

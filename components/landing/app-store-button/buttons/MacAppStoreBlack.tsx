import React from 'react';

export const MacAppStoreBlack = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      className="h-full w-auto"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 157 40"
      {...props}
    >
      <g clipPath="url(#a)">
        <path
          d="M146.571 0H9.535c-.367 0-.73 0-1.095.002-.306.002-.61.008-.919.013C6.85.023 6.18.082 5.517.19a6.665 6.665 0 0 0-1.9.627A6.438 6.438 0 0 0 .193 5.521a12.993 12.993 0 0 0-.179 2.002c-.01.307-.01.615-.015.921V31.56c.*************.015.921.008.671.068 1.34.18 2.002.11.663.32 1.306.624 1.905.303.598.701 1.143 1.179 1.614.473.477 1.019.875 1.618 1.179a6.7 6.7 0 0 0 1.901.63c.663.11 1.333.168 ************.007.613.011.919.011.366.002.728.002 1.095.002H146.57c.36 0 .725 0 1.084-.002.305 0 .617-.004.922-.01.67-.01 1.339-.068 2-.178a6.794 6.794 0 0 0 1.908-.63A6.281 6.281 0 0 0 154.103 38a6.401 6.401 0 0 0 1.181-1.614 6.59 6.59 0 0 0 .619-1.905c.112-.661.174-1.33.186-2.002.004-.31.004-.61.004-.921.008-.364.008-.725.008-1.094V9.536c0-.366 0-.73-.008-1.092 0-.306 0-.614-.004-.92a13.437 13.437 0 0 0-.186-2.003 6.587 6.587 0 0 0-.619-1.903 6.464 6.464 0 0 0-2.799-2.8 6.76 6.76 0 0 0-1.908-.627c-.661-.11-1.33-.169-2-.176-.305-.005-.617-.011-.922-.013C147.296 0 146.931 0 146.571 0Z"
          fill="#000"
        />
        <path
          d="M8.445 39.125c-.305 0-.602-.004-.904-.01a12.696 12.696 0 0 1-1.87-.164 5.884 5.884 0 0 1-1.656-.548 5.406 5.406 0 0 1-1.397-1.016 5.321 5.321 0 0 1-1.02-1.397 5.722 5.722 0 0 1-.544-1.657 12.413 12.413 0 0 1-.166-1.875c-.007-.21-.015-.913-.015-.913v-23.1s.009-.692.015-.895a12.37 12.37 0 0 1 .165-1.872 5.755 5.755 0 0 1 .544-1.662c.26-.518.603-.99 1.015-1.398A5.565 5.565 0 0 1 5.668 1.05C6.288.95 6.915.895 7.543.887l.902-.012H147.65l.913.013c.623.007 1.244.061 1.859.162a5.943 5.943 0 0 1 1.671.548 5.594 5.594 0 0 1 2.415 2.42c.26.52.441 1.076.535 1.649.104.624.162 1.255.174 1.887.003.283.003.588.003.89.008.375.008.732.008 1.092v20.929c0 .363 0 .718-.008 1.075 0 .325 0 .623-.004.93-.012.62-.069 1.24-.171 1.853a5.746 5.746 0 0 1-.54 1.67c-.264.513-.606.98-1.016 1.386a5.409 5.409 0 0 1-1.399 1.022 5.865 5.865 0 0 1-1.668.55c-.618.101-1.243.156-1.869.163-.293.007-.6.011-.898.011l-1.084.002-138.126-.002Z"
          fill="#fff"
        />
        <path
          d="M46.149 30.495v-9.14h-.06L42.344 30.4h-1.428l-3.753-9.045h-.06v9.14h-1.756V18.076h2.23l4.018 9.802h.069l4.01-9.802h2.238v12.419h-1.764ZM49.396 27.922c0-1.584 1.213-2.54 3.365-2.669l2.479-.138v-.688c0-1.007-.663-1.575-1.791-1.575a1.73 1.73 0 0 0-1.902 1.274H49.81c.052-1.636 1.575-2.797 3.692-2.797 2.16 0 3.588 1.179 3.588 2.96v6.206h-1.78v-1.49h-.044a3.237 3.237 0 0 1-2.857 1.645 2.745 2.745 0 0 1-3.012-2.728Zm5.844-.818v-.697l-2.23.137c-1.11.07-1.738.551-1.738 1.326 0 .792.654 1.308 1.652 1.308a2.17 2.17 0 0 0 2.316-2.074ZM64.893 24.556a2 2 0 0 0-2.134-1.67c-1.428 0-2.375 1.197-2.375 3.082 0 1.928.955 3.09 2.392 3.09a1.948 1.948 0 0 0 2.117-1.627h1.79a3.618 3.618 0 0 1-3.924 3.176c-2.582 0-4.268-1.765-4.268-4.639 0-2.815 1.686-4.639 4.25-4.639a3.64 3.64 0 0 1 3.925 3.227h-1.773ZM78.76 27.138h-4.734l-1.137 3.357h-2.005l4.484-12.419h2.083l4.483 12.419h-2.039l-1.136-3.357Zm-4.244-1.549h3.752l-1.85-5.447h-.051l-1.85 5.447ZM91.617 25.968c0 2.814-1.506 4.621-3.779 4.621a3.07 3.07 0 0 1-2.848-1.584h-.043v4.485h-1.859V21.44h1.8v1.506h.033a3.212 3.212 0 0 1 2.883-1.6c2.298 0 3.813 1.816 3.813 4.622Zm-1.91 0c0-1.833-.948-3.038-2.393-3.038-1.42 0-2.375 1.23-2.375 3.038 0 1.825.955 3.046 2.375 3.046 1.445 0 2.393-1.196 2.393-3.046ZM101.582 25.968c0 2.814-1.506 4.621-3.779 4.621a3.069 3.069 0 0 1-2.848-1.584h-.043v4.485h-1.859V21.44h1.799v1.506h.034a3.212 3.212 0 0 1 2.883-1.6c2.298 0 3.813 1.816 3.813 4.622Zm-1.91 0c0-1.833-.948-3.038-2.393-3.038-1.42 0-2.375 1.23-2.375 3.038 0 1.825.955 3.046 2.375 3.046 1.445 0 2.392-1.196 2.392-3.046ZM108.167 27.035c.138 1.231 1.334 2.04 2.969 2.04 1.567 0 2.694-.809 2.694-1.92 0-.963-.68-1.54-2.289-1.936l-1.61-.388c-2.28-.55-3.339-1.617-3.339-3.348 0-2.142 1.867-3.614 4.519-3.614 2.624 0 4.423 1.472 4.483 3.614h-1.876c-.112-1.239-1.136-1.987-2.634-1.987-1.497 0-2.521.757-2.521 1.859 0 .878.654 1.394 2.255 1.79l1.368.335c2.548.603 3.606 1.627 3.606 3.443 0 2.324-1.85 3.779-4.793 3.779-2.754 0-4.614-1.421-4.734-3.667h1.902ZM119.803 19.298v2.143h1.722v1.471h-1.722v4.992c0 .775.345 1.137 1.102 1.137.204-.004.408-.018.611-.043v1.463c-.34.063-.686.092-1.032.086-1.833 0-2.548-.689-2.548-2.445v-5.19h-1.316v-1.471h1.316v-2.143h1.867ZM122.521 25.968c0-2.849 1.678-4.639 4.294-4.639 2.625 0 4.295 1.79 4.295 4.64 0 2.856-1.661 4.638-4.295 4.638-2.633 0-4.294-1.782-4.294-4.639Zm6.695 0c0-1.954-.895-3.108-2.401-3.108-1.506 0-2.401 1.163-2.401 3.108 0 1.962.895 3.107 2.401 3.107 1.506 0 2.401-1.145 2.401-3.107ZM132.642 21.44h1.773v1.541h.043a2.159 2.159 0 0 1 2.177-1.635c.214-.001.428.022.637.07v1.737a2.597 2.597 0 0 0-.835-.112 1.872 1.872 0 0 0-1.937 2.083v5.37h-1.858v-9.053ZM145.84 27.835c-.25 1.644-1.85 2.772-3.898 2.772-2.634 0-4.269-1.765-4.269-4.596 0-2.84 1.644-4.682 4.191-4.682 2.505 0 4.08 1.72 4.08 4.466v.637h-6.395v.113a2.36 2.36 0 0 0 2.436 2.564 2.049 2.049 0 0 0 2.091-1.274h1.764Zm-6.282-2.702h4.526a2.174 2.174 0 0 0-2.22-2.298 2.293 2.293 0 0 0-2.306 2.298ZM37.826 8.731a2.64 2.64 0 0 1 2.808 2.965c0 1.906-1.03 3.002-2.808 3.002h-2.155V8.73h2.155Zm-1.228 5.123h1.125a1.876 1.876 0 0 0 1.967-2.146 1.88 1.88 0 0 0-1.967-2.134h-1.125v4.28ZM41.68 12.444a2.133 2.133 0 1 1 4.248 0 2.133 2.133 0 1 1-4.247 0Zm3.334 0c0-.976-.439-1.546-1.208-1.546-.773 0-1.207.57-1.207 1.546 0 .984.434 1.55 1.207 1.55.77 0 1.208-.57 1.208-1.55ZM51.573 14.698h-.922l-.93-3.317h-.07l-.927 3.317h-.913l-1.242-4.503h.902l.806 3.436h.067l.925-3.436h.853l.926 3.436h.07l.803-3.436h.889l-1.237 4.503ZM53.853 10.195h.856v.715h.066c.231-.527.771-.849 1.344-.802a1.465 1.465 0 0 1 1.559 1.675v2.915h-.889v-2.692c0-.724-.314-1.084-.972-1.084a1.033 1.033 0 0 0-1.075 1.141v2.635h-.889v-4.503ZM59.094 8.437h.888v6.26h-.888v-6.26ZM61.218 12.444a2.133 2.133 0 1 1 4.247 0 2.134 2.134 0 1 1-4.247 0Zm3.333 0c0-.976-.439-1.546-1.208-1.546-.773 0-1.207.57-1.207 1.546 0 .984.434 1.55 1.207 1.55.77 0 1.208-.57 1.208-1.55ZM66.4 13.424c0-.81.604-1.277 1.675-1.344l1.22-.07v-.389c0-.476-.314-.744-.922-.744-.496 0-.84.182-.938.5h-.86c.09-.773.818-1.27 1.84-1.27 1.128 0 1.765.563 1.765 1.514v3.077h-.856v-.633h-.07c-.29.462-.807.732-1.353.707a1.36 1.36 0 0 1-1.5-1.348Zm2.895-.384v-.377l-1.1.07c-.62.042-.901.253-.901.65 0 .405.352.64.835.64a1.061 1.061 0 0 0 1.166-.983ZM71.348 12.444c0-1.423.731-2.324 1.869-2.324a1.484 1.484 0 0 1 1.38.79h.067V8.437h.889v6.26H74.7v-.71h-.07a1.563 1.563 0 0 1-1.414.785c-1.146 0-1.87-.901-1.87-2.328Zm.918 0c0 .955.45 1.53 1.203 1.53.749 0 1.212-.583 1.212-1.526 0-.938-.468-1.53-1.212-1.53-.748 0-1.203.58-1.203 1.526ZM79.23 12.444a2.133 2.133 0 1 1 4.247 0 2.134 2.134 0 1 1-4.248 0Zm3.332 0c0-.976-.438-1.546-1.207-1.546-.773 0-1.208.57-1.208 1.546 0 .984.435 1.55 1.207 1.55.77 0 1.209-.57 1.209-1.55ZM84.67 10.195h.855v.715h.066c.231-.527.77-.849 1.344-.802a1.464 1.464 0 0 1 1.559 1.675v2.915h-.889v-2.692c0-.724-.315-1.084-.972-1.084a1.033 1.033 0 0 0-1.075 1.141v2.635h-.889v-4.503ZM93.515 9.074v1.141h.976v.749h-.976v2.315c0 .472.195.678.637.678.113 0 .226-.007.339-.02v.74c-.16.029-.322.044-.484.046-.988 0-1.382-.348-1.382-1.216v-2.543h-.714v-.749h.715V9.074h.89ZM95.705 8.437h.88v2.482h.07a1.386 1.386 0 0 1 1.374-.807 1.484 1.484 0 0 1 1.55 1.678v2.908h-.889V12.01c0-.72-.335-1.084-.963-1.084a1.052 1.052 0 0 0-1.134 1.142v2.63h-.888V8.437ZM104.761 13.482a1.828 1.828 0 0 1-1.951 1.303 2.047 2.047 0 0 1-2.08-2.325 2.078 2.078 0 0 1 2.076-2.352c1.253 0 2.009.856 2.009 2.27v.31h-3.18v.05a1.189 1.189 0 0 0 1.199 1.29 1.08 1.08 0 0 0 1.072-.546h.855Zm-3.126-1.451h2.275a1.09 1.09 0 0 0-1.109-1.167 1.153 1.153 0 0 0-1.166 1.167ZM24.997 19.89a5.146 5.146 0 0 1 2.45-4.318 5.268 5.268 0 0 0-4.15-2.244c-1.746-.183-3.44 1.045-4.33 1.045-.906 0-2.276-1.027-3.752-.997a5.528 5.528 0 0 0-4.651 2.837c-2.011 3.482-.511 8.6 1.416 11.414.963 1.378 2.09 2.918 3.564 2.863 1.442-.06 1.981-.92 3.722-.92 1.725 0 2.23.92 3.734.886 1.548-.026 2.523-1.385 3.453-2.776a11.397 11.397 0 0 0 1.58-3.216 4.973 4.973 0 0 1-3.036-4.575ZM22.156 11.477a5.067 5.067 0 0 0 1.16-3.63 5.155 5.155 0 0 0-3.336 1.726 4.821 4.821 0 0 0-1.19 3.495 4.262 4.262 0 0 0 3.366-1.591Z"
          fill="#000"
        />
      </g>
      <defs>
        <clipPath id="a">
          <path fill="#fff" d="M0 0h157v40H0z" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default MacAppStoreBlack;

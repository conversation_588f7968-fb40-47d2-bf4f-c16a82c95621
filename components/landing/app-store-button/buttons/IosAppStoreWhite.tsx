import React from 'react';

export const IosAppStoreWhite = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      className="h-full w-auto"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 120 40"
      {...props}
    >
      <rect x=".5" y=".5" width="119" height="39" rx="6.5" fill="#0C0D10" />
      <rect x=".5" y=".5" width="119" height="39" rx="6.5" stroke="#A6A6A6" />
      <path
        d="M81.526 19.2v2.292H80.09v1.502h1.436V28.1c0 1.744.788 2.44 2.772 2.44.349 0 .681-.04.971-.09v-1.486c-.249.024-.406.041-.68.041-.888 0-1.278-.415-1.278-1.361v-4.649h1.958v-1.502H83.31V19.2h-1.784ZM90.323 30.664c2.64 0 4.259-1.768 4.259-4.698 0-2.914-1.627-4.69-4.259-4.69-2.64 0-4.266 1.776-4.266 4.69 0 2.93 1.618 4.698 4.266 4.698Zm0-1.585c-1.552 0-2.423-1.137-2.423-3.113 0-1.959.871-3.104 2.423-3.104 1.544 0 2.424 1.145 2.424 3.104 0 1.967-.88 3.113-2.424 3.113ZM95.967 30.49h1.784v-5.337c0-1.27.955-2.125 2.308-2.125.315 0 .847.058.996.108v-1.76c-.191-.05-.531-.075-.797-.075-1.179 0-2.183.648-2.44 1.536h-.133V21.45h-1.718v9.04ZM105.486 22.795c1.32 0 2.183.921 2.225 2.341h-4.566c.1-1.411 1.021-2.34 2.341-2.34Zm2.217 5.255c-.332.705-1.071 1.095-2.15 1.095-1.428 0-2.349-1.004-2.408-2.59v-.1h6.384v-.622c0-2.839-1.519-4.557-4.034-4.557-2.549 0-4.167 1.835-4.167 4.723 0 2.889 1.585 4.665 4.175 4.665 2.067 0 3.511-.996 3.918-2.614h-1.718ZM69.822 27.152c.138 2.22 1.988 3.64 4.74 3.64 2.943 0 4.784-1.49 4.784-3.864 0-1.867-1.05-2.9-3.596-3.493l-1.368-.335c-1.617-.379-2.271-.887-2.271-1.773 0-1.118 1.015-1.85 2.538-1.85 1.445 0 2.443.715 2.624 1.859h1.876c-.112-2.09-1.954-3.562-4.474-3.562-2.71 0-4.517 1.471-4.517 3.682 0 1.824 1.024 2.908 3.27 3.433l1.6.387c1.643.387 2.365.955 2.365 1.902 0 1.1-1.135 1.901-2.684 1.901-1.66 0-2.813-.748-2.977-1.927h-1.91ZM51.335 21.301c-1.229 0-2.291.614-2.839 1.644h-.133V21.45h-1.718v12.045h1.785V29.12h.14c.474.955 1.495 1.52 2.782 1.52 2.282 0 3.735-1.802 3.735-4.674 0-2.872-1.453-4.665-3.752-4.665Zm-.506 7.736c-1.495 0-2.432-1.178-2.432-3.063 0-1.892.938-3.07 2.44-3.07 1.51 0 2.415 1.153 2.415 3.062 0 1.918-.904 3.071-2.423 3.071ZM61.332 21.301c-1.229 0-2.291.614-2.84 1.644h-.132V21.45h-1.718v12.045h1.784V29.12h.142c.473.955 1.494 1.52 2.78 1.52 2.283 0 3.736-1.802 3.736-4.674 0-2.872-1.453-4.665-3.752-4.665Zm-.507 7.736c-1.494 0-2.432-1.178-2.432-3.063 0-1.892.938-3.07 2.44-3.07 1.511 0 2.416 1.153 2.416 3.062 0 1.918-.905 3.071-2.424 3.071ZM43.443 30.49h2.048l-4.483-12.415h-2.073L34.452 30.49h1.98l1.143-3.295h4.732l1.136 3.295Zm-3.57-10.16h.146l1.798 5.247h-3.751l1.806-5.248ZM35.651 8.71v5.99h2.162c1.785 0 2.819-1.1 2.819-3.013 0-1.885-1.042-2.976-2.819-2.976h-2.162Zm.93.848h1.129c1.24 0 1.975.788 1.975 2.141 0 1.374-.722 2.154-1.975 2.154h-1.13V9.558ZM43.797 14.787c1.32 0 2.129-.884 2.129-2.349 0-1.457-.814-2.345-2.13-2.345-1.32 0-2.133.888-2.133 2.345 0 1.465.81 2.35 2.134 2.35Zm0-.793c-.776 0-1.212-.568-1.212-1.556 0-.98.436-1.552 1.212-1.552.772 0 1.212.573 1.212 1.552 0 .984-.44 1.556-1.212 1.556ZM52.818 10.18h-.892l-.806 3.45h-.07l-.93-3.45h-.855l-.93 3.45h-.066l-.81-3.45h-.904L47.8 14.7h.917l.93-3.329h.07l.934 3.329h.926l1.241-4.52ZM53.846 14.7h.892v-2.644c0-.705.42-1.145 1.079-1.145.66 0 .975.36.975 1.087V14.7h.893v-2.926c0-1.075-.556-1.681-1.565-1.681-.68 0-1.129.303-1.349.805h-.066v-.718h-.86v4.52ZM59.09 14.7h.892V8.416h-.892V14.7ZM63.338 14.787c1.32 0 2.13-.884 2.13-2.349 0-1.457-.814-2.345-2.13-2.345-1.32 0-2.133.888-2.133 2.345 0 1.465.81 2.35 2.133 2.35Zm0-.793c-.776 0-1.212-.568-1.212-1.556 0-.98.436-1.552 1.212-1.552.772 0 1.212.573 1.212 1.552 0 .984-.44 1.556-1.212 1.556ZM68.126 14.023c-.485 0-.838-.236-.838-.643 0-.398.282-.61.905-.652l1.104-.07v.378c0 .56-.498.987-1.17.987Zm-.228.752c.594 0 1.087-.258 1.357-.71h.07v.635h.86v-3.088c0-.954-.64-1.519-1.772-1.519-1.025 0-1.756.498-1.847 1.274h.863c.1-.32.444-.502.942-.502.61 0 .926.27.926.747v.39l-1.225.07c-1.075.067-1.68.536-1.68 1.35 0 .826.634 1.353 1.506 1.353ZM73.213 14.775c.623 0 1.15-.295 1.42-.789h.07v.714h.855V8.416h-.892v2.482h-.067c-.245-.498-.768-.793-1.386-.793-1.141 0-1.876.905-1.876 2.333 0 1.432.726 2.337 1.876 2.337Zm.253-3.869c.747 0 1.216.594 1.216 1.536 0 .946-.465 1.532-1.216 1.532-.755 0-1.208-.577-1.208-1.536 0-.95.457-1.532 1.208-1.532ZM81.344 14.787c1.32 0 2.13-.884 2.13-2.349 0-1.457-.814-2.345-2.13-2.345-1.32 0-2.133.888-2.133 2.345 0 1.465.81 2.35 2.133 2.35Zm0-.793c-.776 0-1.212-.568-1.212-1.556 0-.98.436-1.552 1.212-1.552.772 0 1.212.573 1.212 1.552 0 .984-.44 1.556-1.212 1.556ZM84.655 14.7h.892v-2.644c0-.705.42-1.145 1.08-1.145.66 0 .975.36.975 1.087V14.7h.892v-2.926c0-1.075-.556-1.681-1.565-1.681-.68 0-1.129.303-1.349.805h-.066v-.718h-.86v4.52ZM92.604 9.055v1.146h-.718v.751h.718v2.553c0 .871.394 1.22 1.386 1.22.174 0 .34-.02.485-.046v-.743c-.124.013-.203.021-.34.021-.444 0-.64-.208-.64-.68v-2.325h.98v-.751h-.98V9.055h-.891ZM95.673 14.7h.893v-2.64c0-.684.406-1.145 1.137-1.145.63 0 .967.365.967 1.087V14.7h.892v-2.918c0-1.075-.593-1.685-1.556-1.685-.68 0-1.158.303-1.378.81h-.07v-2.49h-.885V14.7ZM102.781 10.852c.66 0 1.092.461 1.113 1.17h-2.283c.05-.705.51-1.17 1.17-1.17Zm1.108 2.628c-.166.352-.535.548-1.075.548-.713 0-1.174-.503-1.203-1.295v-.05h3.191v-.311c0-1.42-.759-2.279-2.017-2.279-1.274 0-2.083.917-2.083 2.362 0 1.444.793 2.332 2.087 2.332 1.034 0 1.756-.498 1.959-1.307h-.859ZM24.769 20.3a4.989 4.989 0 0 1 2.357-4.151 5.128 5.128 0 0 0-3.992-2.158c-1.679-.176-3.307 1.005-4.163 1.005-.872 0-2.19-.987-3.608-.958a5.359 5.359 0 0 0-4.473 2.728c-1.934 3.348-.491 8.27 1.361 10.976.927 1.325 2.01 2.805 3.428 2.753 1.387-.058 1.905-.885 3.58-.885 1.658 0 2.144.885 3.59.852 1.489-.024 2.426-1.332 3.32-2.67a10.96 10.96 0 0 0 1.52-3.092 4.824 4.824 0 0 1-2.92-4.4ZM22.037 12.211a4.872 4.872 0 0 0 1.115-3.49 4.957 4.957 0 0 0-3.208 1.66 4.678 4.678 0 0 0-1.143 3.36 4.148 4.148 0 0 0 3.236-1.53Z"
        fill="#fff"
      />
    </svg>
  );
};

export default IosAppStoreWhite;

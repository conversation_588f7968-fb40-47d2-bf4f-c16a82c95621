import React from 'react';

export const MacAppStoreWhite = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      className="h-full w-auto"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 157 40"
      {...props}
    >
      <g clipPath="url(#a)">
        <path
          d="M146.571 0H9.535c-.367 0-.73 0-1.095.002-.306.002-.61.008-.919.013C6.85.023 6.18.082 5.517.192a6.665 6.665 0 0 0-1.9.627A6.438 6.438 0 0 0 .193 5.522a12.993 12.993 0 0 0-.179 2.002c-.01.306-.01.614-.015.92V31.56c.005.31.006.611.015.922.008.67.068 1.34.18 2.002.11.663.32 1.305.624 1.904.303.598.701 1.143 1.179 1.614.473.477 1.019.875 1.618 1.179a6.7 6.7 0 0 0 1.901.63c.663.11 1.333.169 ************.007.613.011.919.011.366.002.728.002 1.095.002H146.57c.36 0 .725 0 1.084-.002.305 0 .617-.004.922-.01.67-.009 1.339-.068 2-.177a6.794 6.794 0 0 0 1.908-.631A6.281 6.281 0 0 0 154.103 38a6.4 6.4 0 0 0 1.181-1.614 6.59 6.59 0 0 0 .619-1.904c.112-.662.174-1.331.186-2.002.004-.311.004-.612.004-.922.008-.364.008-.725.008-1.094V9.536c0-.366 0-.73-.008-1.092 0-.306 0-.614-.004-.92a13.437 13.437 0 0 0-.186-2.002 6.587 6.587 0 0 0-.619-1.904 6.464 6.464 0 0 0-2.799-2.8 6.76 6.76 0 0 0-1.908-.627c-.661-.11-1.33-.169-2-.176-.305-.005-.617-.011-.922-.013C147.296 0 146.931 0 146.571 0Z"
          fill="#000"
        />
        <path
          d="M46.149 30.496v-9.14h-.06l-3.744 9.045h-1.428l-3.753-9.044h-.06v9.14h-1.756V18.077h2.23l4.018 9.802h.069l4.01-9.802h2.238v12.418h-1.764ZM49.396 27.923c0-1.583 1.213-2.54 3.365-2.668l2.479-.138v-.688c0-1.007-.663-1.575-1.791-1.575a1.73 1.73 0 0 0-1.902 1.274H49.81c.052-1.636 1.575-2.797 3.692-2.797 2.16 0 3.588 1.179 3.588 2.96v6.205h-1.78v-1.49h-.044a3.237 3.237 0 0 1-2.857 1.645 2.745 2.745 0 0 1-3.012-2.728Zm5.844-.817v-.698l-2.23.138c-1.11.07-1.738.55-1.738 1.325 0 .792.654 1.309 1.652 1.309a2.17 2.17 0 0 0 2.316-2.074ZM64.893 24.558a2 2 0 0 0-2.134-1.67c-1.428 0-2.375 1.197-2.375 3.082 0 1.928.955 3.089 2.392 3.089a1.948 1.948 0 0 0 2.117-1.626h1.79a3.618 3.618 0 0 1-3.924 3.175c-2.582 0-4.268-1.764-4.268-4.638 0-2.815 1.686-4.639 4.25-4.639a3.64 3.64 0 0 1 3.925 3.227h-1.773ZM78.76 27.14h-4.734l-1.137 3.356h-2.005l4.484-12.418h2.083l4.483 12.418h-2.039l-1.136-3.356Zm-4.244-1.55h3.752l-1.85-5.446h-.051l-1.85 5.447ZM91.617 25.97c0 2.813-1.506 4.62-3.779 4.62a3.07 3.07 0 0 1-2.848-1.583h-.043v4.484h-1.859V21.442h1.8v1.506h.033a3.211 3.211 0 0 1 2.883-1.6c2.298 0 3.813 1.816 3.813 4.622Zm-1.91 0c0-1.833-.948-3.038-2.393-3.038-1.42 0-2.375 1.23-2.375 3.038 0 1.824.955 3.046 2.375 3.046 1.445 0 2.393-1.196 2.393-3.046ZM101.582 25.97c0 2.813-1.506 4.62-3.779 4.62a3.069 3.069 0 0 1-2.848-1.583h-.043v4.484h-1.859V21.442h1.799v1.506h.034a3.211 3.211 0 0 1 2.883-1.6c2.298 0 3.813 1.816 3.813 4.622Zm-1.91 0c0-1.833-.948-3.038-2.393-3.038-1.42 0-2.375 1.23-2.375 3.038 0 1.824.955 3.046 2.375 3.046 1.445 0 2.392-1.196 2.392-3.046ZM108.167 27.036c.138 1.232 1.334 2.04 2.969 2.04 1.567 0 2.694-.808 2.694-1.919 0-.964-.68-1.54-2.289-1.936l-1.61-.388c-2.28-.55-3.339-1.617-3.339-3.348 0-2.142 1.867-3.614 4.519-3.614 2.624 0 4.423 1.472 4.483 3.614h-1.876c-.112-1.239-1.136-1.987-2.634-1.987-1.497 0-2.521.757-2.521 1.858 0 .878.654 1.395 2.255 1.79l1.368.337c2.548.602 3.606 1.625 3.606 3.442 0 2.323-1.85 3.778-4.793 3.778-2.754 0-4.614-1.42-4.734-3.667h1.902ZM119.803 19.3v2.142h1.722v1.472h-1.722v4.991c0 .776.345 1.137 1.102 1.137a5.79 5.79 0 0 0 .611-.043v1.463c-.34.064-.686.092-1.032.086-1.833 0-2.548-.688-2.548-2.444v-5.19h-1.316v-1.472h1.316V19.3h1.867ZM122.521 25.97c0-2.849 1.678-4.639 4.294-4.639 2.625 0 4.295 1.79 4.295 4.639 0 2.856-1.661 4.639-4.295 4.639-2.633 0-4.294-1.783-4.294-4.64Zm6.695 0c0-1.954-.895-3.108-2.401-3.108-1.506 0-2.401 1.163-2.401 3.108 0 1.962.895 3.106 2.401 3.106 1.506 0 2.401-1.144 2.401-3.106ZM132.643 21.442h1.773v1.541h.043a2.159 2.159 0 0 1 2.177-1.635c.214-.001.428.022.637.07v1.737a2.594 2.594 0 0 0-.835-.112 1.872 1.872 0 0 0-1.937 2.083v5.37h-1.858v-9.053ZM145.84 27.837c-.25 1.644-1.85 2.771-3.898 2.771-2.634 0-4.269-1.764-4.269-4.595 0-2.84 1.644-4.682 4.191-4.682 2.505 0 4.08 1.72 4.08 4.466v.637h-6.395v.112a2.36 2.36 0 0 0 2.436 2.564 2.049 2.049 0 0 0 2.091-1.273h1.764Zm-6.282-2.702h4.526a2.174 2.174 0 0 0-2.22-2.298 2.294 2.294 0 0 0-2.306 2.298ZM37.826 9.731a2.64 2.64 0 0 1 2.808 2.965c0 1.906-1.03 3.002-2.808 3.002h-2.155V9.73h2.155Zm-1.228 5.123h1.125a1.876 1.876 0 0 0 1.967-2.146 1.88 1.88 0 0 0-1.967-2.134h-1.125v4.28ZM41.68 13.444a2.133 2.133 0 1 1 4.248 0 2.133 2.133 0 1 1-4.247 0Zm3.334 0c0-.976-.439-1.546-1.208-1.546-.773 0-1.207.57-1.207 1.546 0 .984.434 1.55 1.207 1.55.77 0 1.208-.57 1.208-1.55ZM51.573 15.698h-.922l-.93-3.317h-.07l-.927 3.317h-.913l-1.242-4.503h.902l.806 3.436h.067l.925-3.436h.853l.926 3.436h.07l.803-3.436h.889l-1.237 4.503ZM53.853 11.195h.856v.715h.066c.231-.527.771-.849 1.344-.802a1.465 1.465 0 0 1 1.559 1.675v2.915h-.889v-2.692c0-.724-.314-1.084-.972-1.084a1.033 1.033 0 0 0-1.075 1.141v2.635h-.889v-4.503ZM59.094 9.437h.888v6.26h-.888v-6.26ZM61.218 13.444a2.133 2.133 0 1 1 4.247 0 2.134 2.134 0 1 1-4.247 0Zm3.333 0c0-.976-.439-1.546-1.208-1.546-.773 0-1.207.57-1.207 1.546 0 .984.434 1.55 1.207 1.55.77 0 1.208-.57 1.208-1.55ZM66.4 14.424c0-.81.604-1.277 1.675-1.344l1.22-.07v-.389c0-.476-.314-.744-.922-.744-.496 0-.84.182-.938.5h-.86c.09-.773.818-1.27 1.84-1.27 1.128 0 1.765.563 1.765 1.514v3.077h-.856v-.633h-.07c-.29.462-.807.732-1.353.707a1.36 1.36 0 0 1-1.5-1.348Zm2.895-.384v-.377l-1.1.07c-.62.042-.901.253-.901.65 0 .405.352.64.835.64a1.061 1.061 0 0 0 1.166-.983ZM71.348 13.444c0-1.423.731-2.324 1.869-2.324a1.484 1.484 0 0 1 1.38.79h.067V9.437h.889v6.26H74.7v-.71h-.07a1.563 1.563 0 0 1-1.414.785c-1.146 0-1.87-.901-1.87-2.328Zm.918 0c0 .955.45 1.53 1.203 1.53.749 0 1.212-.583 1.212-1.526 0-.938-.468-1.53-1.212-1.53-.748 0-1.203.58-1.203 1.526ZM79.23 13.444a2.133 2.133 0 1 1 4.247 0 2.134 2.134 0 1 1-4.248 0Zm3.332 0c0-.976-.438-1.546-1.207-1.546-.773 0-1.208.57-1.208 1.546 0 .984.435 1.55 1.207 1.55.77 0 1.209-.57 1.209-1.55ZM84.67 11.195h.855v.715h.066c.231-.527.77-.849 1.344-.802a1.464 1.464 0 0 1 1.559 1.675v2.915h-.889v-2.692c0-.724-.315-1.084-.972-1.084a1.033 1.033 0 0 0-1.075 1.141v2.635h-.889v-4.503ZM93.515 10.074v1.141h.976v.749h-.976v2.315c0 .472.195.678.637.678.113 0 .226-.007.339-.02v.74c-.16.029-.322.044-.484.046-.988 0-1.382-.348-1.382-1.216v-2.543h-.714v-.749h.715v-1.141h.89ZM95.705 9.437h.88v2.482h.07a1.386 1.386 0 0 1 1.374-.807 1.484 1.484 0 0 1 1.55 1.678v2.908h-.889V13.01c0-.72-.335-1.084-.963-1.084a1.052 1.052 0 0 0-1.134 1.142v2.63h-.888V9.437ZM104.761 14.482a1.828 1.828 0 0 1-1.951 1.303 2.047 2.047 0 0 1-2.08-2.325 2.078 2.078 0 0 1 2.076-2.352c1.253 0 2.009.856 2.009 2.27v.31h-3.18v.05a1.189 1.189 0 0 0 1.199 1.29 1.08 1.08 0 0 0 1.072-.546h.855Zm-3.126-1.451h2.275a1.09 1.09 0 0 0-1.109-1.167 1.153 1.153 0 0 0-1.166 1.167ZM24.769 20.3a4.948 4.948 0 0 1 2.356-4.151 5.065 5.065 0 0 0-3.99-2.158c-1.68-.176-3.308 1.005-4.164 1.005-.872 0-2.19-.987-3.608-.958a5.315 5.315 0 0 0-4.473 2.728c-1.934 3.348-.492 8.27 1.361 10.976.927 1.325 2.01 2.805 3.428 2.753 1.387-.058 1.905-.885 3.58-.885 1.658 0 2.144.885 3.59.852 1.489-.024 2.426-1.332 3.32-2.67a10.963 10.963 0 0 0 1.52-3.092 4.782 4.782 0 0 1-2.92-4.4ZM22.037 12.211a4.872 4.872 0 0 0 1.115-3.49 4.957 4.957 0 0 0-3.208 1.659 4.636 4.636 0 0 0-1.143 3.361 4.099 4.099 0 0 0 3.236-1.53Z"
          fill="#fff"
        />
      </g>
      <defs>
        <clipPath id="a">
          <path fill="#fff" d="M0 0h157v40H0z" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default MacAppStoreWhite;

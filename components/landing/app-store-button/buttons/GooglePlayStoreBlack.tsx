import React from 'react';

export const GooglePlayStoreBlack = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      className="h-full w-auto"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 135 40"
      {...props}
    >
      <rect x=".5" y=".5" width="134" height="39" rx="4.5" fill="#fff" />
      <rect x=".5" y=".5" width="134" height="39" rx="4.5" stroke="#000" />
      <path
        d="M68.136 21.751c-2.352 0-4.269 1.79-4.269 4.253 0 2.45 1.917 4.253 4.269 4.253 2.353 0 4.27-1.804 4.27-4.253-.001-2.464-1.918-4.253-4.27-4.253Zm0 6.832c-1.289 0-2.4-1.063-2.4-2.578 0-1.53 1.112-2.578 2.4-2.578 1.289 0 2.4 1.047 2.4 2.578 0 1.514-1.111 2.578-2.4 2.578Zm-9.314-6.832c-2.352 0-4.269 1.79-4.269 4.253 0 2.45 1.917 4.253 4.269 4.253 2.353 0 4.27-1.804 4.27-4.253 0-2.464-1.917-4.253-4.27-4.253Zm0 6.832c-1.289 0-2.4-1.063-2.4-2.578 0-1.53 1.112-2.578 2.4-2.578 1.289 0 2.4 1.047 2.4 2.578.001 1.514-1.111 2.578-2.4 2.578Zm-11.078-5.526v1.804h4.318c-.129 1.015-.467 1.756-.983 2.271-.628.628-1.611 1.321-3.335 1.321-2.658 0-4.736-2.143-4.736-4.8 0-2.659 2.078-4.802 4.736-4.802 1.434 0 2.481.564 3.254 1.29l1.273-1.274c-1.08-1.03-2.513-1.82-4.527-1.82-3.641 0-6.702 2.964-6.702 6.605 0 3.641 3.061 6.605 6.702 6.605 1.965 0 3.448-.645 4.607-1.853 1.192-1.192 1.563-2.868 1.563-4.22 0-.419-.032-.806-.097-1.128h-6.073v.001Zm45.308 1.401c-.354-.95-1.434-2.707-3.641-2.707-2.191 0-4.012 1.724-4.012 4.253 0 2.384 1.805 4.253 4.221 4.253 1.949 0 3.077-1.192 3.545-1.885l-1.45-.967c-.483.71-1.144 1.176-2.095 1.176-.95 0-1.627-.435-2.062-1.289l5.687-2.352-.193-.482Zm-5.8 1.418c-.048-1.644 1.273-2.48 2.224-2.48.741 0 1.369.37 1.579.901l-3.803 1.58ZM82.629 30h1.868V17.5h-1.868V30Zm-3.062-7.298h-.064c-.419-.5-1.225-.95-2.239-.95-2.127 0-4.076 1.868-4.076 4.27 0 2.383 1.949 4.236 4.076 4.236 1.015 0 1.82-.45 2.239-.966h.064v.612c0 1.627-.87 2.497-2.271 2.497-1.144 0-1.853-.82-2.143-1.514l-1.627.677c.467 1.127 1.707 2.513 3.77 2.513 2.191 0 4.044-1.289 4.044-4.43V22.01h-1.772v.692h-.001Zm-2.142 5.881c-1.289 0-2.368-1.08-2.368-2.562 0-1.499 1.079-2.594 2.368-2.594 1.272 0 2.271 1.095 2.271 2.594 0 1.482-.999 2.562-2.271 2.562ZM101.806 17.5h-4.471V30H99.2v-4.736h2.605c2.068 0 4.102-1.497 4.102-3.882s-2.033-3.883-4.101-3.883Zm.048 6.025H99.2V19.24h2.654c1.395 0 2.187 1.155 2.187 2.143 0 .968-.792 2.142-2.187 2.142Zm11.532-1.795c-1.351 0-2.75.595-3.329 1.914l1.656.691c.354-.69 1.014-.917 1.705-.917.965 0 1.946.58 1.962 1.608v.13c-.338-.194-1.062-.483-1.946-.483-1.785 0-3.603.981-3.603 2.814 0 1.673 1.464 2.75 3.104 2.75 1.254 0 1.946-.563 2.38-1.223h.064v.965h1.802v-4.793c.001-2.218-1.657-3.456-3.795-3.456Zm-.226 6.851c-.61 0-1.463-.306-1.463-1.062 0-.965 1.062-1.335 1.979-1.335.819 0 1.206.177 1.704.418a2.262 2.262 0 0 1-2.22 1.98Zm10.583-6.578-2.139 5.42h-.064l-2.22-5.42h-2.01l3.329 7.575-1.898 4.214h1.946l5.131-11.789h-2.075ZM106.937 30h1.865V17.5h-1.865V30ZM47.418 10.243c0 .838-.248 1.505-.745 2.003-.564.592-1.3.888-2.204.888-.866 0-1.603-.3-2.208-.9-.606-.601-.909-1.345-.909-2.233 0-.89.303-1.633.91-2.233.604-.601 1.341-.901 2.207-.901.43 0 .841.084 1.231.25.391.169.704.392.938.67l-.527.529c-.397-.475-.944-.712-1.643-.712-.632 0-1.178.222-1.639.666-.461.444-.69 1.02-.69 1.73 0 .709.229 1.286.69 1.73a2.282 2.282 0 0 0 1.639.666c.67 0 1.23-.223 1.676-.67.29-.291.458-.696.503-1.215h-2.179V9.79h2.907c.03.157.043.308.043.453ZM52.028 7.737h-2.732v1.902h2.464v.721h-2.464v1.902h2.732V13h-3.503V7h3.503v.737ZM55.279 13h-.771V7.737h-1.676V7h4.123v.737h-1.676V13ZM59.938 13V7h.771v6h-.771ZM64.128 13h-.77V7.737H61.68V7h4.123v.737h-1.676V13ZM73.609 12.225c-.59.606-1.323.909-2.2.909-.877 0-1.61-.303-2.2-.909-.59-.606-.883-1.348-.883-2.225s.294-1.619.884-2.225c.589-.606 1.322-.91 2.199-.91.872 0 1.604.305 2.196.914.592.609.888 1.349.888 2.221 0 .877-.295 1.619-.884 2.225Zm-3.83-.503a2.2 2.2 0 0 0 1.63.674c.643 0 1.187-.225 1.63-.674.444-.45.667-1.024.667-1.722s-.223-1.272-.667-1.722a2.199 2.199 0 0 0-1.63-.674c-.643 0-1.186.225-1.63.674-.443.45-.666 1.024-.666 1.722s.223 1.272.666 1.722ZM75.575 13V7h.938l2.916 4.667h.033l-.033-1.156V7h.77v6h-.804l-3.051-4.894h-.033l.033 1.156V13h-.77Z"
        fill="#000"
      />
      <path
        d="M47.418 10.243c0 .838-.248 1.505-.745 2.003-.564.592-1.3.888-2.204.888-.866 0-1.603-.3-2.208-.9-.606-.601-.909-1.345-.909-2.233 0-.89.303-1.633.91-2.233.604-.601 1.341-.901 2.207-.901.43 0 .841.084 1.231.25.391.169.704.392.938.67l-.527.529c-.397-.475-.944-.712-1.643-.712-.632 0-1.178.222-1.639.666-.461.444-.69 1.02-.69 1.73 0 .709.229 1.286.69 1.73a2.282 2.282 0 0 0 1.639.666c.67 0 1.23-.223 1.676-.67.29-.291.458-.696.503-1.215h-2.179V9.79h2.907c.03.157.043.308.043.453ZM52.028 7.737h-2.732v1.902h2.464v.721h-2.464v1.902h2.732V13h-3.503V7h3.503v.737ZM55.279 13h-.771V7.737h-1.676V7h4.123v.737h-1.676V13ZM59.938 13V7h.771v6h-.771ZM64.128 13h-.77V7.737H61.68V7h4.123v.737h-1.676V13ZM73.609 12.225c-.59.606-1.323.909-2.2.909-.877 0-1.61-.303-2.2-.909-.59-.606-.883-1.348-.883-2.225s.294-1.619.884-2.225c.589-.606 1.322-.91 2.199-.91.872 0 1.604.305 2.196.914.592.609.888 1.349.888 2.221 0 .877-.295 1.619-.884 2.225Zm-3.83-.503a2.2 2.2 0 0 0 1.63.674c.643 0 1.187-.225 1.63-.674.444-.45.667-1.024.667-1.722s-.223-1.272-.667-1.722a2.199 2.199 0 0 0-1.63-.674c-.643 0-1.186.225-1.63.674-.443.45-.666 1.024-.666 1.722s.223 1.272.666 1.722ZM75.575 13V7h.938l2.916 4.667h.033l-.033-1.156V7h.77v6h-.804l-3.051-4.894h-.033l.033 1.156V13h-.77Z"
        stroke="#000"
        strokeWidth=".2"
        strokeMiterlimit="10"
      />
      <path
        d="M25.874 25.786 14.34 32.444l-.034.02a2.866 2.866 0 0 1-1.46.396c-1.12 0-2.104-.68-2.577-1.63l.085-.085 10.44-10.438 5.08 5.08ZM20.086 20 9.973 30.11a2.753 2.753 0 0 1-.003-.127V10.017c0-.043 0-.086.003-.128L20.086 20Zm11.39-2.508.01.007a2.878 2.878 0 0 1-.018 5.06l-4.697 2.71L21.5 20l5.248-5.249 4.727 2.741ZM12.846 7.14c.544 0 1.052.15 1.487.41l11.522 6.682-5.062 5.061-10.44-10.438-.084-.084c.473-.95 1.456-1.631 2.578-1.631Z"
        fill="#000"
      />
    </svg>
  );
};

export default GooglePlayStoreBlack;

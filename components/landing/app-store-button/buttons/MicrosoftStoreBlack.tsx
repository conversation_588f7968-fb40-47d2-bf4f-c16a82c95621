import React from 'react';

export const MicrosoftStoreBlack = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      className="h-full w-auto"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 111 40"
      {...props}
    >
      <path fill="#fff" d="M.5.5h110v39H.5z" />
      <path stroke="#000" d="M.5.5h110v39H.5z" />
      <path
        d="M50.414 31H48.57v-7.25c0-.594.037-1.32.11-2.18h-.032c-.114.49-.216.841-.304 1.055L45 31h-1.281l-3.352-8.313c-.094-.244-.192-.617-.297-1.117h-.03c.04.448.062 1.18.062 2.196V31h-1.72V19.797H41l2.945 7.46c.224.574.37 1 .438 1.282h.039c.193-.588.349-1.026.469-1.312l3-7.43h2.523V31ZM53.57 21.32c-.296 0-.551-.096-.765-.289a.957.957 0 0 1-.313-.734c0-.297.105-.544.313-.742.214-.198.469-.297.766-.297.307 0 .567.099.78.297a.97.97 0 0 1 .321.742.969.969 0 0 1-.32.726 1.107 1.107 0 0 1-.781.297Zm.9 9.68h-1.813v-8h1.812v8ZM62.25 30.633c-.64.37-1.398.555-2.273.555-1.187 0-2.146-.37-2.875-1.11-.729-.745-1.094-1.708-1.094-2.89 0-1.318.391-2.375 1.172-3.172.787-.802 1.836-1.203 3.149-1.203.729 0 1.372.127 1.93.382v1.688c-.558-.417-1.152-.625-1.782-.625-.765 0-1.393.26-1.883.781-.49.516-.734 1.193-.734 2.031 0 .828.23 1.482.688 1.961.463.48 1.083.719 1.859.719.65 0 1.266-.232 1.844-.695v1.578ZM68.376 24.727c-.219-.172-.534-.258-.945-.258-.537 0-.985.242-1.344.726-.36.485-.54 1.143-.54 1.977V31h-1.812v-8h1.813v1.648h.031c.177-.562.448-1 .813-1.312.37-.318.78-.477 1.234-.477.328 0 .578.05.75.149v1.719ZM72.884 31.188c-1.234 0-2.221-.373-2.96-1.118-.735-.75-1.102-1.742-1.102-2.976 0-1.344.382-2.393 1.148-3.149.77-.755 1.807-1.133 3.11-1.133 1.25 0 2.223.368 2.921 1.102.698.734 1.047 1.753 1.047 3.055 0 1.276-.377 2.3-1.133 3.07-.75.766-1.76 1.148-3.03 1.148Zm.086-6.93c-.708 0-1.268.247-1.68.742-.411.495-.617 1.177-.617 2.047 0 .838.209 1.5.625 1.984.417.48.974.719 1.672.719.713 0 1.26-.237 1.64-.71.386-.475.579-1.15.579-2.024 0-.88-.193-1.56-.578-2.04-.38-.479-.928-.718-1.641-.718ZM78.08 30.75v-1.68c.677.516 1.424.774 2.242.774 1.094 0 1.64-.323 1.64-.969a.747.747 0 0 0-.14-.46 1.301 1.301 0 0 0-.383-.345 2.837 2.837 0 0 0-.562-.265l-.72-.266a7.936 7.936 0 0 1-.89-.414c-.26-.146-.479-.31-.656-.492a1.95 1.95 0 0 1-.39-.633 2.317 2.317 0 0 1-.133-.82c0-.386.09-.724.273-1.016.182-.297.427-.544.734-.742a3.392 3.392 0 0 1 1.047-.453c.39-.104.794-.157 1.211-.157.74 0 1.401.112 1.985.336v1.586c-.563-.385-1.209-.578-1.938-.578-.23 0-.438.024-.625.07a1.603 1.603 0 0 0-.469.196.944.944 0 0 0-.304.305.697.697 0 0 0-.11.382c0 .172.037.318.11.438.073.12.18.226.32.32.146.089.318.172.516.25.203.073.434.154.695.242.344.141.65.284.922.43.276.146.51.313.703.5.192.182.341.396.445.64.104.24.156.527.156.86 0 .406-.093.76-.28 1.063a2.346 2.346 0 0 1-.75.75 3.443 3.443 0 0 1-1.087.437 5.434 5.434 0 0 1-1.289.148c-.875 0-1.633-.145-2.273-.437ZM88.713 31.188c-1.235 0-2.221-.373-2.961-1.118-.734-.75-1.102-1.742-1.102-2.976 0-1.344.383-2.393 1.149-3.149.77-.755 1.807-1.133 3.11-1.133 1.25 0 2.223.368 2.921 1.102.698.734 1.047 1.753 1.047 3.055 0 1.276-.378 2.3-1.133 3.07-.75.766-1.76 1.148-3.031 1.148Zm.086-6.93c-.709 0-1.269.247-1.68.742-.411.495-.617 1.177-.617 2.047 0 .838.208 1.5.625 1.984.416.48.974.719 1.672.719.713 0 1.26-.237 1.64-.71.386-.475.579-1.15.579-2.024 0-.88-.193-1.56-.579-2.04-.38-.479-.927-.718-1.64-.718ZM98.932 20.633a1.648 1.648 0 0 0-.836-.211c-.88 0-1.32.497-1.32 1.492V23h1.859v1.422h-1.852V31h-1.812v-6.578h-1.367V23h1.367v-1.297c0-.844.276-1.508.828-1.992.552-.49 1.242-.734 2.07-.734.448 0 .802.049 1.063.148v1.508ZM103.823 30.914c-.354.177-.82.266-1.399.266-1.552 0-2.328-.745-2.328-2.235v-4.523H98.76V23h1.336v-1.852l1.813-.515V23h1.914v1.422h-1.914v4c0 .474.086.812.258 1.015.171.204.458.305.859.305.307 0 .573-.088.797-.265v1.437ZM43.512 14.545c-.693.378-1.465.566-2.315.566-.983 0-1.78-.306-2.389-.918-.61-.612-.914-1.423-.914-2.43 0-1.03.333-1.875.998-2.534.668-.661 1.515-.992 2.542-.992.739 0 1.36.108 1.864.325v1.136c-.51-.34-1.118-.51-1.823-.51-.708 0-1.29.233-1.744.7-.451.467-.677 1.072-.677 1.814 0 .764.195 1.365.584 1.804.39.436.919.654 1.587.654.457 0 .853-.088 1.187-.264v-1.582h-1.4v-.928h2.5v3.16ZM48.941 12.917h-3.238c.013.44.147.778.404 1.016.26.238.615.357 1.067.357.507 0 .972-.151 1.396-.454v.867c-.433.272-1.005.408-1.716.408-.7 0-1.248-.215-1.647-.644-.396-.433-.594-1.041-.594-1.823 0-.74.218-1.341.654-1.805.44-.467.984-.7 1.633-.7.65 0 1.152.208 1.508.626.355.417.533.997.533 1.74v.412Zm-1.039-.76c-.003-.387-.094-.687-.274-.9-.179-.217-.426-.325-.742-.325-.309 0-.572.113-.788.338-.214.226-.345.522-.395.886h2.2ZM52.566 14.949c-.21.105-.487.158-.83.158-.922 0-1.382-.442-1.382-1.327v-2.686h-.794v-.844h.794v-1.1l1.076-.306v1.406h1.136v.844H51.43v2.375c0 .282.05.483.153.603.102.12.272.181.51.181a.74.74 0 0 0 .473-.158v.854ZM56.843 9.253a.655.655 0 0 1-.455-.172.568.568 0 0 1-.185-.436c0-.176.062-.323.185-.44a.646.646 0 0 1 .455-.177c.182 0 .337.059.464.176.127.118.19.265.19.441 0 .167-.063.31-.19.431a.658.658 0 0 1-.464.177ZM57.376 15H56.3v-4.75h1.076V15ZM61.386 14.949c-.21.105-.487.158-.83.158-.921 0-1.382-.442-1.382-1.327v-2.686h-.794v-.844h.794v-1.1l1.076-.306v1.406h1.136v.844H60.25v2.375c0 .282.05.483.153.603.102.12.272.181.51.181a.74.74 0 0 0 .473-.158v.854ZM67.806 8.844a.978.978 0 0 0-.496-.125c-.523 0-.784.296-.784.886v.645h1.104v.844h-1.1V15h-1.076v-3.906h-.811v-.844h.811v-.77c0-.501.164-.895.492-1.183.328-.29.738-.436 1.23-.436.265 0 .475.03.63.088v.895ZM71.246 11.275c-.13-.102-.317-.153-.562-.153-.318 0-.584.144-.797.431-.214.288-.32.68-.32 1.174V15H68.49v-4.75h1.076v.979h.019c.105-.334.266-.594.482-.78.22-.188.464-.283.733-.283.195 0 .344.03.446.089v1.02ZM74.207 15.111c-.733 0-1.319-.22-1.758-.663-.436-.445-.654-1.034-.654-1.767 0-.798.227-1.421.682-1.87.458-.448 1.073-.672 1.846-.672.742 0 1.32.218 1.735.654.414.436.622 1.04.622 1.813 0 .758-.225 1.366-.673 1.823-.445.455-1.045.682-1.8.682Zm.051-4.114c-.42 0-.753.147-.997.44-.244.294-.366.7-.366 1.216 0 .498.123.89.37 1.178.248.285.579.427.993.427.424 0 .749-.14.974-.422.23-.282.344-.682.344-1.202 0-.522-.115-.926-.344-1.21-.225-.285-.55-.427-.974-.427ZM84.976 15H83.9v-2.588c0-.498-.071-.859-.214-1.081-.139-.223-.375-.334-.71-.334-.28 0-.52.14-.718.422-.195.281-.293.618-.293 1.011V15h-1.08v-2.677c0-.884-.313-1.326-.937-1.326-.291 0-.53.133-.72.399-.185.266-.278.61-.278 1.034V15h-1.076v-4.75h1.076v.752h.019c.343-.576.843-.863 1.498-.863.328 0 .614.09.858.273.248.18.416.416.506.71.352-.655.878-.983 1.577-.983 1.045 0 1.568.644 1.568 1.934V15Z"
        fill="#000"
      />
      <path fill="#F15021" d="M8 8h11v11H8z" />
      <path fill="#00A3EE" d="M8 20h11v11H8z" />
      <path fill="#7EB900" d="M20 8h11v11H20z" />
      <path fill="#FFB800" d="M20 20h11v11H20z" />
    </svg>
  );
};

export default MicrosoftStoreBlack;

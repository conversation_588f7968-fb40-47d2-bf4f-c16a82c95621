export const colorThemes = [
  // 0
  {
    name: 'Easy Slate',
    colors: [
      '#f8fafc',
      '#f1f5f9',
      '#e2e8f0',
      '#cbd5e1',
      '#94a3b8',
      '#64748b',
      '#475569',
      '#334155',
      '#1e293b',
      '#0f172a',
      '#020617',
    ],
    tailwindName: 'slate',
  },

  // 1
  {
    name: 'Gray Concrete',
    colors: [
      '#f9fafb',
      '#f3f4f6',
      '#e5e7eb',
      '#d1d5db',
      '#9ca3af',
      '#6b7280',
      '#4b5563',
      '#374151',
      '#1f2937',
      '#111827',
      '#030712',
    ],
    tailwindName: 'gray',
  },

  // 2
  {
    name: 'Chill Zinc',
    colors: [
      '#f4f4f5',
      '#e4e4e7',
      '#d4d4d8',
      '#a1a1aa',
      '#71717a',
      '#52525b',
      '#3f3f46',
      '#27272a',
      '#18181b',
      '#09090b',
    ],
    tailwindName: 'zinc',
  },

  // 3
  {
    name: '<PERSON><PERSON>ed Neutral',
    colors: [
      '#f5f5f5',
      '#e5e5e5',
      '#d4d4d4',
      '#a3a3a3',
      '#737373',
      '#525252',
      '#404040',
      '#262626',
      '#171717',
      '#0a0a0a',
    ],
    tailwindName: 'neutral',
  },

  // 4
  {
    name: 'Smokey Stone',
    colors: [
      '#fafaf9',
      '#f5f5f4',
      '#e7e5e4',
      '#d6d3d1',
      '#a8a29e',
      '#78716c',
      '#57534e',
      '#44403c',
      '#292524',
      '#1c1917',
      '#0c0a09',
    ],
    tailwindName: 'stone',
  },

  // 5
  {
    name: 'Blood Red',
    colors: [
      '#fef2f2',
      '#fee2e2',
      '#fecaca',
      '#fca5a5',
      '#f87171',
      '#ef4444',
      '#dc2626',
      '#b91c1c',
      '#991b1b',
      '#7f1d1d',
      '#450a0a',
    ],
    tailwindName: 'red',
  },

  // 6
  {
    name: 'Juicy Orange',
    colors: [
      '#fff7ed',
      '#ffedd5',
      '#fed7aa',
      '#fdba74',
      '#fb923c',
      '#f97316',
      '#ea580c',
      '#c2410c',
      '#9a3412',
      '#7c2d12',
      '#431407',
    ],
    tailwindName: 'orange',
  },

  // 7

  {
    name: 'Soft Amber',
    colors: [
      '#fffbeb',
      '#fef3c7',
      '#fde68a',
      '#fcd34d',
      '#fbbf24',
      '#f59e0b',
      '#d97706',
      '#b45309',
      '#92400e',
      '#78350f',
      '#451a03',
    ],
    tailwindName: 'amber',
  },

  // 8
  {
    name: 'Yellow Banana',
    colors: [
      '#fefce8',
      '#fef9c3',
      '#fef08a',
      '#fde047',
      '#facc15',
      '#eab308',
      '#ca8a04',
      '#a16207',
      '#854d0e',
      '#713f12',
      '#422006',
    ],
    tailwindName: 'yellow',
  },

  // 9
  {
    name: 'Sourest Lime',
    colors: [
      '#f7fee7',
      '#ecfccb',
      '#d9f99d',
      '#bef264',
      '#a3e635',
      '#84cc16',
      '#65a30d',
      '#4d7c0f',
      '#3f6212',
      '#365314',
      '#1a2e05',
    ],
    tailwindName: 'lime',
  },

  // 10
  {
    name: 'Apple Green',
    colors: [
      '#f0fdf4',
      '#dcfce7',
      '#bbf7d0',
      '#86efac',
      '#4ade80',
      '#22c55e',
      '#16a34a',
      '#15803d',
      '#166534',
      '#14532d',
      '#052e16',
    ],
    tailwindName: 'green',
  },

  // 11
  {
    name: 'Expensive Emerald',
    colors: [
      '#ecfdf5',
      '#d1fae5',
      '#a7f3d0',
      '#6ee7b7',
      '#34d399',
      '#10b981',
      '#059669',
      '#047857',
      '#065f46',
      '#064e3b',
      '#022c22',
    ],
    tailwindName: 'emerald',
  },

  // 12
  {
    name: 'Minty Teal',
    colors: [
      '#f0fdfa',
      '#ccfbf1',
      '#99f6e4',
      '#5eead4',
      '#2dd4bf',
      '#14b8a6',
      '#0d9488',
      '#0f766e',
      '#115e59',
      '#134e4a',
      '#042f2e',
    ],
    tailwindName: 'teal',
  },

  // 13
  {
    name: 'Ocean Cyan',
    colors: [
      '#ecfeff',
      '#cffafe',
      '#a5f3fc',
      '#67e8f9',
      '#22d3ee',
      '#06b6d4',
      '#0891b2',
      '#0e7490',
      '#155e75',
      '#164e63',
      '#083344',
    ],
    tailwindName: 'cyan',
  },

  // 14
  {
    name: 'Sky Blue',
    colors: [
      '#f0f9ff',
      '#e0f2fe',
      '#bae6fd',
      '#7dd3fc',
      '#38bdf8',
      '#0ea5e9',
      '#0284c7',
      '#0369a1',
      '#075985',
      '#0c4a6e',
      '#082f49',
    ],
    tailwindName: 'sky',
  },

  // 15
  {
    name: 'Clever Blue',
    colors: [
      '#eff6ff',
      '#dbeafe',
      '#bfdbfe',
      '#93c5fd',
      '#60a5fa',
      '#3b82f6',
      '#2563eb',
      '#1d4ed8',
      '#1e40af',
      '#1e3a8a',
      '#172554',
    ],
    tailwindName: 'blue',
  },

  // 16
  {
    name: 'Copycat Indigo',
    colors: [
      '#eef2ff',
      '#e0e7ff',
      '#c7d2fe',
      '#a5b4fc',
      '#818cf8',
      '#6366f1',
      '#4f46e5',
      '#4338ca',
      '#3730a3',
      '#312e81',
      '#1e1b4b',
    ],
    tailwindName: 'indigo',
  },

  // 17
  {
    name: 'Grape Violet',
    colors: [
      '#f5f3ff',
      '#ede9fe',
      '#ddd6fe',
      '#c4b5fd',
      '#a78bfa',
      '#8b5cf6',
      '#7c3aed',
      '#6d28d9',
      '#5b21b6',
      '#4c1d95',
      '#2e1065',
    ],
    tailwindName: 'violet',
  },

  // 18
  {
    name: 'Fancy Purple',
    colors: [
      '#faf5ff',
      '#f3e8ff',
      '#e9d5ff',
      '#d8b4fe',
      '#c084fc',
      '#a855f7',
      '#9333ea',
      '#7e22ce',
      '#6b21a8',
      '#581c87',
      '#3b0764',
    ],
    tailwindName: 'purple',
  },

  // 19
  {
    name: 'Intense Fuchsia',
    colors: [
      '#fdf4ff',
      '#fae8ff',
      '#f5d0fe',
      '#f0abfc',
      '#e879f9',
      '#d946ef',
      '#c026d3',
      '#a21caf',
      '#86198f',
      '#701a75',
      '#4a044e',
    ],
    tailwindName: 'fuchsia',
  },

  // 20
  {
    name: 'Hot Pink',
    colors: [
      '#fdf2f8',
      '#fce7f3',
      '#fbcfe8',
      '#f9a8d4',
      '#f472b6',
      '#ec4899',
      '#db2777',
      '#be185d',
      '#9d174d',
      '#831843',
      '#500724',
    ],
    tailwindName: 'pink',
  },

  // 21
  {
    name: 'Candy Bubblegum',
    colors: [
      '#fecad3',
      '#fbb6c9',
      '#f999b9',
      '#f675a7',
      '#f45196',
      '#e52679',
      '#c7205a',
      '#9c1854',
      '#781647',
      '#57103d',
      '#2a062b',
    ],
    tailwindName: 'rose',
  },

  // GPT Ones

  // 22
  {
    name: 'Soft Peach',
    colors: [
      '#fff8f7',
      '#ffecea',
      '#fdd8d3',
      '#fbc2ba',
      '#f9a09c',
      '#f47e7a',
      '#e05c58',
      '#c24140',
      '#a43437',
      '#882d30',
      '#4f161b',
    ],
    tailwindName: 'orange', // closest
  },

  // 23
  {
    name: 'Yellow Sunshine',
    colors: [
      '#fffbeb',
      '#fffac7',
      '#fff38a',
      '#ffde4d',
      '#ffc615',
      '#fab308',
      '#d98a04',
      '#b46207',
      '#924d0e',
      '#783f12',
      '#451a06',
    ],
    tailwindName: 'yellow', // closest
  },

  // 24
  {
    name: 'Green Forest',
    colors: [
      '#f3fdf4',
      '#dcfce7',
      '#bbf7d0',
      '#86efac',
      '#4ade80',
      '#22c55e',
      '#16a34a',
      '#15803d',
      '#166534',
      '#14532d',
      '#052e16',
    ],
    tailwindName: 'green', // closest
  },

  // 25
  {
    name: 'Denim Cave',
    colors: [
      '#f2f9ef',
      '#e0f2d9',
      '#bce5b2',
      '#7edba5',
      '#3dcfa3',
      '#0db4b9',
      '#0893a4',
      '#0e7490',
      '#155e75',
      '#164e63',
      '#083344',
    ],
    tailwindName: 'teal', // closest
  },

  // 26
  {
    name: 'Midnight Universe',
    colors: [
      '#e0e7df',
      '#c7d2cf',
      '#a5b4b2',
      '#818c89',
      '#636663',
      '#4f4646',
      '#433d3c',
      '#373231',
      '#312b2a',
      '#1e1a1a',
      '#0f0d0d',
    ],
    tailwindName: 'gray', // closest
  },

  // 27
  {
    name: 'Lilac Delight',
    colors: [
      '#f0f1ff',
      '#e1e3ff',
      '#c4c6ff',
      '#a4a6ff',
      '#8588ff',
      '#6769ff',
      '#4f51ff',
      '#3648ff',
      '#2734ff',
      '#1922ff',
      '#0a0cff',
    ],
    tailwindName: 'indigo', // closest
  },

  // 28
  {
    name: 'Dusty Petal',
    colors: [
      '#fff1f2',
      '#ffe4e6',
      '#fecdd3',
      '#fda4af',
      '#fb7185',
      '#f43f5e',
      '#e11d48',
      '#be123c',
      '#9f1239',
      '#881337',
      '#4c0519',
    ],
    tailwindName: 'rose', // closest
  },

  // 29
  {
    name: 'Charcoal Evening',
    colors: [
      '#f8f8f8',
      '#f1f1f1',
      '#e3e3e3',
      '#d4d4d4',
      '#a8a8a8',
      '#7a7a7a',
      '#5c5c5c',
      '#404040',
      '#262626',
      '#1a1a1a',
      '#000000',
    ],
    tailwindName: 'neutral', // closest
  },

  // 30
  {
    name: 'Dark State',
    colors: [
      '#f2f2f2',
      '#e5e7eb',
      '#d1d5db',
      '#bdbdbd',
      '#878d91',
      '#545b62',
      '#3f444c',
      '#2a2e33',
      '#1f2229',
      '#15171a',
      '#0a0b0d',
    ],
    tailwindName: 'gray', // closest
  },

  // 31
  {
    name: 'Wisteria Purple',
    colors: [
      '#fbd2f1',
      '#f7b3e3',
      '#f191d1',
      '#e668b5',
      '#d53f8c',
      '#b83280',
      '#9e2b72',
      '#86266b',
      '#6f2160',
      '#5b1c4e',
      '#3d1334',
    ],
    tailwindName: 'pink', // closest
  },

  // 32
  {
    name: 'Sea Abyss',
    colors: [
      '#f2f6f9',
      '#dee9ef',
      '#c1d6e0',
      '#96b8ca',
      '#6491ac',
      '#446e88',
      '#3f617b',
      '#385166',
      '#344656',
      '#2f3d4a',
      '#1b2631',
    ],
    tailwindName: 'sky', // closest
  },

  // 33
  {
    name: 'Jet Blue',
    colors: [
      '#f4f7fb',
      '#e8eef6',
      '#cddbea',
      '#a0bed9',
      '#6d9ac3',
      '#4b7eac',
      '#37618c',
      '#2f5075',
      '#2a4562',
      '#273c53',
      '#1a2637',
    ],
    tailwindName: 'blue', // closest
  },

  // 34
  {
    name: 'Mauve Smoke',
    colors: [
      '#f7f3f5',
      '#f0e6eb',
      '#e4ccd3',
      '#d8b2ba',
      '#b98b94',
      '#8f666f',
      '#6d4a54',
      '#51363f',
      '#38272d',
      '#26191f',
      '#100b0e',
    ],
    tailwindName: 'red', // closest
  },

  // 35
  {
    name: 'Simple Lavender',
    colors: [
      '#f5f5f9',
      '#eae8f1',
      '#d9d6e7',
      '#bebad6',
      '#a099c1',
      '#8b80b1',
      '#7e6ea2',
      '#736293',
      '#625479',
      '#504662',
      '#3b3446',
    ],
    tailwindName: 'violet', // closest
  },

  // 36
  {
    name: 'Grayish Sage',
    colors: [
      '#eaedef',
      '#d3d8dc',
      '#b8bfc4',
      '#99a3aa',
      '#7b8b93',
      '#627d85',
      '#4e6b75',
      '#3c5866',
      '#274753',
      '#1a3741',
      '#0b1f2e',
    ],
    tailwindName: 'stone', // closest
  },

  // 37
  {
    name: 'Dark Night',
    colors: [
      '#f2f2f2',
      '#e5e7eb',
      '#d1d5db',
      '#9ca3af',
      '#6b7280',
      '#4b5563',
      '#374151',
      '#1f2937',
      '#111827',
      '#030712',
      '#020617',
    ],
    tailwindName: 'gray', // closest
  },
];

export interface ColorThemeObject {
  name: string;
  colors: {
    primary: string[];
    secondary: string[];
  };
  tailwindNames: {
    primary: string;
    secondary: string;
  };
}

// Safe range for tailwind: 0 - 19
export const complementaryColorThemes: ColorThemeObject[] = [
  {
    name: `${colorThemes[0].name.split(' ')[0]} ${
      colorThemes[4].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[0].colors,
      secondary: colorThemes[4].colors,
    },
    tailwindNames: {
      primary: colorThemes[0].tailwindName,
      secondary: colorThemes[4].tailwindName,
    },
  },

  {
    name: `${colorThemes[1].name.split(' ')[0]} ${
      colorThemes[6].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[1].colors,
      secondary: colorThemes[6].colors,
    },
    tailwindNames: {
      primary: colorThemes[1].tailwindName,
      secondary: colorThemes[6].tailwindName,
    },
  },

  {
    name: `${colorThemes[9].name.split(' ')[0]} ${
      colorThemes[4].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[9].colors,
      secondary: colorThemes[4].colors,
    },
    tailwindNames: {
      primary: colorThemes[9].tailwindName,
      secondary: colorThemes[4].tailwindName,
    },
  },

  {
    name: `${colorThemes[14].name.split(' ')[0]} ${
      colorThemes[1].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[14].colors,
      secondary: colorThemes[1].colors,
    },
    tailwindNames: {
      primary: colorThemes[14].tailwindName,
      secondary: colorThemes[1].tailwindName,
    },
  },

  {
    name: `${colorThemes[10].name.split(' ')[0]} ${
      colorThemes[5].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[10].colors,
      secondary: colorThemes[5].colors,
    },
    tailwindNames: {
      primary: colorThemes[10].tailwindName,
      secondary: colorThemes[5].tailwindName,
    },
  },

  {
    name: `${colorThemes[11].name.split(' ')[0]} ${
      colorThemes[6].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[11].colors,
      secondary: colorThemes[6].colors,
    },
    tailwindNames: {
      primary: colorThemes[11].tailwindName,
      secondary: colorThemes[6].tailwindName,
    },
  },

  {
    name: `${colorThemes[12].name.split(' ')[0]} ${
      colorThemes[7].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[12].colors,
      secondary: colorThemes[7].colors,
    },
    tailwindNames: {
      primary: colorThemes[12].tailwindName,
      secondary: colorThemes[7].tailwindName,
    },
  },

  {
    name: `${colorThemes[13].name.split(' ')[0]} ${
      colorThemes[8].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[13].colors,
      secondary: colorThemes[8].colors,
    },
    tailwindNames: {
      primary: colorThemes[13].tailwindName,
      secondary: colorThemes[8].tailwindName,
    },
  },

  {
    name: `${colorThemes[15].name.split(' ')[0]} ${
      colorThemes[19].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[15].colors,
      secondary: colorThemes[19].colors,
    },
    tailwindNames: {
      primary: colorThemes[15].tailwindName,
      secondary: colorThemes[19].tailwindName,
    },
  },

  {
    name: `${colorThemes[14].name.split(' ')[0]} ${
      colorThemes[9].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[14].colors,
      secondary: colorThemes[9].colors,
    },
    tailwindNames: {
      primary: colorThemes[14].tailwindName,
      secondary: colorThemes[9].tailwindName,
    },
  },

  {
    name: `${colorThemes[14].name.split(' ')[0]} ${
      colorThemes[16].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[14].colors,
      secondary: colorThemes[16].colors,
    },
    tailwindNames: {
      primary: colorThemes[14].tailwindName,
      secondary: colorThemes[16].tailwindName,
    },
  },

  {
    name: `${colorThemes[14].name.split(' ')[0]} ${
      colorThemes[19].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[14].colors,
      secondary: colorThemes[19].colors,
    },
    tailwindNames: {
      primary: colorThemes[14].tailwindName,
      secondary: colorThemes[19].tailwindName,
    },
  },

  {
    name: `${colorThemes[15].name.split(' ')[0]} ${
      colorThemes[10].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[15].colors,
      secondary: colorThemes[10].colors,
    },
    tailwindNames: {
      primary: colorThemes[15].tailwindName,
      secondary: colorThemes[10].tailwindName,
    },
  },

  {
    name: `${colorThemes[16].name.split(' ')[0]} ${
      colorThemes[11].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[16].colors,
      secondary: colorThemes[11].colors,
    },
    tailwindNames: {
      primary: colorThemes[16].tailwindName,
      secondary: colorThemes[11].tailwindName,
    },
  },

  {
    name: `${colorThemes[17].name.split(' ')[0]} ${
      colorThemes[12].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[17].colors,
      secondary: colorThemes[12].colors,
    },
    tailwindNames: {
      primary: colorThemes[17].tailwindName,
      secondary: colorThemes[12].tailwindName,
    },
  },

  {
    name: `${colorThemes[18].name.split(' ')[0]} ${
      colorThemes[13].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[18].colors,
      secondary: colorThemes[13].colors,
    },
    tailwindNames: {
      primary: colorThemes[18].tailwindName,
      secondary: colorThemes[13].tailwindName,
    },
  },

  {
    name: `${colorThemes[19].name.split(' ')[0]} ${
      colorThemes[14].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[19].colors,
      secondary: colorThemes[14].colors,
    },
    tailwindNames: {
      primary: colorThemes[19].tailwindName,
      secondary: colorThemes[14].tailwindName,
    },
  },

  {
    name: `${colorThemes[5].name.split(' ')[0]} ${
      colorThemes[15].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[5].colors,
      secondary: colorThemes[15].colors,
    },
    tailwindNames: {
      primary: colorThemes[5].tailwindName,
      secondary: colorThemes[15].tailwindName,
    },
  },

  {
    name: `${colorThemes[20].name.split(' ')[0]} ${
      colorThemes[16].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[20].colors,
      secondary: colorThemes[16].colors,
    },
    tailwindNames: {
      primary: colorThemes[20].tailwindName,
      secondary: colorThemes[16].tailwindName,
    },
  },

  {
    name: `${colorThemes[6].name.split(' ')[0]} ${
      colorThemes[16].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[6].colors,
      secondary: colorThemes[16].colors,
    },
    tailwindNames: {
      primary: colorThemes[6].tailwindName,
      secondary: colorThemes[16].tailwindName,
    },
  },

  {
    name: `${colorThemes[7].name.split(' ')[0]} ${
      colorThemes[17].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[7].colors,
      secondary: colorThemes[17].colors,
    },
    tailwindNames: {
      primary: colorThemes[7].tailwindName,
      secondary: colorThemes[17].tailwindName,
    },
  },

  {
    name: `${colorThemes[8].name.split(' ')[0]} ${
      colorThemes[18].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[8].colors,
      secondary: colorThemes[18].colors,
    },
    tailwindNames: {
      primary: colorThemes[8].tailwindName,
      secondary: colorThemes[18].tailwindName,
    },
  },

  {
    name: `${colorThemes[9].name.split(' ')[0]} ${
      colorThemes[19].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[9].colors,
      secondary: colorThemes[19].colors,
    },
    tailwindNames: {
      primary: colorThemes[9].tailwindName,
      secondary: colorThemes[19].tailwindName,
    },
  },

  {
    name: `${colorThemes[10].name.split(' ')[0]} ${
      colorThemes[15].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[10].colors,
      secondary: colorThemes[15].colors,
    },
    tailwindNames: {
      primary: colorThemes[10].tailwindName,
      secondary: colorThemes[15].tailwindName,
    },
  },

  {
    name: `${colorThemes[12].name.split(' ')[0]} ${
      colorThemes[18].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[12].colors,
      secondary: colorThemes[18].colors,
    },
    tailwindNames: {
      primary: colorThemes[12].tailwindName,
      secondary: colorThemes[18].tailwindName,
    },
  },

  {
    name: `${colorThemes[14].name.split(' ')[0]} ${
      colorThemes[20].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[14].colors,
      secondary: colorThemes[20].colors,
    },
    tailwindNames: {
      primary: colorThemes[14].tailwindName,
      secondary: colorThemes[20].tailwindName,
    },
  },

  {
    name: `${colorThemes[13].name.split(' ')[0]} ${
      colorThemes[8].name.split(' ')[0]
    }`,
    colors: {
      primary: colorThemes[13].colors,
      secondary: colorThemes[8].colors,
    },
    tailwindNames: {
      primary: colorThemes[13].tailwindName,
      secondary: colorThemes[8].tailwindName,
    },
  },

  {
    name: `${colorThemes[14].name.split(' ')[0]} ${
      colorThemes[6].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[14].colors,
      secondary: colorThemes[6].colors,
    },
    tailwindNames: {
      primary: colorThemes[14].tailwindName,
      secondary: colorThemes[6].tailwindName,
    },
  },

  {
    name: `${colorThemes[0].name.split(' ')[0]} ${
      colorThemes[8].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[0].colors,
      secondary: colorThemes[8].colors,
    },
    tailwindNames: {
      primary: colorThemes[0].tailwindName,
      secondary: colorThemes[8].tailwindName,
    },
  },

  {
    name: `${colorThemes[0].name.split(' ')[0]} ${
      colorThemes[10].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[0].colors,
      secondary: colorThemes[10].colors,
    },
    tailwindNames: {
      primary: colorThemes[0].tailwindName,
      secondary: colorThemes[10].tailwindName,
    },
  },

  {
    name: `${colorThemes[0].name.split(' ')[0]} ${
      colorThemes[13].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[0].colors,
      secondary: colorThemes[13].colors,
    },
    tailwindNames: {
      primary: colorThemes[0].tailwindName,
      secondary: colorThemes[13].tailwindName,
    },
  },

  {
    name: `${colorThemes[4].name.split(' ')[0]} ${
      colorThemes[17].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[4].colors,
      secondary: colorThemes[17].colors,
    },
    tailwindNames: {
      primary: colorThemes[4].tailwindName,
      secondary: colorThemes[17].tailwindName,
    },
  },

  {
    name: `${colorThemes[4].name.split(' ')[0]} ${
      colorThemes[1].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[4].colors,
      secondary: colorThemes[1].colors,
    },
    tailwindNames: {
      primary: colorThemes[4].tailwindName,
      secondary: colorThemes[1].tailwindName,
    },
  },

  {
    name: `${colorThemes[20].name.split(' ')[0]} ${
      colorThemes[2].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[20].colors,
      secondary: colorThemes[2].colors,
    },
    tailwindNames: {
      primary: colorThemes[20].tailwindName,
      secondary: colorThemes[2].tailwindName,
    },
  },

  {
    name: `${colorThemes[21].name.split(' ')[0]} ${
      colorThemes[9].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[21].colors,
      secondary: colorThemes[9].colors,
    },
    tailwindNames: {
      primary: colorThemes[21].tailwindName,
      secondary: colorThemes[9].tailwindName,
    },
  },
];

export const minimalColorThemes: ColorThemeObject[] = [
  {
    name: `${colorThemes[0].name.split(' ')[0]} ${
      colorThemes[1].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[0].colors,
      secondary: colorThemes[1].colors,
    },
    tailwindNames: {
      primary: colorThemes[0].tailwindName,
      secondary: colorThemes[1].tailwindName,
    },
  },
  {
    name: `${colorThemes[2].name.split(' ')[0]} ${
      colorThemes[37].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[2].colors,
      secondary: colorThemes[37].colors,
    },
    tailwindNames: {
      primary: colorThemes[2].tailwindName,
      secondary: colorThemes[37].tailwindName,
    },
  },
  {
    name: `${colorThemes[4].name.split(' ')[0]} ${
      colorThemes[29].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[4].colors,
      secondary: colorThemes[29].colors,
    },
    tailwindNames: {
      primary: colorThemes[4].tailwindName,
      secondary: colorThemes[29].tailwindName,
    },
  },

  {
    name: `${colorThemes[37].name.split(' ')[0]} ${
      colorThemes[2].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[37].colors,
      secondary: colorThemes[2].colors,
    },
    tailwindNames: {
      primary: colorThemes[37].tailwindName,
      secondary: colorThemes[2].tailwindName,
    },
  },
  {
    name: `${colorThemes[0].name.split(' ')[0]} ${
      colorThemes[4].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[0].colors,
      secondary: colorThemes[4].colors,
    },
    tailwindNames: {
      primary: colorThemes[0].tailwindName,
      secondary: colorThemes[4].tailwindName,
    },
  },
  {
    name: `${colorThemes[1].name.split(' ')[0]} ${
      colorThemes[37].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[1].colors,
      secondary: colorThemes[37].colors,
    },
    tailwindNames: {
      primary: colorThemes[1].tailwindName,
      secondary: colorThemes[37].tailwindName,
    },
  },
  {
    name: `${colorThemes[2].name.split(' ')[0]} ${
      colorThemes[30].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[2].colors,
      secondary: colorThemes[30].colors,
    },
    tailwindNames: {
      primary: colorThemes[2].tailwindName,
      secondary: colorThemes[30].tailwindName,
    },
  },

  {
    name: `${colorThemes[3].name.split(' ')[0]} ${
      colorThemes[0].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[3].colors,
      secondary: colorThemes[0].colors,
    },
    tailwindNames: {
      primary: colorThemes[3].tailwindName,
      secondary: colorThemes[0].tailwindName,
    },
  },

  {
    name: `${colorThemes[30].name.split(' ')[0]} ${
      colorThemes[33].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[30].colors,
      secondary: colorThemes[33].colors,
    },
    tailwindNames: {
      primary: colorThemes[30].tailwindName,
      secondary: colorThemes[33].tailwindName,
    },
  },

  {
    name: `${colorThemes[1].name.split(' ')[0]} ${
      colorThemes[32].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[1].colors,
      secondary: colorThemes[32].colors,
    },
    tailwindNames: {
      primary: colorThemes[1].tailwindName,
      secondary: colorThemes[32].tailwindName,
    },
  },

  {
    name: `${colorThemes[21].name.split(' ')[0]} ${
      colorThemes[37].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[21].colors,
      secondary: colorThemes[37].colors,
    },
    tailwindNames: {
      primary: colorThemes[21].tailwindName,
      secondary: colorThemes[37].tailwindName,
    },
  },

  {
    name: `${colorThemes[8].name.split(' ')[0]} ${
      colorThemes[29].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[8].colors,
      secondary: colorThemes[29].colors,
    },
    tailwindNames: {
      primary: colorThemes[8].tailwindName,
      secondary: colorThemes[29].tailwindName,
    },
  },
];

export const vividColorThemes = [
  {
    name: `${colorThemes[5].name.split(' ')[0]} ${
      colorThemes[7].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[5].colors,
      secondary: colorThemes[7].colors,
    },
    tailwindNames: {
      primary: colorThemes[5].tailwindName,
      secondary: colorThemes[7].tailwindName,
    },
  },
  {
    name: `${colorThemes[21].name.split(' ')[0]} ${
      colorThemes[23].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[21].colors,
      secondary: colorThemes[23].colors,
    },
    tailwindNames: {
      primary: colorThemes[21].tailwindName,
      secondary: colorThemes[23].tailwindName,
    },
  },

  {
    name: `${colorThemes[17].name.split(' ')[0]} ${
      colorThemes[8].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[17].colors,
      secondary: colorThemes[8].colors,
    },
    tailwindNames: {
      primary: colorThemes[17].tailwindName,
      secondary: colorThemes[8].tailwindName,
    },
  },

  {
    name: `${colorThemes[6].name.split(' ')[0]} ${
      colorThemes[19].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[6].colors,
      secondary: colorThemes[19].colors,
    },
    tailwindNames: {
      primary: colorThemes[6].tailwindName,
      secondary: colorThemes[19].tailwindName,
    },
  },

  {
    name: `${colorThemes[15].name.split(' ')[0]} ${
      colorThemes[19].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[15].colors,
      secondary: colorThemes[19].colors,
    },
    tailwindNames: {
      primary: colorThemes[15].tailwindName,
      secondary: colorThemes[19].tailwindName,
    },
  },
  {
    name: `${colorThemes[14].name.split(' ')[0]} ${
      colorThemes[28].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[14].colors,
      secondary: colorThemes[28].colors,
    },
    tailwindNames: {
      primary: colorThemes[14].tailwindName,
      secondary: colorThemes[28].tailwindName,
    },
  },

  {
    name: `${colorThemes[27].name.split(' ')[0]} ${
      colorThemes[20].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[27].colors,
      secondary: colorThemes[20].colors,
    },
    tailwindNames: {
      primary: colorThemes[27].tailwindName,
      secondary: colorThemes[20].tailwindName,
    },
  },

  {
    name: `${colorThemes[16].name.split(' ')[0]} ${
      colorThemes[22].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[16].colors,
      secondary: colorThemes[22].colors,
    },
    tailwindNames: {
      primary: colorThemes[16].tailwindName,
      secondary: colorThemes[22].tailwindName,
    },
  },

  {
    name: `${colorThemes[18].name.split(' ')[0]} ${
      colorThemes[24].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[18].colors,
      secondary: colorThemes[24].colors,
    },
    tailwindNames: {
      primary: colorThemes[18].tailwindName,
      secondary: colorThemes[24].tailwindName,
    },
  },

  {
    name: `${colorThemes[18].name.split(' ')[0]} ${
      colorThemes[9].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[18].colors,
      secondary: colorThemes[9].colors,
    },
    tailwindNames: {
      primary: colorThemes[18].tailwindName,
      secondary: colorThemes[9].tailwindName,
    },
  },

  {
    name: `${colorThemes[28].name.split(' ')[0]} ${
      colorThemes[24].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[28].colors,
      secondary: colorThemes[24].colors,
    },
    tailwindNames: {
      primary: colorThemes[28].tailwindName,
      secondary: colorThemes[24].tailwindName,
    },
  },

  {
    name: `${colorThemes[5].name.split(' ')[0]} ${
      colorThemes[9].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[5].colors,
      secondary: colorThemes[9].colors,
    },
    tailwindNames: {
      primary: colorThemes[5].tailwindName,
      secondary: colorThemes[9].tailwindName,
    },
  },

  {
    name: `${colorThemes[28].name.split(' ')[0]} ${
      colorThemes[18].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[28].colors,
      secondary: colorThemes[18].colors,
    },
    tailwindNames: {
      primary: colorThemes[28].tailwindName,
      secondary: colorThemes[18].tailwindName,
    },
  },

  {
    name: `${colorThemes[7].name.split(' ')[0]} ${
      colorThemes[20].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[7].colors,
      secondary: colorThemes[20].colors,
    },
    tailwindNames: {
      primary: colorThemes[7].tailwindName,
      secondary: colorThemes[20].tailwindName,
    },
  },

  {
    name: `${colorThemes[8].name.split(' ')[0]} ${
      colorThemes[25].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[8].colors,
      secondary: colorThemes[25].colors,
    },
    tailwindNames: {
      primary: colorThemes[8].tailwindName,
      secondary: colorThemes[25].tailwindName,
    },
  },
];

export const freshColorThemes = [
  {
    name: `${colorThemes[9].name.split(' ')[0]} ${
      colorThemes[25].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[9].colors,
      secondary: colorThemes[25].colors,
    },
    tailwindNames: {
      primary: colorThemes[9].tailwindName,
      secondary: colorThemes[25].tailwindName,
    },
  },
  {
    name: `${colorThemes[10].name.split(' ')[0]} ${
      colorThemes[13].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[10].colors,
      secondary: colorThemes[13].colors,
    },
    tailwindNames: {
      primary: colorThemes[10].tailwindName,
      secondary: colorThemes[13].tailwindName,
    },
  },
  {
    name: `${colorThemes[9].name.split(' ')[0]} ${
      colorThemes[15].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[9].colors,
      secondary: colorThemes[15].colors,
    },
    tailwindNames: {
      primary: colorThemes[9].tailwindName,
      secondary: colorThemes[15].tailwindName,
    },
  },
  {
    name: `${colorThemes[12].name.split(' ')[0]} ${
      colorThemes[16].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[12].colors,
      secondary: colorThemes[16].colors,
    },
    tailwindNames: {
      primary: colorThemes[12].tailwindName,
      secondary: colorThemes[16].tailwindName,
    },
  },
  {
    name: `${colorThemes[13].name.split(' ')[0]} ${
      colorThemes[17].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[13].colors,
      secondary: colorThemes[17].colors,
    },
    tailwindNames: {
      primary: colorThemes[13].tailwindName,
      secondary: colorThemes[17].tailwindName,
    },
  },

  {
    name: `${colorThemes[16].name.split(' ')[0]} ${
      colorThemes[24].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[16].colors,
      secondary: colorThemes[24].colors,
    },
    tailwindNames: {
      primary: colorThemes[16].tailwindName,
      secondary: colorThemes[24].tailwindName,
    },
  },

  {
    name: `${colorThemes[18].name.split(' ')[0]} ${
      colorThemes[25].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[18].colors,
      secondary: colorThemes[25].colors,
    },
    tailwindNames: {
      primary: colorThemes[18].tailwindName,
      secondary: colorThemes[25].tailwindName,
    },
  },

  {
    name: `${colorThemes[27].name.split(' ')[0]} ${
      colorThemes[10].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[27].colors,
      secondary: colorThemes[10].colors,
    },
    tailwindNames: {
      primary: colorThemes[27].tailwindName,
      secondary: colorThemes[10].tailwindName,
    },
  },

  {
    name: `${colorThemes[14].name.split(' ')[0]} ${
      colorThemes[11].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[14].colors,
      secondary: colorThemes[11].colors,
    },
    tailwindNames: {
      primary: colorThemes[14].tailwindName,
      secondary: colorThemes[11].tailwindName,
    },
  },

  {
    name: `${colorThemes[12].name.split(' ')[0]} ${
      colorThemes[10].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[12].colors,
      secondary: colorThemes[10].colors,
    },
    tailwindNames: {
      primary: colorThemes[12].tailwindName,
      secondary: colorThemes[10].tailwindName,
    },
  },

  {
    name: `${colorThemes[15].name.split(' ')[0]} ${
      colorThemes[11].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[15].colors,
      secondary: colorThemes[11].colors,
    },
    tailwindNames: {
      primary: colorThemes[15].tailwindName,
      secondary: colorThemes[11].tailwindName,
    },
  },

  {
    name: `${colorThemes[10].name.split(' ')[0]} ${
      colorThemes[8].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[10].colors,
      secondary: colorThemes[8].colors,
    },
    tailwindNames: {
      primary: colorThemes[10].tailwindName,
      secondary: colorThemes[8].tailwindName,
    },
  },

  {
    name: `${colorThemes[12].name.split(' ')[0]} ${
      colorThemes[23].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[12].colors,
      secondary: colorThemes[23].colors,
    },
    tailwindNames: {
      primary: colorThemes[12].tailwindName,
      secondary: colorThemes[23].tailwindName,
    },
  },

  {
    name: `${colorThemes[8].name.split(' ')[0]} ${
      colorThemes[9].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[8].colors,
      secondary: colorThemes[9].colors,
    },
    tailwindNames: {
      primary: colorThemes[8].tailwindName,
      secondary: colorThemes[9].tailwindName,
    },
  },

  {
    name: `${colorThemes[7].name.split(' ')[0]} ${
      colorThemes[13].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[7].colors,
      secondary: colorThemes[13].colors,
    },
    tailwindNames: {
      primary: colorThemes[7].tailwindName,
      secondary: colorThemes[13].tailwindName,
    },
  },
];

export const expensiveColorThemes = [
  {
    name: `${colorThemes[19].name.split(' ')[0]} ${
      colorThemes[15].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[19].colors,
      secondary: colorThemes[15].colors,
    },
    tailwindNames: {
      primary: colorThemes[19].tailwindName,
      secondary: colorThemes[15].tailwindName,
    },
  },

  {
    name: `${colorThemes[20].name.split(' ')[0]} ${
      colorThemes[27].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[20].colors,
      secondary: colorThemes[27].colors,
    },
    tailwindNames: {
      primary: colorThemes[20].tailwindName,
      secondary: colorThemes[27].tailwindName,
    },
  },

  {
    name: `${colorThemes[18].name.split(' ')[0]} ${
      colorThemes[14].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[18].colors,
      secondary: colorThemes[14].colors,
    },
    tailwindNames: {
      primary: colorThemes[18].tailwindName,
      secondary: colorThemes[14].tailwindName,
    },
  },

  {
    name: `${colorThemes[17].name.split(' ')[0]} ${
      colorThemes[13].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[17].colors,
      secondary: colorThemes[13].colors,
    },
    tailwindNames: {
      primary: colorThemes[17].tailwindName,
      secondary: colorThemes[13].tailwindName,
    },
  },

  {
    name: `${colorThemes[12].name.split(' ')[0]} ${
      colorThemes[27].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[12].colors,
      secondary: colorThemes[27].colors,
    },
    tailwindNames: {
      primary: colorThemes[12].tailwindName,
      secondary: colorThemes[27].tailwindName,
    },
  },

  {
    name: `${colorThemes[13].name.split(' ')[0]} ${
      colorThemes[16].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[13].colors,
      secondary: colorThemes[16].colors,
    },
    tailwindNames: {
      primary: colorThemes[13].tailwindName,
      secondary: colorThemes[16].tailwindName,
    },
  },

  {
    name: `${colorThemes[14].name.split(' ')[0]} ${
      colorThemes[18].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[14].colors,
      secondary: colorThemes[18].colors,
    },
    tailwindNames: {
      primary: colorThemes[14].tailwindName,
      secondary: colorThemes[18].tailwindName,
    },
  },

  {
    name: `${colorThemes[15].name.split(' ')[0]} ${
      colorThemes[25].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[15].colors,
      secondary: colorThemes[25].colors,
    },
    tailwindNames: {
      primary: colorThemes[15].tailwindName,
      secondary: colorThemes[25].tailwindName,
    },
  },

  {
    name: `${colorThemes[16].name.split(' ')[0]} ${
      colorThemes[32].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[16].colors,
      secondary: colorThemes[32].colors,
    },
    tailwindNames: {
      primary: colorThemes[16].tailwindName,
      secondary: colorThemes[32].tailwindName,
    },
  },

  {
    name: `${colorThemes[16].name.split(' ')[1]} ${
      colorThemes[26].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[16].colors,
      secondary: colorThemes[26].colors,
    },
    tailwindNames: {
      primary: colorThemes[16].tailwindName,
      secondary: colorThemes[26].tailwindName,
    },
  },

  {
    name: `${colorThemes[32].name.split(' ')[0]} ${
      colorThemes[27].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[32].colors,
      secondary: colorThemes[27].colors,
    },
    tailwindNames: {
      primary: colorThemes[32].tailwindName,
      secondary: colorThemes[27].tailwindName,
    },
  },

  {
    name: `${colorThemes[25].name.split(' ')[0]} ${
      colorThemes[15].name.split(' ')[1]
    }`,
    colors: {
      primary: colorThemes[25].colors,
      secondary: colorThemes[15].colors,
    },
    tailwindNames: {
      primary: colorThemes[25].tailwindName,
      secondary: colorThemes[15].tailwindName,
    },
  },
];

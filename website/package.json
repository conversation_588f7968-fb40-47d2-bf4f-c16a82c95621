{"name": "Page UI", "description": "Page UI is a set of landing page components & templates that you can copy & paste into you codebase. Made for React & built on top of Shadcn UI.", "version": "1.6.2", "private": true, "scripts": {"dev": "cross-env PORT=6010 next dev", "build": "cross-env next build && cross-env NODE_OPTIONS='--experimental-json-modules' node ./scripts/postbuild.mjs", "serve": "next start", "analyze": "cross-env ANALYZE=true next build", "lint": "next lint --fix --dir pages --dir app --dir components --dir lib --dir layouts --dir scripts", "pretty": "prettier --write ."}, "dependencies": {"@hookform/resolvers": "^3.3.1", "@monaco-editor/react": "^4.6.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-aspect-ratio": "^1.0.3", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-context-menu": "^2.1.5", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-hover-card": "^1.0.7", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.0.4", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-toggle-group": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@shipixen/next-contentlayer-module": "1.0.2", "@shipixen/pliny": "1.0.13", "@tailwindcss/forms": "^0.5.4", "@tailwindcss/typography": "^0.5.9", "@tanstack/react-table": "^8.10.7", "@types/react-dom": "^18.2.14", "@vercel/analytics": "^1.1.1", "@vercel/og": "^0.5.20", "axios": "^1.7.7", "chroma-js": "^2.4.2", "class-variance-authority": "^0.7.1", "clsx": "^2.0.0", "cmdk": "^0.2.0", "common-tags": "^1.8.2", "date-fns": "^2.30.0", "embla-carousel-react": "^8.0.0-rc17", "esbuild": "0.18.11", "framer-motion": "^10.16.4", "github-slugger": "^1.4.0", "image-size": "^1.0.0", "lucide-react": "^0.400.0", "mime-types": "^2.1.35", "monaco-editor": "^0.45.0", "next": "14.2.24", "next-themes": "^0.2.1", "posthog-js": "^1.130.1", "react": "18.2.0", "react-day-picker": "^8.8.2", "react-dom": "^18.2.0", "react-hook-form": "^7.46.2", "react-resizable-panels": "^1.0.7", "reading-time": "1.5.0", "rehype-autolink-headings": "^6.1.0", "rehype-citation": "^1.0.2", "rehype-katex": "^6.0.3", "rehype-preset-minify": "6.0.0", "rehype-prism-plus": "^1.6.1", "rehype-slug": "^5.1.0", "remark": "^14.0.2", "remark-gfm": "^3.0.1", "remark-math": "^5.1.1", "shipixen-contentlayer": "1.0.2", "sonner": "^1.3.1", "tailwind-merge": "^1.14.0", "tailwindcss": "^3.3.3", "tailwindcss-animate": "^1.0.7", "vaul": "^0.8.0", "zod": "^3.22.2", "zustand": "^4.5.2"}, "devDependencies": {"@next/bundle-analyzer": "14.2.24", "@svgr/webpack": "^8.0.1", "@types/mdx": "^2.0.5", "@types/react": "^18.2.36", "@typescript-eslint/eslint-plugin": "^6.1.0", "@typescript-eslint/parser": "^6.1.0", "autoprefixer": "^10.4.13", "cross-env": "^7.0.3", "eslint": "^8.45.0", "eslint-config-next": "^14.2.24", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^5.0.0", "husky": "^8.0.0", "lint-staged": "^13.0.0", "postcss": "^8.4.24", "prettier": "^3.0.0", "prettier-plugin-tailwindcss": "^0.4.1", "typescript": "^5.1.3"}, "overrides": {"react": "18.2.0", "next": "14.2.24"}, "resolutions": {"next": "14.2.24"}, "lint-staged": {"*.+(js|jsx|ts|tsx)": ["eslint --fix"], "*.+(js|jsx|ts|tsx|json|css|md|mdx)": ["prettier --write"]}}
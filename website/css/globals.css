@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --hard-shadow: 0px 29px 52px 0px rgba(0, 0, 0, 0.4),
      22px 25px 16px 0px rgba(0, 0, 0, 0.2);
    --hard-shadow-left: 0px 29px 52px 0px rgba(0, 0, 0, 0.4),
      -22px 25px 16px 0px rgba(0, 0, 0, 0.2);
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary-foreground: 355.7 100% 97.3%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 20 14.3% 4.1%;
    --foreground: 0 0% 95%;
    --card: 24 9.8% 10%;
    --card-foreground: 0 0% 95%;
    --popover: 0 0% 9%;
    --popover-foreground: 0 0% 95%;
    --primary-foreground: 144.9 80.4% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 15%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 12 6.5% 15.1%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 85.7% 97.3%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
  }

  *,
  ::before,
  ::after {
    @apply border-gray-100 dark:border-neutral-800;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-semibold font-display;
  }
}

@layer utilities {
  .perspective-none {
    transform: none;
  }

  .perspective-left {
    box-shadow: var(--hard-shadow);
    transform: perspective(400em) rotateY(-15deg) rotateX(6deg)
      skew(-8deg, 4deg) translate3d(-4%, -2%, 0) scale(0.8);
  }

  .perspective-right {
    box-shadow: var(--hard-shadow-left);
    transform: perspective(400em) rotateY(15deg) rotateX(6deg) skew(8deg, -4deg)
      translate3d(4%, -2%, 0) scale(0.8);
  }

  .perspective-bottom {
    box-shadow: var(--hard-shadow);
    transform: translateY(-4%) perspective(400em) rotateX(18deg) scale(0.9);
  }

  .perspective-bottom-lg {
    box-shadow: var(--hard-shadow);
    transform: perspective(400em) translate3d(0, -6%, 0) rotateX(34deg)
      scale(0.8);
  }

  .perspective-paper {
    box-shadow: var(--hard-shadow);
    transform: rotateX(40deg) rotate(40deg) scale(0.8);
  }

  .perspective-paper-left {
    box-shadow: var(--hard-shadow-left);
    transform: rotateX(40deg) rotate(-40deg) scale(0.8);
  }

  .hard-shadow {
    box-shadow: var(--hard-shadow);
  }

  .hard-shadow-left {
    box-shadow: var(--hard-shadow-left);
  }

  .faq li h3 {
    @apply font-semibold text-lg;
  }

  .nav-link-active {
    @apply opacity-80 py-2 rounded text-secondary-500 dark:text-slate-400;
  }

  .nav-link {
    @apply opacity-100 text-slate-900 dark:text-slate-200 hover:text-secondary-700 dark:hover:text-secondary-100 transition-all;
  }

  .toc-link {
    @apply text-sm;
  }

  .toc-active-link {
    @apply text-sm text-primary-500 dark:text-primary-400;
  }

  .toc-list,
  .toc-list ul,
  ul.toc-list {
    @apply flex flex-col gap-2;
  }

  .toc-separator {
    @apply border-b border-gray-300 dark:border-neutral-600;
  }

  .toc-title {
    @apply mt-6 text-sm font-semibold text-black dark:text-white;
  }

  /**
   * Fancy heading gradients
   */
  @supports (background-clip: text) {
    .fancy-heading {
      background-image: linear-gradient(
        rgb(10, 10, 10) 10%,
        rgba(10, 10, 10, 0.68)
      );
      background-clip: text;
      color: transparent;
    }

    .dark .fancy-heading {
      background-image: linear-gradient(
        rgb(245, 245, 245) 10%,
        rgba(245, 245, 245, 0.68)
      );
      background-clip: text;
      color: transparent;
    }
  }

  /**
   * Fancy overlay gradients & animation.
   */
  @property --fancy-x {
    syntax: '<percentage>';
    inherits: true;
    initial-value: 0%;
  }
  @property --fancy-y {
    syntax: '<percentage>';
    inherits: true;
    initial-value: 0%;
  }

  @keyframes roundabout {
    0% {
      --fancy-x: 60%;
      --fancy-y: 20%;

      opacity: 0;
    }

    5% {
      --fancy-x: 80%;
      --fancy-y: 10%;
    }

    20% {
      --fancy-x: 95%;
      --fancy-y: 5%;

      opacity: var(--maximum-opacity);
    }

    100% {
      --fancy-x: 100%;
      --fancy-y: 0%;

      opacity: var(--maximum-opacity);
    }
  }

  .fancy-overlay::after {
    --maximum-opacity: 0.1;

    content: '';
    position: fixed;
    top: 0;
    left: 0;
    z-index: -1;
    width: 100%;
    height: 100%;
    background-image: linear-gradient(
      54deg,
      var(--primary-light-hex) var(--fancy-y) var(--fancy-y),
      var(--secondary-light-hex) var(--fancy-x) var(--fancy-x)
    );
    animation: roundabout 5s ease-in-out both;
  }

  .dark .fancy-overlay::after {
    background-image: linear-gradient(
      54deg,
      var(--primary-dark-hex) var(--fancy-y) var(--fancy-y),
      var(--secondary-dark-hex) var(--fancy-x) var(--fancy-x)
    );
  }

  .fancy-overlay--muted::after {
    --maximum-opacity: 0.05;
    animation: roundabout 5s ease-in-out both;
  }

  /**
   * Fancy links
   * with a gradient background and underline
   * and fancy hover underline via tailwind
   */
  .fancy-link {
    background-image: linear-gradient(
      120deg,
      var(--primary-light-hex) 0%,
      var(--primary-darker-hex) 100%
    );
    background-repeat: no-repeat;
    background-size: 100% 0;
    background-position: 0 100%;
    transition: background-size 0.25s ease-in;
    text-decoration: none;
    color: rgba(0, 0, 0, 0.5);
    background-clip: text;
    text-decoration-line: underline;
    text-underline-offset: 3px;
  }

  .fancy-link:hover {
    background-size: 100% 100%;
  }

  .fancy-link--no-underline {
    text-decoration: none;
  }

  .dark .fancy-link {
    color: rgba(255, 255, 255, 0.5);
  }

  /**
   * Fancy Text
   */
  .fancy-text-black {
    @apply bg-clip-text bg-gradient-to-r text-transparent from-black to-neutral-500;
  }

  .fancy-text-blue {
    @apply bg-clip-text bg-gradient-to-r text-transparent from-sky-400 to-blue-700;
  }

  .fancy-text-pink {
    @apply bg-clip-text bg-gradient-to-r text-transparent from-pink-500 to-yellow-500;
  }

  .fancy-text-white {
    @apply bg-clip-text bg-gradient-to-r text-transparent from-gray-100 to-neutral-400;
  }

  .fancy-text-purple {
    @apply bg-clip-text bg-gradient-to-r text-transparent from-purple-500 to-fuchsia-800 dark:from-purple-400 dark:to-fuchsia-600;
  }

  /**
   * Fancy glass
   * with a gradient background and blur
   */
  .fancy-glass,
  .fancy-glass-contrast {
    --glass-color: 0, 0, 200;

    background: radial-gradient(
        63.94% 63.94% at 50% 0%,
        rgba(var(--glass-color), 0.12) 0%,
        rgba(var(--glass-color), 0) 100%
      ),
      rgba(var(--glass-color), 0.01);
    backdrop-filter: blur(6px);
    position: relative;
    overflow: hidden;
  }

  .fancy-glass-contrast:after {
    content: '';
    width: calc(100% + 2px);
    height: calc(100% + 2px);
    background: var(--primary-darker-hex);
    opacity: 0.1;
    position: absolute;
    top: -1px;
    left: -1px;
    z-index: -1;
  }

  .fancy-glass-contrast:before,
  .fancy-glass:before {
    content: '';
    width: calc(100% + 2px);
    height: calc(100% + 2px);
    background: linear-gradient(
        rgba(var(--glass-color), 0.12) 0%,
        rgba(var(--glass-color), 0) 74.04%
      ),
      linear-gradient(
        0deg,
        rgba(var(--glass-color), 0.04),
        rgba(var(--glass-color), 0.04)
      );
    position: absolute;
    top: -1px;
    left: -1px;
    mask: url("data:image/svg+xml,%3Csvg width='402' height='202' viewBox='0 0 402 202' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect x='0.5' y='0.5' width='401' height='201' rx='9.5' /%3E%3C/svg%3E%0A");
    pointer-events: none;
  }
}

/**
 * Container utilities
 */
.narrow-container {
  @apply w-full p-6 max-w-4xl;
}

.wide-container {
  @apply w-full p-6 max-w-full xl:max-w-6xl;
}

.ultrawide-container {
  @apply w-full p-6 max-w-full xl:max-w-7xl;
}

.gigawide-container {
  @apply w-full p-6 max-w-full xl:max-w-[1400px];
}

/**
 * Container utilities
 */
.container-narrow {
  @apply max-w-4xl;
}

.container-wide {
  @apply xl:max-w-6xl;
}

.container-ultrawide {
  @apply xl:max-w-7xl;
}

/**
 * MDX requirements
 */
.task-list-item::before {
  @apply hidden;
}

.task-list-item {
  @apply list-none;
}

.footnotes {
  @apply mt-12 border-t border-gray-200 pt-8 dark:border-gray-700;
}

.data-footnote-backref {
  @apply no-underline;
}

.csl-entry {
  @apply my-5;
}

/**
 * Hacks and fixes
 */
.safari-border-radius-fix {
  -webkit-mask-image: -webkit-radial-gradient(white, black);
}

[data-nextjs-scroll-focus-boundary] {
  display: contents;
}

/* https://stackoverflow.com/questions/61083813/how-to-avoid-internal-autofill-selected-style-to-be-applied */
input:-webkit-autofill,
input:-webkit-autofill:focus {
  transition:
    background-color 600000s 0s,
    color 600000s 0s;
}

/**
 * Scrollbar
 */
::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

::-webkit-scrollbar-track {
  background-color: rgba(240, 240, 240, 0.1);
}

::-webkit-scrollbar-thumb {
  background-color: #d6dee1;
  border-radius: 16px;
  border: 4px solid transparent;
  background-clip: content-box;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #a8bbbf;
}

::-webkit-scrollbar-corner {
  background: rgba(240, 240, 240, 0.1);
}

.no-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

/**
 * Set select colors to primary and respect dark mode
 */
::selection {
  background-color: var(--primary-lighter-hex);
  color: black;
}

.dark ::selection {
  background-color: var(--primary-darker-hex);
  color: white;
}

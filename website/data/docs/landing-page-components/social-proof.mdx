---
title: 'Landing Page Social Proof Component'
date: '2024-04-04'
lastmod: '2024-07-06'
tags:
  - 'guide'
  - 'components'
  - 'documentation'
  - 'landing-page'
  - 'ui-library'
  - 'react'
  - 'nextjs'
  - 'tailwind-css'
  - 'social-proof'
  - 'shadcn-ui'
  - 'shadcn-ui-social-proof'
  - 'shadcn-ui-landing-page'

images:
  - '/static/images/blog/docs/landing-page-social-proof-component.webp'

summary: 'A component meant to be used in the landing page. Use this to show proof of existing, happy customers & increase trust..'
layout: PostHub

showThemeSelector: true
showInstallNotice: true

canonicalUrl: 'https://shipixen.com/boilerplate-documentation/landing-page-components/social-proof'
---

Use this to show proof of existing, happy customers & increase trust.

Shows social proof with avatars, number of users and an optional rating.

<ComponentExample previewComponent={
  <LandingSocialProof
    className="p-4"
    numberOfUsers={99}
    avatarItems={[
      {
        imageSrc: '/static/images/people/1.webp',
        name: '<PERSON>',
      },
      {
        imageSrc: '/static/images/people/2.webp',
        name: '<PERSON>',
      },
      {
        imageSrc: '/static/images/people/3.webp',
        name: 'Alice Doe',
      },
      {
        imageSrc: '/static/images/people/4.webp',
        name: 'Bob Doe',
      },
      {
        imageSrc: '/static/images/people/5.webp',
        name: 'Eve Doe',
      },
    ]}
  />}>

```jsx
import { LandingSocialProof } from '@/components/landing/social-proof/LandingSocialProof';

const avatarItems = [
  {
    imageSrc: '/static/images/people/1.webp',
    name: 'John Doe',
  },
  {
    imageSrc: '/static/images/people/2.webp',
    name: 'Jane Doe',
  },
  {
    imageSrc: '/static/images/people/3.webp',
    name: 'Alice Doe',
  },
  {
    imageSrc: '/static/images/people/4.webp',
    name: 'Bob Doe',
  },
  {
    imageSrc: '/static/images/people/5.webp',
    name: 'Eve Doe',
  },
]

<LandingSocialProof
  numberOfUsers={99}
  avatarItems={avatarItems}
/>
```

</ComponentExample>

## Usage

```jsx
import { LandingSocialProof } from '@/components/landing/social-proof/LandingSocialProof';
```

```jsx
const avatarItems = [
  {
    imageSrc: '/static/images/people/1.webp',
    name: 'John Doe',
  },
  {
    imageSrc: '/static/images/people/2.webp',
    name: 'Jane Doe',
  },
  {
    imageSrc: '/static/images/people/3.webp',
    name: 'Alice Doe',
  },
  {
    imageSrc: '/static/images/people/4.webp',
    name: 'Bob Doe',
  },
  {
    imageSrc: '/static/images/people/5.webp',
    name: 'Eve Doe',
  },
];
```

```jsx
<LandingSocialProof numberOfUsers={99} avatarItems={avatarItems} />
```

## Examples

### With Rating

<ComponentExample previewComponent={
  <LandingSocialProof
    className="p-4"
    showRating
    numberOfUsers={99}
    avatarItems={[
      {
        imageSrc: '/static/images/people/1.webp',
        name: 'John Doe',
      },
      {
        imageSrc: '/static/images/people/2.webp',
        name: 'Jane Doe',
      },
      {
        imageSrc: '/static/images/people/3.webp',
        name: 'Alice Doe',
      },
    ]}
  />}>

```jsx
import { LandingSocialProof } from '@/components/landing/social-proof/LandingSocialProof';

const avatarItems = [
  {
    imageSrc: '/static/images/people/1.webp',
    name: 'John Doe',
  },
  {
    imageSrc: '/static/images/people/2.webp',
    name: 'Jane Doe',
  },
  {
    imageSrc: '/static/images/people/3.webp',
    name: 'Alice Doe',
  },
]

<LandingSocialProof
  showRating
  numberOfUsers={99}
  avatarItems={avatarItems}
/>
```

</ComponentExample>

### With Custom Suffix Text

<ComponentExample previewComponent={
  <LandingSocialProof
    className="p-4"
    showRating
    suffixText="experienced developers"
    numberOfUsers={99}
    avatarItems={[
      {
        imageSrc: '/static/images/people/1.webp',
        name: 'John Doe',
      },
      {
        imageSrc: '/static/images/people/2.webp',
        name: 'Jane Doe',
      },
      {
        imageSrc: '/static/images/people/3.webp',
        name: 'Alice Doe',
      },
    ]}
  />}>

```jsx
import { LandingSocialProof } from '@/components/landing/social-proof/LandingSocialProof';

const avatarItems = [
  {
    imageSrc: '/static/images/people/1.webp',
    name: 'John Doe',
  },
  {
    imageSrc: '/static/images/people/2.webp',
    name: 'Jane Doe',
  },
  {
    imageSrc: '/static/images/people/3.webp',
    name: 'Alice Doe',
  },
]

<LandingSocialProof
  suffixText="experienced developers"
  showRating
  numberOfUsers={99}
  avatarItems={avatarItems}
/>
```

</ComponentExample>

### Without Hover Animation

<ComponentExample previewComponent={
  <LandingSocialProof
    className="p-4"
    disableAnimation
    numberOfUsers={99}
    avatarItems={[
      {
        imageSrc: '/static/images/people/1.webp',
        name: 'John Doe',
      },
      {
        imageSrc: '/static/images/people/2.webp',
        name: 'Jane Doe',
      },
      {
        imageSrc: '/static/images/people/3.webp',
        name: 'Alice Doe',
      },
      {
        imageSrc: '/static/images/people/4.webp',
        name: 'Bob Doe',
      },
      {
        imageSrc: '/static/images/people/5.webp',
        name: 'Eve Doe',
      },
    ]}
  />}>

```jsx
import { LandingSocialProof } from '@/components/landing/social-proof/LandingSocialProof';

const avatarItems = [
  {
    imageSrc: '/static/images/people/1.webp',
    name: 'John Doe',
  },
  {
    imageSrc: '/static/images/people/2.webp',
    name: 'Jane Doe',
  },
  {
    imageSrc: '/static/images/people/3.webp',
    name: 'Alice Doe',
  },
  {
    imageSrc: '/static/images/people/4.webp',
    name: 'Bob Doe',
  },
  {
    imageSrc: '/static/images/people/5.webp',
    name: 'Eve Doe',
  },
]

<LandingSocialProof
  disableAnimation
  numberOfUsers={12000}
  avatarItems={avatarItems}
/>
```

</ComponentExample>

### With Primary Image Cta

<ComponentExample
  previewComponent={
    <>
      <LandingPrimaryImageCtaSection
        title="Landing page in minutes"
        description="Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back."
        imageSrc="/static/images/shipixen/product/1.webp"
        imageAlt="Sample image"
        withBackground
        withBackgroundGlow
        leadingComponent={<LandingProductHuntAward />}
      >
        <Button size="xl" asChild>
          <a href="#">
            Buy now
          </a>
        </Button>

        <Button size="xl" variant="outlinePrimary">
          <a href="#">Read more</a>
        </Button>

        <LandingSocialProof
          className="w-full mt-12"
          showRating
          numberOfUsers={12000}
          avatarItems={[
            {
              imageSrc: '/static/images/people/1.webp',
              name: 'John Doe',
            },
            {
              imageSrc: '/static/images/people/2.webp',
              name: 'Jane Doe',
            },
            {
              imageSrc: '/static/images/people/3.webp',
              name: 'Alice Doe',
            },
          ]}
        />
      </LandingPrimaryImageCtaSection>
    </>

}

>

```jsx
import { Button } from '@/components/shared/ui/button';
import { LandingPrimaryImageCtaSection } from '@/components/landing/cta/LandingPrimaryCta';
import { LandingDiscount } from '@/components/landing/discount/LandingDiscount';
import { LandingProductHuntAward } from '@/components/landing/social-proof/LandingProductHuntAward';
import { LandingSocialProof } from '@/components/landing/social-proof/LandingSocialProof';

<LandingPrimaryImageCtaSection
  title="Landing page in minutes"
  description="Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back."
  imageSrc="/static/images/shipixen/product/1.webp"
  imageAlt="Sample image"
  withBackground
  withBackgroundGlow
  leadingComponent={<LandingProductHuntAward />}
>
  <Button size="xl" asChild>
    <a href="#">Buy now</a>
  </Button>

  <Button size="xl" variant="outlinePrimary">
    <a href="#">Read more</a>
  </Button>

  <LandingSocialProof
    className="w-full mt-12"
    showRating
    numberOfUsers={99}
    avatarItems={[
      {
        imageSrc: '/static/images/people/1.webp',
        name: 'John Doe',
      },
      {
        imageSrc: '/static/images/people/2.webp',
        name: 'Jane Doe',
      },
      {
        imageSrc: '/static/images/people/3.webp',
        name: 'Alice Doe',
      },
    ]}
  />
</LandingPrimaryImageCtaSection>;
```

</ComponentExample>

## API Reference

| Prop Name                                                                                                     | Prop Type           | Required | Default       |
| ------------------------------------------------------------------------------------------------------------- | ------------------- | -------- | ------------- |
| **avatarItems** <Tippy>Array of objects representing social proof items with avatar images and names.</Tippy> | `SocialProofItem[]` | Yes      | -             |
| **numberOfUsers** <Tippy>Number of users displayed as social proof.</Tippy>                                   | `number`            | Yes      | -             |
| **suffixText** <Tippy>Text to be appended after the number of users, e.g., 'happy users'.</Tippy>             | `string`            | No       | 'happy users' |
| **showRating** <Tippy>Boolean to determine whether to display the rating.</Tippy>                             | `boolean`           | No       | -             |
| **disableAnimation** <Tippy>Boolean to disable animation.</Tippy>                                             | `boolean`           | No       | -             |

```ts
export interface SocialProofItem {
  imageSrc: string;
  name: string;
}
```

## More Examples

For more even more examples, see our <a href="/demo/landing-page-component-examples" className="fancy-link">Landing Page Component Examples</a> page or see complete landing page examples by <a href="/demo/landing-page-templates" className="fancy-link">Exploring Our Landing Page Templates</a>.

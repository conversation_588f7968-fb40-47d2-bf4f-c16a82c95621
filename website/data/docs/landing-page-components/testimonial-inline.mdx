---
title: 'Landing Page Testimonial Inline Component'
date: '2024-04-04'
lastmod: '2024-07-06'
tags:
  - 'guide'
  - 'components'
  - 'documentation'
  - 'landing-page'
  - 'ui-library'
  - 'react'
  - 'nextjs'
  - 'tailwind-css'
  - 'testimonial'
  - 'shadcn-ui'
  - 'shadcn-ui-testimonial'
  - 'shadcn-ui-landing-page'

images:
  - '/static/images/blog/docs/landing-page-testimonial-inline-component.webp'

summary: 'A component meant to be used in the landing page. It displays a grid of short testimonials. Use this to highlight short customer testimonials or reviews. These are not meant for reviews, but short validation and are usually support for a primary or secondary Call to action.'
layout: PostHub

showThemeSelector: true
showInstallNotice: true

canonicalUrl: 'https://shipixen.com/boilerplate-documentation/landing-page-components/testimonial-inline'
---

Use this to highlight short customer testimonials or reviews. These are not meant for reviews, but short validation and are usually support for a primary or secondary Call to action.

It displays an inline grid of short testimonials.

<ComponentExample
  previewComponent={
    <LandingTestimonialInline>
      <LandingTestimonialInlineItem
        imageSrc="https://picsum.photos/id/64/100/100"
        name="John Doe"
        text="I love this app"
      />

      <LandingTestimonialInlineItem
        imageSrc="https://picsum.photos/id/65/100/100"
        name="Jane Doe"
        text="Best app ever"
      />

      <LandingTestimonialInlineItem
        imageSrc="https://picsum.photos/id/669/100/100"
        name="Alice Doe"
        text="Fantastic"
        suffix="CEO of Instagram"
      />

      <LandingTestimonialInlineItem
        imageSrc="https://picsum.photos/id/829/100/100"
        name="Guido Ross"
        text="Recommended"
        suffix="DevOps at Meta"
      />
    </LandingTestimonialInline>

}

>

```jsx
import { LandingTestimonialInline } from '@/components/landing/testimonial/LandingTestimonialInline';
import { LandingTestimonialInlineItem } from '@/components/landing/testimonial/LandingTestimonialInlineItem';

<LandingTestimonialInline>
  <LandingTestimonialInlineItem
    imageSrc="https://picsum.photos/id/64/100/100"
    name="John Doe"
    text="I love this app"
  />

  <LandingTestimonialInlineItem
    imageSrc="https://picsum.photos/id/65/100/100"
    name="Jane Doe"
    text="Best app ever"
  />

  <LandingTestimonialInlineItem
    imageSrc="https://picsum.photos/id/669/100/100"
    name="Alice Doe"
    text="Fantastic"
    suffix="CEO of Instagram"
  />

  <LandingTestimonialInlineItem
    imageSrc="https://picsum.photos/id/829/100/100"
    name="Guido Ross"
    text="Recommended"
    suffix="DevOps at Meta"
  />
</LandingTestimonialInline>;
```

</ComponentExample>

## Usage

```jsx
import { LandingTestimonialInline } from '@/components/landing/testimonial/LandingTestimonialInline';
import { LandingTestimonialInlineItem } from '@/components/landing/testimonial/LandingTestimonialInlineItem';
```

```jsx
<LandingTestimonialInline>
  <LandingTestimonialInlineItem
    imageSrc="https://picsum.photos/id/64/100/100"
    name="John Doe"
    text="I love this app"
  />

  <LandingTestimonialInlineItem
    imageSrc="https://picsum.photos/id/65/100/100"
    name="Jane Doe"
    text="Best app ever"
  />

  <LandingTestimonialInlineItem
    imageSrc="https://picsum.photos/id/669/100/100"
    name="Alice Doe"
    text="Fantastic"
    suffix="CEO of Instagram"
  />

  <LandingTestimonialInlineItem
    imageSrc="https://picsum.photos/id/829/100/100"
    name="Guido Ross"
    text="Recommended"
    suffix="DevOps at Meta"
  />
</LandingTestimonialInline>
```

## Examples

### Background Color

<ComponentExample
  previewComponent={
    <LandingTestimonialInline
      withBackground
      variant="secondary"
    >
      <LandingTestimonialInlineItem
        imageSrc="https://picsum.photos/id/64/100/100"
        name="John Doe"
        text="I love this app"
      />

      <LandingTestimonialInlineItem
        imageSrc="https://picsum.photos/id/65/100/100"
        name="Jane Doe"
        text="Best app on the market"
      />

      <LandingTestimonialInlineItem
        imageSrc="https://picsum.photos/id/669/100/100"
        name="Alice Doe"
        text="Never seen anything like it"
        suffix="CEO of Instagram"
      />

      <LandingTestimonialInlineItem
        imageSrc="https://picsum.photos/id/829/100/100"
        name="Guido Ross"
        text="Nothing comes close to it"
        suffix="DevOps at Meta"
      />
    </LandingTestimonialInline>

}

>

```jsx
import { LandingTestimonialInline } from '@/components/landing/testimonial/LandingTestimonialInline';
import { LandingTestimonialInlineItem } from '@/components/landing/testimonial/LandingTestimonialInlineItem';

<LandingTestimonialInline withBackground variant="secondary">
  <LandingTestimonialInlineItem
    imageSrc="https://picsum.photos/id/64/100/100"
    name="John Doe"
    text="I love this app"
  />

  <LandingTestimonialInlineItem
    imageSrc="https://picsum.photos/id/65/100/100"
    name="Jane Doe"
    text="Best app on the market"
  />

  <LandingTestimonialInlineItem
    imageSrc="https://picsum.photos/id/669/100/100"
    name="Alice Doe"
    text="Never seen anything like it"
    suffix="CEO of Instagram"
  />

  <LandingTestimonialInlineItem
    imageSrc="https://picsum.photos/id/829/100/100"
    name="Guido Ross"
    text="Nothing comes close to it"
    suffix="DevOps at Meta"
  />
</LandingTestimonialInline>;
```

</ComponentExample>

### With Primary CTA

<ComponentExample
  previewComponent={
    <LandingPrimaryImageCtaSection
      title="Beautiful landing pages in minutes"
      description="Get your landing page up and running with a few clicks."
      imageSrc="/static/images/product-sample.webp"
      imageAlt="Sample image"
      withBackground
      footerComponent={
        <LandingTestimonialInline>
          <LandingTestimonialInlineItem
            imageSrc="https://picsum.photos/id/64/100/100"
            name="John Doe"
            text="I love this app"
          />

          <LandingTestimonialInlineItem
            imageSrc="https://picsum.photos/id/65/100/100"
            name="Jane Doe"
            text="Best app on the market"
          />

          <LandingTestimonialInlineItem
            imageSrc="https://picsum.photos/id/669/100/100"
            name="Alice Doe"
            text="Never seen anything like it"
            suffix="CEO of Instagram"
          />

          <LandingTestimonialInlineItem
            imageSrc="https://picsum.photos/id/829/100/100"
            name="Guido Ross"
            text="Nothing comes close to it"
            suffix="DevOps at Meta"
          />
        </LandingTestimonialInline>
      }
    >
      <Button size="xl" asChild>
        <a href="#">Sign up</a>
      </Button>

      <Button size="xl" variant="outlinePrimary">
        <a href="#">See demo</a>
      </Button>
    </LandingPrimaryImageCtaSection>

}

>

```jsx
import { LandingTestimonialInline } from '@/components/landing/testimonial/LandingTestimonialInline';
import { LandingTestimonialInlineItem } from '@/components/landing/testimonial/LandingTestimonialInlineItem';
import { LandingPrimaryImageCtaSection } from '@/components/landing/cta/LandingPrimaryCta';

<LandingPrimaryImageCtaSection
  title="Beautiful landing pages in minutes"
  description="Get your landing page up and running with a few clicks."
  imageSrc="/static/images/product-sample.webp"
  imageAlt="Sample image"
  withBackground
  footerComponent={
    <LandingTestimonialInline>
      <LandingTestimonialInlineItem
        imageSrc="https://picsum.photos/id/64/100/100"
        name="John Doe"
        text="I love this app"
      />

      <LandingTestimonialInlineItem
        imageSrc="https://picsum.photos/id/65/100/100"
        name="Jane Doe"
        text="Best app on the market"
      />

      <LandingTestimonialInlineItem
        imageSrc="https://picsum.photos/id/669/100/100"
        name="Alice Doe"
        text="Never seen anything like it"
        suffix="CEO of Instagram"
      />

      <LandingTestimonialInlineItem
        imageSrc="https://picsum.photos/id/829/100/100"
        name="Guido Ross"
        text="Nothing comes close to it"
        suffix="DevOps at Meta"
      />
    </LandingTestimonialInline>
  }
>
  <Button size="xl" asChild>
    <a href="#">Sign up</a>
  </Button>

  <Button size="xl" variant="outlinePrimary">
    <a href="#">See demo</a>
  </Button>
</LandingPrimaryImageCtaSection>;
```

</ComponentExample>

## API Reference

| Prop Name                                                                                                                             | Prop Type                             | Required | Default       |
| ------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------- | -------- | ------------- |
| **children** <Tippy>React nodes to be rendered within the component (below the text on smaller screen, right side on larger).</Tippy> | `React.ReactNode` ǀ `string`          | No       | -             |
| **withBackground** <Tippy>Boolean indicating whether to display with a background or not.</Tippy>                                     | `boolean`                             | No       | `false`       |
| **variant** <Tippy>String defining the variant of the component. Possible values are 'primary' or 'secondary'.</Tippy>                | `'primary'` ǀ `'secondary'`           | No       | `'primary'`   |
| **containerType** <Tippy>String defining the type of container. Possible values are 'narrow', 'wide', or 'ultrawide'.</Tippy>         | `'narrow'` ǀ `'wide'` ǀ `'ultrawide'` | No       | `'ultrawide'` |

## More Examples

For more even more examples, see our <a href="/demo/landing-page-component-examples" className="fancy-link">Landing Page Component Examples</a> page or see complete landing page examples by <a href="/demo/landing-page-templates" className="fancy-link">Exploring Our Landing Page Templates</a>.

---
title: 'Landing Page Testimonial'
date: '2023-11-19'
lastmod: '2024-07-06'
tags:
  - 'guide'
  - 'components'
  - 'documentation'
  - 'landing-page'
  - 'ui-library'
  - 'react'
  - 'nextjs'
  - 'tailwind-css'
  - 'testimonial'
  - 'shadcn-ui'
  - 'shadcn-ui-testimonial'
  - 'shadcn-ui-landing-page'

images:
  - '/static/images/blog/docs/landing-page-testimonials-component.webp'
summary: 'This component is used to display a single testimonial. It has text, a name, and a picture of the person. '
layout: PostHub

showThemeSelector: true
showInstallNotice: true

canonicalUrl: 'https://shipixen.com/boilerplate-documentation/landing-page-components/testimonial'
---

Use this component to display a single testimonial. It has text, a name, and a picture of the person. This is used as part of [Testimonial Lists](/boilerplate-documentation/landing-page-components/testimonial-list) and [Testimonial Grids](/boilerplate-documentation/landing-page-components/testimonial-grid).

<ComponentExample
  previewComponent={
    <LandingTestimonial
      name={'<PERSON>'}
      text={'Excellent product. I love it!'}
      handle={'@mandy'}
      imageSrc={'https://picsum.photos/100/100.webp?random=3'}
      hideFooter
    />}
>

```jsx
<LandingTestimonial
  name={'Mandy'}
  text={'Excellent product. I love it!'}
  handle={'@mandy'}
  imageSrc={'https://picsum.photos/100/100.webp?random=3'}
  hideFooter
/>
```

</ComponentExample>

## Usage

```jsx
import { LandingTestimonial } from '@/components/landing/testimonial/LandingTestimonial';
```

```
<LandingTestimonial
  name={'Mandy'}
  text={'Excellent product. I love it!'}
  handle={'@mandy'}
  imageSrc={'https://picsum.photos/100/100.webp?random=3'}
  hideFooter
/>
```

## Examples

### With Testimonial Grid

<ComponentExample previewComponent={
  <LandingTestimonialGrid
    title="Don't take it from us"
    description="See what other people have to say."
    testimonialItems={[
      {
        name: 'Mathew',
        text: 'After using this, I cannot imagine going back to the old way of doing things.',
        handle: '@heymatt_oo',
        imageSrc: 'https://picsum.photos/100/100.webp?random=2',
      },
      {
        name: 'Joshua',
        text: 'Perfect for my use case',
        handle: '@joshua',
        imageSrc: 'https://picsum.photos/100/100.webp?random=3',
      },
      {
        name: 'Parl Coppa',
        text: 'This is the best thing since sliced bread. I cannot believe I did not think of it myself.',
        handle: '@coppalipse',
        imageSrc: 'https://picsum.photos/100/100.webp?random=1',
        featured: true, // Feature this testimonial
      },
      {
        name: 'Mandy',
        text: 'Excellent product!',
        handle: '@mandy',
        imageSrc: 'https://picsum.photos/100/100.webp?random=4',
      },
      {
        name: 'Alex',
        text: 'Can easily recommend!',
        handle: '@alex',
        imageSrc: 'https://picsum.photos/100/100.webp?random=5',
      },
      {
        name: 'Sam',
        text: 'I am very happy with the results.',
        handle: '@sama',
        imageSrc: 'https://picsum.photos/100/100.webp?random=6',
      }
    ]}
/>}>

```jsx
import { LandingTestimonialGrid } from '@/components/landing/testimonial/LandingTestimonialGrid';

const testimonialItems = [
  {
    name: 'Mathew',
    text: 'After using this, I cannot imagine going back to the old way of doing things.',
    handle: '@heymatt_oo',
    imageSrc: 'https://picsum.photos/100/100.webp?random=2',
  },
  {
    name: 'Joshua',
    text: 'Perfect for my use case',
    handle: '@joshua',
    imageSrc: 'https://picsum.photos/100/100.webp?random=3',
  },
  {
    name: 'Parl Coppa',
    text: 'This is the best thing since sliced bread. I cannot believe I did not think of it myself.',
    handle: '@coppalipse',
    imageSrc: 'https://picsum.photos/100/100.webp?random=1',
    featured: true, // Feature this testimonial
  },
  {
    name: 'Mandy',
    text: 'Excellent product!',
    handle: '@mandy',
    imageSrc: 'https://picsum.photos/100/100.webp?random=4',
  },
  {
    name: 'Alex',
    text: 'Can easily recommend!',
    handle: '@alex',
    imageSrc: 'https://picsum.photos/100/100.webp?random=5',
  },
  {
    name: 'Sam',
    text: 'I am very happy with the results.',
    handle: '@sama',
    imageSrc: 'https://picsum.photos/100/100.webp?random=6',
  },
];

<LandingTestimonialGrid
  title="Don't take it from us"
  description="See what other people have to say."
  testimonialItems={testimonialItems}
/>;
```

</ComponentExample>

### With Testimonial List

<ComponentExample previewComponent={
  <LandingTestimonialListSection
    title="Don't take it from us"
    description="See what other people have to say."
    testimonialItems={[
      {
        name: 'Parl Coppa',
        text: 'This is the best thing since sliced bread. I cannot believe I did not think of it myself.',
        handle: '@coppalipse',
        imageSrc: 'https://picsum.photos/100/100.webp?random=1',
        size: 'half',
      },
        {
        name: 'Mathew',
        text: 'After using this, I cannot imagine going back to the old way of doing things.',
        handle: '@heymatt_oo',
        imageSrc: 'https://picsum.photos/100/100.webp?random=2',
        size: 'half',
      },
      {
        name: 'Joshua',
        text: 'Perfect for my use case',
        handle: '@joshua',
        imageSrc: 'https://picsum.photos/100/100.webp?random=3',
        size: 'third'
      },
      {
        name: 'Mandy',
        text: 'Excellent product!',
        handle: '@mandy',
        imageSrc: 'https://picsum.photos/100/100.webp?random=4',
        size: 'third'
      },
      {
        name: 'Alex',
        text: 'Can easily recommend!',
        handle: '@alex',
        imageSrc: 'https://picsum.photos/100/100.webp?random=5',
        size: 'third'
      }
    ]}
  />}>

```jsx
import { LandingTestimonialListSection } from '@/components/landing/testimonial/LandingTestimonialList';

const testimonialItems = [
  {
    name: 'Parl Coppa',
    text: 'This is the best thing since sliced bread. I cannot believe I did not think of it myself.',
    handle: '@coppalipse',
    imageSrc: 'https://picsum.photos/100/100.webp?random=1',
    size: 'half',
  },
  {
    name: 'Mathew',
    text: 'After using this, I cannot imagine going back to the old way of doing things.',
    handle: '@heymatt_oo',
    imageSrc: 'https://picsum.photos/100/100.webp?random=2',
    size: 'half',
  },
  {
    name: 'Joshua',
    text: 'Perfect for my use case',
    handle: '@joshua',
    imageSrc: 'https://picsum.photos/100/100.webp?random=3',
    size: 'third',
  },
  {
    name: 'Mandy',
    text: 'Excellent product!',
    handle: '@mandy',
    imageSrc: 'https://picsum.photos/100/100.webp?random=4',
    size: 'third',
  },
  {
    name: 'Alex',
    text: 'Can easily recommend!',
    handle: '@alex',
    imageSrc: 'https://picsum.photos/100/100.webp?random=5',
    size: 'third',
  },
]

<LandingTestimonialListSection
  title="Don't take it from us"
  description="See what other people have to say."
  testimonialItems={testimonialItems}
/>
```

</ComponentExample>

## API Reference

| Prop Name                                                                                                                               | Prop Type                       | Required | Default |
| --------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------- | -------- | ------- |
| **url** <Tippy>The URL to navigate to when the testimonial is clicked.</Tippy>                                                          | `string`                        | Yes      | -       |
| **text** <Tippy>The main text content of the testimonial.</Tippy>                                                                       | `string`                        | Yes      | -       |
| **imageSrc** <Tippy>The URL of the image to be displayed alongside the testimonial.</Tippy>                                             | `string`                        | Yes      | -       |
| **name** <Tippy>The name of the person providing the testimonial.</Tippy>                                                               | `string`                        | Yes      | -       |
| **handle** <Tippy>The handle or username associated with the person providing the testimonial.</Tippy>                                  | `string`                        | Yes      | -       |
| **featured** <Tippy>Whether the testimonial is featured or not.</Tippy>                                                                 | `boolean`                       | No       | -       |
| **verified** <Tippy>Whether the testimonial is verified or not.</Tippy>                                                                 | `boolean`                       | No       | `true`  |
| **size** <Tippy>The size of the testimonial (`full`, `half`, or `third`). NB: Only applies to testimonials in a list, not grid.</Tippy> | `'full'   ǀ 'half'   ǀ 'third'` | No       | -       |

## More Examples

For more even more examples, see our <a href="/demo/landing-page-component-examples" className="fancy-link">Landing Page Component Examples</a> page or see complete landing page examples by <a href="/demo/landing-page-templates" className="fancy-link">Exploring Our Landing Page Templates</a>.

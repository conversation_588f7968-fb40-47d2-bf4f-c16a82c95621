---
title: 'Landing Page Primary Text Call to Action (CTA) Component'
date: '2024-04-04'
lastmod: '2025-05-30'
tags:
  - 'guide'
  - 'components'
  - 'documentation'
  - 'landing-page'
  - 'ui-library'
  - 'react'
  - 'nextjs'
  - 'tailwind-css'
  - 'cta'
  - 'call-to-action'
  - 'shadcn-ui'
  - 'shadcn-ui-cta'
  - 'shadcn-ui-call-to-action'
  - 'shadcn-ui-landing-page'

images:
  - '/static/images/blog/docs/landing-page-primary-text-cta-component.webp'

summary: 'A component meant to be used in the landing page as the primary Call to Action section. A section that shows a title & description. Optionally, it can have actions (children) and a background.'
layout: PostHub

showThemeSelector: true
showInstallNotice: true

canonicalUrl: 'https://shipixen.com/boilerplate-documentation/landing-page-components/primary-text-cta'
---

Use this component on landing page as the primary Call to Action section. A section that shows a title & description. Optionally, it can have actions (children) and a background.

This is the most important section of your landing page. Use it to grab
the attention of your visitors and encourage them to take action.

<ComponentExample
  previewComponent={
    <LandingPrimaryTextCtaSection
      title="Landing page in minutes"
      description="Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back."
    >
      <Button size="xl" asChild>
        <a href="#">
          Buy now
        </a>
      </Button>

      <Button size="xl" variant="outlinePrimary">
        <a href="#">
          Read more
        </a>
      </Button>
    </LandingPrimaryTextCtaSection>

}

>

```jsx
import { Button } from '@/components/shared/ui/button';
import { LandingPrimaryTextCtaSection } from '@/components/landing/cta/LandingPrimaryCta';

<LandingPrimaryTextCtaSection
  title="Landing page in minutes"
  description="Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back."
>
  <Button size="xl" asChild>
    <a href="#">Buy now</a>
  </Button>

  <Button size="xl" variant="outlinePrimary">
    <a href="#">Read more</a>
  </Button>
</LandingPrimaryTextCtaSection>;
```

</ComponentExample>

## Usage

```jsx
import { Button } from '@/components/shared/ui/button';
import { LandingPrimaryTextCtaSection } from '@/components/landing/cta/LandingPrimaryCta';
```

```jsx
<LandingPrimaryTextCtaSection
  title="Landing page in minutes"
  description="Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back."
>
  <Button size="xl" asChild>
    <a href="#">Buy now</a>
  </Button>

  <Button size="xl" variant="outlinePrimary">
    <a href="#">Read more</a>
  </Button>
</LandingPrimaryTextCtaSection>
```

## Examples

### With Social Proof

<ComponentExample
  previewComponent={
    <LandingPrimaryTextCtaSection
      title="Landing page in minutes"
      description="Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back."
    >
      <Button size="xl" asChild>
        <a href="#">Buy now</a>
      </Button>

      <Button size="xl" variant="outlinePrimary">
        <a href="#">Read more</a>
      </Button>

      <LandingSocialProof
        className="mt-6 w-full flex justify-center"
        showRating
        numberOfUsers={99}
        avatarItems={[
          {
            imageSrc: '/static/images/people/1.webp',
            name: 'John Doe',
          },
          {
            imageSrc: '/static/images/people/2.webp',
            name: 'Jane Doe',
          },
          {
            imageSrc: '/static/images/people/3.webp',
            name: 'Alice Doe',
          },
        ]}
      />
    </LandingPrimaryTextCtaSection>

}

>

```jsx
import { Button } from '@/components/shared/ui/button';
import { LandingPrimaryTextCtaSection } from '@/components/landing/cta/LandingPrimaryCta';
import { LandingSocialProof } from '@/components/landing/social-proof/LandingSocialProof';

<LandingPrimaryTextCtaSection
  title="Landing page in minutes"
  description="Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back."
>
  <Button size="xl" asChild>
    <a href="#">Buy now</a>
  </Button>

  <Button size="xl" variant="outlinePrimary">
    <a href="#">Read more</a>
  </Button>

  <LandingSocialProof
    className="mt-6 w-full flex justify-center"
    showRating
    numberOfUsers={99}
    avatarItems={[
      {
        imageSrc: '/static/images/people/1.webp',
        name: 'John Doe',
      },
      {
        imageSrc: '/static/images/people/2.webp',
        name: 'Jane Doe',
      },
      {
        imageSrc: '/static/images/people/3.webp',
        name: 'Alice Doe',
      },
    ]}
  />
</LandingPrimaryTextCtaSection>;
```

</ComponentExample>

### With Discount/Offer

<ComponentExample
  previewComponent={
    <LandingPrimaryTextCtaSection
      title="Landing page in minutes"
      description="Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back."
    >
      <Button size="xl" asChild>
        <a href="#">Buy now</a>
      </Button>

      <Button size="xl" variant="outlinePrimary">
        <a href="#">Read more</a>
      </Button>

      <LandingDiscount
        className="w-full flex justify-center"
        discountValueText="$350 off"
        discountDescriptionText="for the first 10 customers (2 left)"
      />
    </LandingPrimaryTextCtaSection>

}

>

```jsx
import { Button } from '@/components/shared/ui/button';
import { LandingPrimaryTextCtaSection } from '@/components/landing/cta/LandingPrimaryCta';
import { LandingDiscount } from '@/components/landing/discount/LandingDiscount';

<LandingPrimaryTextCtaSection
  title="Landing page in minutes"
  description="Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back."
>
  <Button size="xl" asChild>
    <a href="#">Buy now</a>
  </Button>

  <Button size="xl" variant="outlinePrimary">
    <a href="#">Read more</a>
  </Button>

  <LandingDiscount
    className="w-full flex justify-center"
    discountValueText="$350 off"
    discountDescriptionText="for the first 10 customers (2 left)"
  />
</LandingPrimaryTextCtaSection>;
```

</ComponentExample>

### With Discount & Left Alignment

<ComponentExample
  previewComponent={
    <LandingPrimaryTextCtaSection
      title="Landing page in minutes"
      description="Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back."
      textPosition="left"
    >
      <Button size="xl" asChild>
        <a href="#">
          Buy now
        </a>
      </Button>

      <Button size="xl" variant="outlinePrimary">
        <a href="#">Read more</a>
      </Button>

      <LandingDiscount
        className="w-full"
        discountValueText="$350 off"
        discountDescriptionText="for the first 10 customers (2 left)"
      />
    </LandingPrimaryTextCtaSection>

}

>

```jsx
import { Button } from '@/components/shared/ui/button';
import { LandingPrimaryTextCtaSection } from '@/components/landing/cta/LandingPrimaryCta';
import { LandingDiscount } from '@/components/landing/discount/LandingDiscount';

<LandingPrimaryTextCtaSection
  title="Landing page in minutes"
  description="Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back."
  textPosition="left"
>
  <Button size="xl" asChild>
    <a href="#">Buy now</a>
  </Button>

  <Button size="xl" variant="outlinePrimary">
    <a href="#">Read more</a>
  </Button>

  <LandingDiscount
    className="w-full"
    discountValueText="$350 off"
    discountDescriptionText="for the first 10 customers (2 left)"
  />
</LandingPrimaryTextCtaSection>;
```

</ComponentExample>

### With Bullet Points

<ComponentExample
  previewComponent={
    <LandingPrimaryTextCtaSection
      title="Landing page in minutes"
      descriptionComponent={
        <LandingProductFeatureKeyPoints
          keyPoints={[
            {
              title: 'Intelligent Assistance',
              description:
                'Receive personalized recommendations and insights tailored to your workflow.',
            },
            {
              title: 'Seamless Collaboration',
              description:
                'Easily collaborate with team members and clients in real-time.',
            },
            {
              title: 'Advanced Customization',
              description:
                'Tailor your app to fit your unique requirements with extensive customization.',
            },
          ]}
        />
      }
      textPosition="left"
    >
      <Button size="xl" asChild>
        <a href="#">
          Buy now
        </a>
      </Button>

      <Button size="xl" variant="outlinePrimary">
        <a href="#">Read more</a>
      </Button>

      <LandingDiscount
        className="w-full"
        discountValueText="$350 off"
        discountDescriptionText="for the first 10 customers (2 left)"
      />
    </LandingPrimaryTextCtaSection>

}

>

```jsx
import { Button } from '@/components/shared/ui/button';
import { LandingPrimaryTextCtaSection } from '@/components/landing/cta/LandingPrimaryCta';
import { LandingDiscount } from '@/components/landing/discount/LandingDiscount';
import { LandingProductFeatureKeyPoints } from '@/components/landing/LandingProductFeatureKeyPoints';

<LandingPrimaryTextCtaSection
  title="Landing page in minutes"
  descriptionComponent={
    <LandingProductFeatureKeyPoints
      keyPoints={[
        {
          title: 'Intelligent Assistance',
          description:
            'Receive personalized recommendations and insights tailored to your workflow.',
        },
        {
          title: 'Seamless Collaboration',
          description:
            'Easily collaborate with team members and clients in real-time.',
        },
        {
          title: 'Advanced Customization',
          description:
            'Tailor your app to fit your unique requirements with extensive customization.',
        },
      ]}
    />
  }
  textPosition="left"
>
  <Button size="xl" asChild>
    <a href="#">Buy now</a>
  </Button>

  <Button size="xl" variant="outlinePrimary">
    <a href="#">Read more</a>
  </Button>

  <LandingDiscount
    className="w-full"
    discountValueText="$350 off"
    discountDescriptionText="for the first 10 customers (2 left)"
  />
</LandingPrimaryTextCtaSection>;
```

</ComponentExample>

### With Product Hunt Award

<ComponentExample
  previewComponent={
    <LandingPrimaryTextCtaSection
      title="Landing page in minutes"
      description="Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back."
      textPosition="left"
      leadingComponent={<LandingProductHuntAward />}
    >
      <Button size="xl" asChild>
        <a href="#">
          Buy now
        </a>
      </Button>

      <Button size="xl" variant="outlinePrimary">
        <a href="#">Read more</a>
      </Button>

      <LandingDiscount
        className="w-full"
        discountValueText="$350 off"
        discountDescriptionText="for the first 10 customers (2 left)"
      />
    </LandingPrimaryTextCtaSection>

}

>

```jsx
import { Button } from '@/components/shared/ui/button';
import { LandingPrimaryTextCtaSection } from '@/components/landing/cta/LandingPrimaryCta';
import { LandingDiscount } from '@/components/landing/discount/LandingDiscount';
import { LandingProductHuntAward } from '@/components/landing/social-proof/LandingProductHuntAward';

<LandingPrimaryTextCtaSection
  title="Landing page in minutes"
  description="Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back."
  textPosition="left"
  leadingComponent={<LandingProductHuntAward />}
>
  <Button size="xl" asChild>
    <a href="#">Buy now</a>
  </Button>

  <Button size="xl" variant="outlinePrimary">
    <a href="#">Read more</a>
  </Button>

  <LandingDiscount
    className="w-full"
    discountValueText="$350 off"
    discountDescriptionText="for the first 10 customers (2 left)"
  />
</LandingPrimaryTextCtaSection>;
```

</ComponentExample>

### With Social Proof Band

<ComponentExample
  previewComponent={
    <>
      <LandingSocialProofBand>
        <LandingSocialProofBandItem>
          100% encrypted and secure
        </LandingSocialProofBandItem>

        <LandingSocialProofBandItem>24/7 customer support</LandingSocialProofBandItem>

        <LandingSocialProofBandItem>
          99% customer satisfaction
        </LandingSocialProofBandItem>
      </LandingSocialProofBand>

      <LandingPrimaryTextCtaSection
        title="Landing page in minutes"
        description="Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back."
        textPosition="left"
        leadingComponent={<LandingProductHuntAward />}
      >
        <Button size="xl" asChild>
          <a href="#">
            Buy now
          </a>
        </Button>

        <Button size="xl" variant="outlinePrimary">
          <a href="#">Read more</a>
        </Button>

        <LandingDiscount
          className="w-full"
          discountValueText="$350 off"
          discountDescriptionText="for the first 10 customers (2 left)"
        />
      </LandingPrimaryTextCtaSection>
    </>

}

>

```jsx
import { Button } from '@/components/shared/ui/button';
import { LandingPrimaryTextCtaSection } from '@/components/landing/cta/LandingPrimaryCta';
import { LandingDiscount } from '@/components/landing/discount/LandingDiscount';
import { LandingProductHuntAward } from '@/components/landing/social-proof/LandingProductHuntAward';
import { LandingSocialProofBand } from '@/components/landing/social-proof/LandingSocialProofBand';
import { LandingSocialProofBandItem } from '@/components/landing/social-proof/LandingSocialProofBandItem';

<LandingSocialProofBand>
  <LandingSocialProofBandItem>
    100% encrypted and secure
  </LandingSocialProofBandItem>

  <LandingSocialProofBandItem>24/7 customer support</LandingSocialProofBandItem>

  <LandingSocialProofBandItem>
    99% customer satisfaction
  </LandingSocialProofBandItem>
</LandingSocialProofBand>

<LandingPrimaryTextCtaSection
  title="Landing page in minutes"
  description="Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back."
  textPosition="left"
  leadingComponent={<LandingProductHuntAward />}
>
  <Button size="xl" asChild>
    <a href="#">
      Buy now
    </a>
  </Button>

  <Button size="xl" variant="outlinePrimary">
    <a href="#">Read more</a>
  </Button>

  <LandingDiscount
    className="w-full"
    discountValueText="$350 off"
    discountDescriptionText="for the first 10 customers (2 left)"
  />
</LandingPrimaryTextCtaSection>
```

</ComponentExample>

### With Background

<ComponentExample
  previewComponent={
    <LandingPrimaryTextCtaSection
      title="Landing page in minutes"
      description="Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back."
      textPosition="left"
      withBackground
      variant="secondary"
      leadingComponent={<LandingProductHuntAward />}
    >
      <Button size="xl" variant="secondary" asChild>
        <a href="#">
          Buy now
        </a>
      </Button>

      <Button size="xl" variant="outlineSecondary">
        <a href="#">Read more</a>
      </Button>

      <LandingDiscount
        className="w-full"
        discountValueText="$350 off"
        discountDescriptionText="for the first 10 customers (2 left)"
      />
    </LandingPrimaryTextCtaSection>

}

>

```jsx
import { Button } from '@/components/shared/ui/button';
import { LandingPrimaryTextCtaSection } from '@/components/landing/cta/LandingPrimaryCta';
import { LandingDiscount } from '@/components/landing/discount/LandingDiscount';
import { LandingProductHuntAward } from '@/components/landing/social-proof/LandingProductHuntAward';

<LandingPrimaryTextCtaSection
  title="Landing page in minutes"
  description="Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back."
  textPosition="left"
  withBackground
  variant="secondary"
  leadingComponent={<LandingProductHuntAward />}
>
  <Button size="xl" variant="secondary" asChild>
    <a href="#">Buy now</a>
  </Button>

  <Button size="xl" variant="outlineSecondary">
    <a href="#">Read more</a>
  </Button>

  <LandingDiscount
    className="w-full"
    discountValueText="$350 off"
    discountDescriptionText="for the first 10 customers (2 left)"
  />
</LandingPrimaryTextCtaSection>;
```

</ComponentExample>

### Left-aligned full example

<ComponentExample
  previewComponent={
    <>
      <LandingSocialProofBand>
        <LandingSocialProofBandItem>
          100% encrypted and secure
        </LandingSocialProofBandItem>

        <LandingSocialProofBandItem>24/7 customer support</LandingSocialProofBandItem>

        <LandingSocialProofBandItem>
          99% customer satisfaction
        </LandingSocialProofBandItem>
      </LandingSocialProofBand>

      <LandingPrimaryTextCtaSection
        title="Landing page in minutes"
        description="Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back."
        textPosition="left"
        withBackground
        withBackgroundGlow
        leadingComponent={<LandingProductHuntAward />}
      >
        <Button size="xl" asChild>
          <a href="#">
            Buy now
          </a>
        </Button>

        <Button size="xl" variant="outlinePrimary">
          <a href="#">Read more</a>
        </Button>

        <LandingSocialProof
          className="w-full mt-12"
          showRating
          numberOfUsers={99}
          avatarItems={[
            {
              imageSrc: '/static/images/people/1.webp',
              name: 'John Doe',
            },
            {
              imageSrc: '/static/images/people/2.webp',
              name: 'Jane Doe',
            },
            {
              imageSrc: '/static/images/people/3.webp',
              name: 'Alice Doe',
            },
          ]}
        />
      </LandingPrimaryTextCtaSection>
    </>

}

>

```jsx
import { Button } from '@/components/shared/ui/button';
import { LandingPrimaryTextCtaSection } from '@/components/landing/cta/LandingPrimaryCta';
import { LandingDiscount } from '@/components/landing/discount/LandingDiscount';
import { LandingProductHuntAward } from '@/components/landing/social-proof/LandingProductHuntAward';
import { LandingSocialProof } from '@/components/landing/social-proof/LandingSocialProof';
import { LandingSocialProofBand } from '@/components/landing/social-proof/LandingSocialProofBand';
import { LandingSocialProofBandItem } from '@/components/landing/social-proof/LandingSocialProofBandItem';

<LandingSocialProofBand>
  <LandingSocialProofBandItem>
    100% encrypted and secure
  </LandingSocialProofBandItem>

  <LandingSocialProofBandItem>24/7 customer support</LandingSocialProofBandItem>

  <LandingSocialProofBandItem>
    99% customer satisfaction
  </LandingSocialProofBandItem>
</LandingSocialProofBand>

<LandingPrimaryTextCtaSection
  title="Landing page in minutes"
  description="Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back."
  textPosition="left"
  withBackground
  withBackgroundGlow
  leadingComponent={<LandingProductHuntAward />}
>
  <Button size="xl" asChild>
    <a href="#">
      Buy now
    </a>
  </Button>

  <Button size="xl" variant="outlinePrimary">
    <a href="#">Read more</a>
  </Button>

  <LandingSocialProof
    className="w-full mt-12"
    showRating
    numberOfUsers={99}
    avatarItems={[
      {
        imageSrc: '/static/images/people/1.webp',
        name: 'John Doe',
      },
      {
        imageSrc: '/static/images/people/2.webp',
        name: 'Jane Doe',
      },
      {
        imageSrc: '/static/images/people/3.webp',
        name: 'Alice Doe',
      },
    ]}
  />
</LandingPrimaryTextCtaSection>
```

</ComponentExample>

### Centered full example

<ComponentExample
  previewComponent={
    <>
      <LandingSocialProofBand invert>
        <LandingSocialProofBandItem>
          100% encrypted and secure
        </LandingSocialProofBandItem>

        <LandingSocialProofBandItem>24/7 customer support</LandingSocialProofBandItem>

        <LandingSocialProofBandItem>
          99% customer satisfaction
        </LandingSocialProofBandItem>
      </LandingSocialProofBand>

      <LandingPrimaryTextCtaSection
        title="Landing page in minutes"
        description="Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back."
        withBackground
        withBackgroundGlow
        variant="secondary"
        backgroundGlowVariant="secondary"
        leadingComponent={<LandingProductHuntAward />}
      >
        <Button size="xl" variant="secondary" asChild>
          <a href="#">
            Buy now
          </a>
        </Button>

        <Button size="xl" variant="outlineSecondary">
          <a href="#">Read more</a>
        </Button>

        <LandingDiscount
          className="w-full flex justify-center"
          discountValueText="$350 off"
          discountDescriptionText="for the first 10 customers (2 left)"
        />

        <LandingSocialProof
          className="mt-12 w-full flex justify-center"
          showRating
          numberOfUsers={99}
          avatarItems={[
            {
              imageSrc: '/static/images/people/1.webp',
              name: 'John Doe',
            },
            {
              imageSrc: '/static/images/people/2.webp',
              name: 'Jane Doe',
            },
            {
              imageSrc: '/static/images/people/3.webp',
              name: 'Alice Doe',
            },
          ]}
        />
      </LandingPrimaryTextCtaSection>
    </>

}

>

```jsx
import { Button } from '@/components/shared/ui/button';
import { LandingPrimaryTextCtaSection } from '@/components/landing/cta/LandingPrimaryCta';
import { LandingDiscount } from '@/components/landing/discount/LandingDiscount';
import { LandingProductHuntAward } from '@/components/landing/social-proof/LandingProductHuntAward';
import { LandingSocialProof } from '@/components/landing/social-proof/LandingSocialProof';
import { LandingSocialProofBand } from '@/components/landing/social-proof/LandingSocialProofBand';
import { LandingSocialProofBandItem } from '@/components/landing/social-proof/LandingSocialProofBandItem';

<LandingSocialProofBand invert>
  <LandingSocialProofBandItem>
    100% encrypted and secure
  </LandingSocialProofBandItem>

  <LandingSocialProofBandItem>24/7 customer support</LandingSocialProofBandItem>

  <LandingSocialProofBandItem>
    99% customer satisfaction
  </LandingSocialProofBandItem>
</LandingSocialProofBand>

<LandingPrimaryTextCtaSection
  title="Landing page in minutes"
  description="Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back."
  withBackground
  withBackgroundGlow
  variant="secondary"
  backgroundGlowVariant="secondary"
  leadingComponent={<LandingProductHuntAward />}
>
  <Button size="xl" variant="secondary" asChild>
    <a href="#">
      Buy now
    </a>
  </Button>

  <Button size="xl" variant="outlineSecondary">
    <a href="#">Read more</a>
  </Button>

  <LandingDiscount
    className="w-full flex justify-center"
    discountValueText="$350 off"
    discountDescriptionText="for the first 10 customers (2 left)"
  />

  <LandingSocialProof
    className="mt-12 w-full flex justify-center"
    showRating
    numberOfUsers={99}
    avatarItems={[
      {
        imageSrc: '/static/images/people/1.webp',
        name: 'John Doe',
      },
      {
        imageSrc: '/static/images/people/2.webp',
        name: 'Jane Doe',
      },
      {
        imageSrc: '/static/images/people/3.webp',
        name: 'Alice Doe',
      },
    ]}
  />
</LandingPrimaryTextCtaSection>
```

</ComponentExample>

### With Newsletter Form
<ComponentExample
  previewComponent={
    <LandingPrimaryTextCtaSection
      title="Sign up today"
      description="Never miss an update! Subscribe to our newsletter to get the latest announcements, news and exclusive offers."
    >
      <LandingNewsletterInput />
    </LandingPrimaryTextCtaSection>
}>

```jsx
import { Button } from '@/components/shared/ui/button';
import { LandingPrimaryTextCtaSection } from '@/components/landing/cta/LandingPrimaryCta';
import { LandingNewsletterInput } from '@/components/landing/newsletter/LandingNewsletterInput';

<LandingPrimaryTextCtaSection
  title="Sign up today"
  description="Never miss an update! Subscribe to our newsletter to get the latest announcements, news and exclusive offers."
>
  <LandingNewsletterInput />
</LandingPrimaryTextCtaSection>
```

</ComponentExample>

### With Background Effects

More effects are available in the <a href="/boilerplate-documentation/landing-page-components/primary-cta-effects" className="fancy-link">Primary CTA Background Effects</a> section.

<ComponentExample
  previewComponent={
    <LandingPrimaryTextCtaSection
      title="Revolutionary Product Launch"
      description="Experience the future with our cutting-edge solution. Built for modern teams who demand excellence and reliability."
      textPosition="center"
      effectComponent={<LandingDotParticleCtaBg />}
    >
      <Button size="xl" asChild>
        <a href="#">Start Free Trial</a>
      </Button>

      <Button size="xl" variant="outlineSecondary">
        <a href="#">Watch Demo</a>
      </Button>

      <LandingSocialProof
        className="mt-8 w-full flex justify-center"
        showRating
        numberOfUsers={1000}
        avatarItems={[
          {
            imageSrc: '/static/images/people/1.webp',
            name: 'John Doe',
          },
          {
            imageSrc: '/static/images/people/2.webp',
            name: 'Jane Smith',
          },
          {
            imageSrc: '/static/images/people/3.webp',
            name: 'Alex Johnson',
          },
        ]}
      />
    </LandingPrimaryTextCtaSection>
  }
>

```jsx
import { Button } from '@/components/shared/ui/button';
import { LandingPrimaryTextCtaSection } from '@/components/landing/cta/LandingPrimaryCta';
import { LandingDotParticleCtaBg } from '@/components/landing/cta-backgrounds/LandingDotParticleCtaBg';
import { LandingSocialProof } from '@/components/landing/social-proof/LandingSocialProof';

<LandingPrimaryTextCtaSection
  title="Revolutionary Product Launch"
  description="Experience the future with our cutting-edge solution. Built for modern teams who demand excellence and reliability."
  textPosition="center"
  effectComponent={<LandingDotParticleCtaBg />}
>
  <Button size="xl" asChild>
    <a href="#">Start Free Trial</a>
  </Button>

  <Button size="xl" variant="outlineSecondary">
    <a href="#">Watch Demo</a>
  </Button>

  <LandingSocialProof
    className="mt-8 w-full flex justify-center"
    showRating
    numberOfUsers={1000}
    avatarItems={[
      {
        imageSrc: '/static/images/people/1.webp',
        name: 'John Doe',
      },
      {
        imageSrc: '/static/images/people/2.webp',
        name: 'Jane Smith',
      },
      {
        imageSrc: '/static/images/people/3.webp',
        name: 'Alex Johnson',
      },
    ]}
  />
</LandingPrimaryTextCtaSection>
```

</ComponentExample>


## API Reference

| Prop Name                                                                                                                             | Prop Type                    | Required | Default     |
| ------------------------------------------------------------------------------------------------------------------------------------- | ---------------------------- | -------- | ----------- |
| **children** <Tippy>React nodes to be rendered within the component (below the text on smaller screen, right side on larger).</Tippy> | `React.ReactNode` ǀ `string` | No       | -           |
| **className** <Tippy>Additional CSS classes to apply to the component.</Tippy>                                                        | `string`                     | No       | -           |
| **innerClassName** <Tippy>Additional CSS classes to apply to the inner container of the component.</Tippy>                            | `string`                     | No       | -           |
| **title** <Tippy>The title text or element to be displayed.</Tippy>                                                                   | `string` ǀ `React.ReactNode` | No       | -           |
| **titleComponent** <Tippy>Custom React component to be used as the title.</Tippy>                                                     | `React.ReactNode`            | No       | -           |
| **description** <Tippy>The description text or element to be displayed.</Tippy>                                                       | `string` ǀ `React.ReactNode` | No       | -           |
| **descriptionComponent** <Tippy>Custom React component to be used as the description.</Tippy>                                         | `React.ReactNode`            | No       | -           |
| **leadingComponent** <Tippy>Custom React component to be displayed before the title and description.</Tippy>                          | `React.ReactNode`            | No       | -           |
| **footerComponent** <Tippy>Custom React component to be displayed as the footer of the section.</Tippy>                               | `React.ReactNode`            | No       | -           |
| **textPosition** <Tippy>The position of the text content within the section. Possible values: `'center'`, `'left'`.</Tippy>           | `'center'` ǀ `'left'`        | No       | `'center'`  |
| **withBackground** <Tippy>Determines whether to show the section with a background or not.</Tippy>                                    | `boolean`                    | No       | `false`     |
| **variant** <Tippy>The variant style of the section. Possible values: `'primary'`, `'secondary'`.</Tippy>                             | `'primary'` ǀ `'secondary'`  | No       | `'primary'` |
| **effectComponent** <Tippy>Custom React component to be displayed as the background effect.</Tippy>                                    | `React.ReactNode`            | No       | -           |
| **effectClassName** <Tippy>Additional CSS classes to apply to the background effect.</Tippy>                                        | `string`                     | No       | -           |

## More Examples

For more even more examples, see our <a href="/demo/landing-page-component-examples" className="fancy-link">Landing Page Component Examples</a> page or see complete landing page examples by <a href="/demo/landing-page-templates" className="fancy-link">Exploring Our Landing Page Templates</a>.

Also see:

- <a href="/boilerplate-documentation/landing-page-components/product-feature" className="fancy-link">Product Feature</a> component for highlighting solutions to these problems
- <a href="/boilerplate-documentation/landing-page-components/primary-image-cta" className="fancy-link">Primary Image Call to Action</a> component for directing users to solutions
- <a href="/boilerplate-documentation/landing-page-components/primary-video-cta" className="fancy-link">Primary Image Video Call to Action</a> component for directing users to solutions
- <a href="/boilerplate-documentation/landing-page-components/primary-cta-effects" className="fancy-link">Primary CTA Background Effects</a> component for adding visual interest to the primary CTA section

---
title: 'Landing Page Testimonial List Component'
date: '2023-11-19'
lastmod: '2024-07-06'
tags:
  - 'guide'
  - 'components'
  - 'documentation'
  - 'landing-page'
  - 'ui-library'
  - 'react'
  - 'nextjs'
  - 'tailwind-css'
  - 'testimonial'
  - 'shadcn-ui'
  - 'shadcn-ui-testimonial'
  - 'shadcn-ui-landing-page'

images:
  - '/static/images/blog/docs/landing-page-testimonials-component.webp'

summary: 'This component is used to display a list of testimonials. Each testimonial has text, a name, and a picture of the person. '
layout: PostHub

showThemeSelector: true
showInstallNotice: true

canonicalUrl: 'https://shipixen.com/boilerplate-documentation/landing-page-components/testimonial-list'
---

Use this component to display a list of testimonials. Each testimonial has text, a name, and a picture of the person.

Testimonials are a great way to show that other people have used your product and are happy with it. Consider adding it high up on your landing page.

Shorter testimonials can be made smaller by setting the `size` prop to `half` or `third`. The default size is `full`.

<ComponentExample previewComponent={
  <LandingTestimonialListSection
    title="Don't take it from us"
    description="See what other people have to say."
    testimonialItems={[
      {
        name: 'Parl Coppa',
        text: 'This is the best thing since sliced bread. I cannot believe I did not think of it myself.',
        handle: '@coppalipse',
        imageSrc: 'https://picsum.photos/100/100.webp?random=1',
        size: 'half',
      },
        {
        name: 'Mathew',
        text: 'After using this, I cannot imagine going back to the old way of doing things.',
        handle: '@heymatt_oo',
        imageSrc: 'https://picsum.photos/100/100.webp?random=2',
        size: 'half',
      },
      {
        name: 'Joshua',
        text: 'Perfect for my use case',
        handle: '@joshua',
        imageSrc: 'https://picsum.photos/100/100.webp?random=3',
        size: 'third'
      },
      {
        name: 'Mandy',
        text: 'Excellent product!',
        handle: '@mandy',
        imageSrc: 'https://picsum.photos/100/100.webp?random=4',
        size: 'third'
      },
      {
        name: 'Alex',
        text: 'Can easily recommend!',
        handle: '@alex',
        imageSrc: 'https://picsum.photos/100/100.webp?random=5',
        size: 'third'
      }
    ]}
  />}>

```jsx
import { LandingTestimonialListSection } from '@/components/landing/testimonial/LandingTestimonialList';

const testimonialItems = [
  {
    name: 'Parl Coppa',
    text: 'This is the best thing since sliced bread. I cannot believe I did not think of it myself.',
    handle: '@coppalipse',
    imageSrc: 'https://picsum.photos/100/100.webp?random=1',
    size: 'half',
  },
  {
    name: 'Mathew',
    text: 'After using this, I cannot imagine going back to the old way of doing things.',
    handle: '@heymatt_oo',
    imageSrc: 'https://picsum.photos/100/100.webp?random=2',
    size: 'half',
  },
  {
    name: 'Joshua',
    text: 'Perfect for my use case',
    handle: '@joshua',
    imageSrc: 'https://picsum.photos/100/100.webp?random=3',
    size: 'third',
  },
  {
    name: 'Mandy',
    text: 'Excellent product!',
    handle: '@mandy',
    imageSrc: 'https://picsum.photos/100/100.webp?random=4',
    size: 'third',
  },
  {
    name: 'Alex',
    text: 'Can easily recommend!',
    handle: '@alex',
    imageSrc: 'https://picsum.photos/100/100.webp?random=5',
    size: 'third',
  },
]

<LandingTestimonialListSection
  title="Don't take it from us"
  description="See what other people have to say."
  testimonialItems={testimonialItems}
/>
```

</ComponentExample>

## Usage

```jsx
import { LandingTestimonialListSection } from '@/components/landing/testimonial/LandingTestimonialList';
```

```jsx
const testimonialItems = [
  {
    name: 'Parl Coppa',
    text: 'This is the best thing since sliced bread. I cannot believe I did not think of it myself.',
    handle: '@coppalipse',
    imageSrc: 'https://picsum.photos/100/100.webp?random=1',
    size: 'half',
  },
  {
    name: 'Mathew',
    text: 'After using this, I cannot imagine going back to the old way of doing things.',
    handle: '@heymatt_oo',
    imageSrc: 'https://picsum.photos/100/100.webp?random=2',
    size: 'half',
  },
  {
    name: 'Joshua',
    text: 'Perfect for my use case',
    handle: '@joshua',
    imageSrc: 'https://picsum.photos/100/100.webp?random=3',
    size: 'third',
  },
  {
    name: 'Mandy',
    text: 'Excellent product!',
    handle: '@mandy',
    imageSrc: 'https://picsum.photos/100/100.webp?random=4',
    size: 'third',
  },
  {
    name: 'Alex',
    text: 'Can easily recommend!',
    handle: '@alex',
    imageSrc: 'https://picsum.photos/100/100.webp?random=5',
    size: 'third',
  },
];
```

```jsx
<LandingTestimonialListSection
  title="Don't take it from us"
  description="See what other people have to say."
  testimonialItems={testimonialItems}
/>
```

## Examples

### Background, links and features

This component supports different background colors.

Here we set <b>variant</b> to <b>secondary</b>. <br />
Testimonials can also be linked + be featured and you can mix and match to
send the desired message.

<ComponentExample previewComponent={
  <LandingTestimonialListSection
    withBackground
    variant="secondary"
    title="Don't take it from us"
    description="See what other people have to say."
    testimonialItems={[
      {
        name: 'Parl Coppa',
        text: 'This is the best thing since sliced bread. I cannot believe I did not think of it myself.',
        handle: '@coppalipse',
        imageSrc: 'https://picsum.photos/100/100.webp?random=1',
        featured: true, // Feature this testimonial
        url: 'https://example.com', // Link the testimonial
      },
      {
        name: 'Mathew',
        text: 'After using this, I cannot imagine going back to the old way of doing things.',
        handle: '@heymatt_oo',
        imageSrc: 'https://picsum.photos/100/100.webp?random=2',
        size: 'half',
        url: 'https://example.com',
      },
      {
        name: 'Joshua',
        text: 'Perfect for my use case',
        handle: '@joshua',
        imageSrc: 'https://picsum.photos/100/100.webp?random=3',
        size: 'half',
      },
    ]}
  />}>

```jsx
import { LandingTestimonialListSection } from '@/components/landing/testimonial/LandingTestimonialList';

const testimonialItems = [
  {
    name: 'Parl Coppa',
    text: 'This is the best thing since sliced bread. I cannot believe I did not think of it myself.',
    handle: '@coppalipse',
    imageSrc: 'https://picsum.photos/100/100.webp?random=1',
    featured: true, // Feature this testimonial
    url: 'https://example.com', // Link the testimonial
  },
  {
    name: 'Mathew',
    text: 'After using this, I cannot imagine going back to the old way of doing things.',
    handle: '@heymatt_oo',
    imageSrc: 'https://picsum.photos/100/100.webp?random=2',
    size: 'half',
    url: 'https://example.com',
  },
  {
    name: 'Joshua',
    text: 'Perfect for my use case',
    handle: '@joshua',
    imageSrc: 'https://picsum.photos/100/100.webp?random=3',
    size: 'half',
  },
]

<LandingTestimonialListSection
  withBackground
  variant="secondary"
  title="Don't take it from us"
  description="See what other people have to say."
  testimonialItems={testimonialItems}
/>
```

</ComponentExample>

### With Background Glow

<ComponentExample previewComponent={
  <LandingTestimonialListSection
    withBackgroundGlow
    backgroundGlowVariant="secondary"
    title="Don't take it from us"
    description="See what other people have to say."
    testimonialItems={[
      {
        name: 'Parl Coppa',
        text: 'This is the best thing since sliced bread. I cannot believe I did not think of it myself.',
        handle: '@coppalipse',
        imageSrc: 'https://picsum.photos/100/100.webp?random=1',
        featured: true, // Feature this testimonial
        url: 'https://example.com', // Link the testimonial
      },
      {
        name: 'Mathew',
        text: 'After using this, I cannot imagine going back to the old way of doing things.',
        handle: '@heymatt_oo',
        imageSrc: 'https://picsum.photos/100/100.webp?random=2',
        size: 'half',
        url: 'https://example.com',
      },
      {
        name: 'Joshua',
        text: 'Perfect for my use case',
        handle: '@joshua',
        imageSrc: 'https://picsum.photos/100/100.webp?random=3',
        size: 'half',
      },
    ]}
  />}>

```jsx
import { LandingTestimonialListSection } from '@/components/landing/testimonial/LandingTestimonialList';

const testimonialItems = [
  {
    name: 'Parl Coppa',
    text: 'This is the best thing since sliced bread. I cannot believe I did not think of it myself.',
    handle: '@coppalipse',
    imageSrc: 'https://picsum.photos/100/100.webp?random=1',
    featured: true, // Feature this testimonial
    url: 'https://example.com', // Link the testimonial
  },
  {
    name: 'Mathew',
    text: 'After using this, I cannot imagine going back to the old way of doing things.',
    handle: '@heymatt_oo',
    imageSrc: 'https://picsum.photos/100/100.webp?random=2',
    size: 'half',
    url: 'https://example.com',
  },
  {
    name: 'Joshua',
    text: 'Perfect for my use case',
    handle: '@joshua',
    imageSrc: 'https://picsum.photos/100/100.webp?random=3',
    size: 'half',
  },
]

<LandingTestimonialListSection
  withBackgroundGlow
  backgroundGlowVariant="secondary"
  title="Don't take it from us"
  description="See what other people have to say."
  testimonialItems={testimonialItems}
/>
```

</ComponentExample>

### With Read More Wrapper

<ComponentExample previewComponent={
  <DemoReadMoreWrapper size="md">
    <LandingTestimonialListSection
      withBackgroundGlow
      backgroundGlowVariant="secondary"
      title="Don't take it from us"
      description="See what other people have to say."
      testimonialItems={[
        {
          name: 'Parl Coppa',
          text: 'This is the best thing since sliced bread. I cannot believe I did not think of it myself.',
          handle: '@coppalipse',
          imageSrc: 'https://picsum.photos/100/100.webp?random=1',
          featured: true, // Feature this testimonial
          url: 'https://example.com', // Link the testimonial
        },
        {
          name: 'Mathew',
          text: 'After using this, I cannot imagine going back to the old way of doing things.',
          handle: '@heymatt_oo',
          imageSrc: 'https://picsum.photos/100/100.webp?random=2',
          size: 'half',
          url: 'https://example.com',
        },
        {
          name: 'Joshua',
          text: 'Perfect for my use case',
          handle: '@joshua',
          imageSrc: 'https://picsum.photos/100/100.webp?random=3',
          size: 'half',
        },
      ]}
    />
  </DemoReadMoreWrapper>
}>

```jsx
import { LandingTestimonialListSection } from '@/components/landing/testimonial/LandingTestimonialList';
import { LandingTestimonialReadMoreWrapper } from '@/components/landing/testimonial/LandingTestimonialReadMoreWrapper';

const testimonialItems = [
  {
    name: 'Parl Coppa',
    text: 'This is the best thing since sliced bread. I cannot believe I did not think of it myself.',
    handle: '@coppalipse',
    imageSrc: 'https://picsum.photos/100/100.webp?random=1',
    featured: true, // Feature this testimonial
    url: 'https://example.com', // Link the testimonial
  },
  {
    name: 'Mathew',
    text: 'After using this, I cannot imagine going back to the old way of doing things.',
    handle: '@heymatt_oo',
    imageSrc: 'https://picsum.photos/100/100.webp?random=2',
    size: 'half',
    url: 'https://example.com',
  },
  {
    name: 'Joshua',
    text: 'Perfect for my use case',
    handle: '@joshua',
    imageSrc: 'https://picsum.photos/100/100.webp?random=3',
    size: 'half',
  },
]

<LandingTestimonialReadMoreWrapper size="md">
  <LandingTestimonialListSection
    withBackgroundGlow
    backgroundGlowVariant="secondary"
    title="Don't take it from us"
    description="See what other people have to say."
    testimonialItems={testimonialItems}
  />
</LandingTestimonialReadMoreWrapper>
```

</ComponentExample>

## API Reference

| Prop Name                                                                                                    | Prop Type                    | Required | Default     |
| ------------------------------------------------------------------------------------------------------------ | ---------------------------- | -------- | ----------- |
| **title** <Tippy>React nodes or string for the section title.</Tippy>                                        | `string` ǀ `React.ReactNode` | No       | -           |
| **titleComponent** <Tippy>Custom React component for the section title.</Tippy>                              | `React.ReactNode`            | No       | -           |
| **description** <Tippy>React nodes or string for the section description.</Tippy>                            | `string` ǀ `React.ReactNode` | No       | -           |
| **descriptionComponent** <Tippy>Custom React component for the section description.</Tippy>                  | `React.ReactNode`            | No       | -           |
| **testimonialItems** <Tippy>An array of objects representing testimonial items.</Tippy>                      | `TestimonialItem[]`          | Yes      | -           |
| **withBackground** <Tippy>Boolean to determine whether to display section background or not.</Tippy>         | `boolean`                    | No       | `false`     |
| **variant** <Tippy>String defining the variant of the section background.</Tippy>                            | `'primary'` ǀ `'secondary'`  | No       | `'primary'` |
| **withBackgroundGlow** <Tippy>Boolean to determine whether to display background glow effect or not.</Tippy> | `boolean`                    | No       | `false`     |
| **backgroundGlowVariant** <Tippy>String defining the variant of the background glow effect.</Tippy>          | `'primary'` ǀ `'secondary'`  | No       | `'primary'` |

```ts
export interface TestimonialItem {
  className?: string;
  url?: string;
  text: string;
  imageSrc: string;
  name: string;
  handle: string;
  featured?: boolean;
  verified?: boolean;
  size?: 'full' | 'half' | 'third'; // NB: Only applies to testimonials in a list, not grid.
}
```

## More Examples

For more even more examples, see our <a href="/demo/landing-page-component-examples" className="fancy-link">Landing Page Component Examples</a> page or see complete landing page examples by <a href="/demo/landing-page-templates" className="fancy-link">Exploring Our Landing Page Templates</a>.

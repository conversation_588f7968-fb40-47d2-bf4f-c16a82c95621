---
title: 'Landing Page Rating Component'
date: '2024-04-04'
lastmod: '2024-07-06'
tags:
  - 'guide'
  - 'components'
  - 'documentation'
  - 'landing-page'
  - 'ui-library'
  - 'react'
  - 'nextjs'
  - 'tailwind-css'
  - 'rating'
  - 'shadcn-ui'
  - 'shadcn-ui-rating'
  - 'shadcn-ui-landing-page'

images:
  - '/static/images/blog/docs/landing-page-rating-component.webp'

summary: 'A component meant to be used in the landing page. Shows a rating with stars.'
layout: PostHub

showThemeSelector: true
showInstallNotice: true

canonicalUrl: 'https://shipixen.com/boilerplate-documentation/landing-page-components/rating'
---

Use this component to show a rating with stars.

<ComponentExample
previewComponent={<div className="p-6">
  <LandingRating />
</div>}
>

```jsx
import { LandingRating } from '@/components/landing/rating/LandingRating';

<LandingRating />;
```

</ComponentExample>

## Usage

```jsx
import { LandingRating } from '@/components/landing/rating/LandingRating';
```

```jsx
<LandingRating />
```

## Examples

### With Custom Rating

<ComponentExample
previewComponent={<div className="p-6">
  <LandingRating rating={4}/>
</div>}
>

```jsx
import { LandingRating } from '@/components/landing/rating/LandingRating';

<LandingRating rating={4} />;
```

</ComponentExample>

### With Partial Rating

<ComponentExample
previewComponent={<div className="p-6">
  <LandingRating rating={4.3}/>
</div>}
>

```jsx
import { LandingRating } from '@/components/landing/rating/LandingRating';

<LandingRating rating={4.3} />;
```

</ComponentExample>

### With Custom Size

<ComponentExample
previewComponent={<div className="p-6">
  <LandingRating rating={4.3} size="large"/>
</div>}
>

```jsx
import { LandingRating } from '@/components/landing/rating/LandingRating';

<LandingRating rating={4.3} size="large" />;
```

</ComponentExample>

### With Custom Max Rating

<ComponentExample
previewComponent={<div className="p-6">
  <LandingRating rating={4.3} maxRating={6}/>
</div>}
>

```jsx
import { LandingRating } from '@/components/landing/rating/LandingRating';

<LandingRating rating={4.3} maxRating={6} />;
```

</ComponentExample>

## API Reference

| Prop Name                                                            | Prop Type                         | Required | Default    |
| -------------------------------------------------------------------- | --------------------------------- | -------- | ---------- |
| **rating** <Tippy>Number representing the current rating.</Tippy>    | `number`                          | No       | 5          |
| **maxRating** <Tippy>Number representing the maximum rating.</Tippy> | `number`                          | No       | 5          |
| **size** <Tippy>Size of the rating stars.</Tippy>                    | `'small'   ǀ 'medium'  ǀ 'large'` | No       | `'medium'` |

## More Examples

For more even more examples, see our <a href="/demo/landing-page-component-examples" className="fancy-link">Landing Page Component Examples</a> page or see complete landing page examples by <a href="/demo/landing-page-templates" className="fancy-link">Exploring Our Landing Page Templates</a>.

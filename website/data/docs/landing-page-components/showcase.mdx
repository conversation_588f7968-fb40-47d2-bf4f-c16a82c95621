---
title: 'Landing Page Showcase'
date: '2024-08-05'
lastmod: '2025-06-04'
tags:
  - 'guide'
  - 'components'
  - 'documentation'
  - 'landing-page'
  - 'ui-library'
  - 'react'
  - 'nextjs'
  - 'tailwind-css'
  - 'showcase'
  - 'logo-grid'
  - 'icon-grid'
  - 'grid'
  - 'shadcn-ui'
  - 'shadcn-ui-showcase'
  - 'shadcn-ui-landing-page'

images:
  - '/static/images/blog/docs/landing-page-showcase-component.webp'

summary: 'A component meant to be used in the landing page. It displays text and a grid of logos/images, optionally showcasing the companies that use the product, integrations etc.'
layout: PostHub

showThemeSelector: true
showInstallNotice: true

canonicalUrl: 'https://shipixen.com/boilerplate-documentation/landing-page-components/showcase'
---

This component displays a grid of logos or images, showcasing the companies that use the product, integrations, etc.

<ComponentExample
  previewComponent={
    <LandingShowcase
      title="Import with ease"
      description="All your video assets in one platform. Import your existing footage from any device with a click."
    >
      <LandingShowcaseItem>
        <FigmaIcon className="w-8 h-8" />
      </LandingShowcaseItem>

      <LandingShowcaseItem>
        <TwitchIcon className="w-8 h-8" />
      </LandingShowcaseItem>

      <LandingShowcaseItem>
        <ChromeIcon className="w-8 h-8" />
      </LandingShowcaseItem>

      <LandingShowcaseItem>
        <InstagramIcon className="w-8 h-8" />
      </LandingShowcaseItem>

      <LandingShowcaseItem>
        <TwitterIcon className="w-8 h-8" />
      </LandingShowcaseItem>

      <LandingShowcaseItem>
        <FramerIcon className="w-8 h-8" />
      </LandingShowcaseItem>

      <LandingShowcaseItem>
        <GithubIcon className="w-8 h-8" />
      </LandingShowcaseItem>

      <LandingShowcaseItem>
        <SlackIcon className="w-8 h-8" />
      </LandingShowcaseItem>
    </LandingShowcase>

}>

```jsx
import {
  FigmaIcon,
  TwitchIcon,
  ChromeIcon,
  InstagramIcon,
  TwitterIcon,
  FramerIcon,
  GithubIcon,
  SlackIcon,
} from 'lucide-react';
import { LandingShowcase } from '@/components/landing/showcase/LandingShowcase';
import { LandingShowcaseItem } from '@/components/landing/showcase/LandingShowcaseItem';

<LandingShowcase
  title="Import with ease"
  description="All your video assets in one platform. Import your existing footage from any device with a click."
>
  <LandingShowcaseItem>
    <FigmaIcon className="w-10 h-10" />
  </LandingShowcaseItem>

  <LandingShowcaseItem>
    <TwitchIcon className="w-10 h-10" />
  </LandingShowcaseItem>

  <LandingShowcaseItem>
    <ChromeIcon className="w-10 h-10" />
  </LandingShowcaseItem>

  <LandingShowcaseItem>
    <InstagramIcon className="w-10 h-10" />
  </LandingShowcaseItem>

  <LandingShowcaseItem>
    <TwitterIcon className="w-10 h-10" />
  </LandingShowcaseItem>

  <LandingShowcaseItem>
    <FramerIcon className="w-10 h-10" />
  </LandingShowcaseItem>

  <LandingShowcaseItem>
    <GithubIcon className="w-10 h-10" />
  </LandingShowcaseItem>

  <LandingShowcaseItem>
    <SlackIcon className="w-10 h-10" />
  </LandingShowcaseItem>
</LandingShowcase>;
```

</ComponentExample>

## Usage

```jsx
import {
  FigmaIcon,
  TwitchIcon,
  ChromeIcon,
  InstagramIcon,
  TwitterIcon,
  FramerIcon,
  GithubIcon,
  SlackIcon,
} from 'lucide-react';
import { LandingShowcase } from '@/components/landing/showcase/LandingShowcase';
import { LandingShowcaseItem } from '@/components/landing/showcase/LandingShowcaseItem';
```

```jsx
<LandingShowcase
  title="Import with ease"
  description="All your video assets in one platform. Import your existing footage from any device with a click."
>
  <LandingShowcaseItem>
    <FigmaIcon className="w-10 h-10" />
  </LandingShowcaseItem>

  <LandingShowcaseItem>
    <TwitchIcon className="w-10 h-10" />
  </LandingShowcaseItem>

  <LandingShowcaseItem>
    <ChromeIcon className="w-10 h-10" />
  </LandingShowcaseItem>

  <LandingShowcaseItem>
    <InstagramIcon className="w-10 h-10" />
  </LandingShowcaseItem>

  <LandingShowcaseItem>
    <TwitterIcon className="w-10 h-10" />
  </LandingShowcaseItem>

  <LandingShowcaseItem>
    <FramerIcon className="w-10 h-10" />
  </LandingShowcaseItem>

  <LandingShowcaseItem>
    <GithubIcon className="w-10 h-10" />
  </LandingShowcaseItem>

  <LandingShowcaseItem>
    <SlackIcon className="w-10 h-10" />
  </LandingShowcaseItem>
</LandingShowcase>
```

## Examples

### Text Position

<ComponentExample
  previewComponent={
    <LandingShowcase
      title="Import with ease"
      description="All your video assets in one platform. Import your existing footage from any device with a click."
      textPosition="right"
    >
      <LandingShowcaseItem>
        <FigmaIcon className="w-10 h-10" />
      </LandingShowcaseItem>

      <LandingShowcaseItem>
        <TwitchIcon className="w-10 h-10" />
      </LandingShowcaseItem>

      <LandingShowcaseItem>
        <ChromeIcon className="w-10 h-10" />
      </LandingShowcaseItem>

      <LandingShowcaseItem>
        <InstagramIcon className="w-10 h-10" />
      </LandingShowcaseItem>

      <LandingShowcaseItem>
        <TwitterIcon className="w-10 h-10" />
      </LandingShowcaseItem>

      <LandingShowcaseItem>
        <FramerIcon className="w-10 h-10" />
      </LandingShowcaseItem>

      <LandingShowcaseItem>
        <GithubIcon className="w-10 h-10" />
      </LandingShowcaseItem>

      <LandingShowcaseItem>
        <SlackIcon className="w-10 h-10" />
      </LandingShowcaseItem>
    </LandingShowcase>}

>

```jsx
import {
  FigmaIcon,
  TwitchIcon,
  ChromeIcon,
  InstagramIcon,
  TwitterIcon,
  FramerIcon,
  GithubIcon,
  SlackIcon,
} from 'lucide-react';
import { LandingShowcase } from '@/components/landing/showcase/LandingShowcase';
import { LandingShowcaseItem } from '@/components/landing/showcase/LandingShowcaseItem';

<LandingShowcase
  title="Import with ease"
  description="All your video assets in one platform. Import your existing footage from any device with a click."
  textPosition="right"
>
  <LandingShowcaseItem>
    <FigmaIcon className="w-10 h-10" />
  </LandingShowcaseItem>

  <LandingShowcaseItem>
    <TwitchIcon className="w-10 h-10" />
  </LandingShowcaseItem>

  <LandingShowcaseItem>
    <ChromeIcon className="w-10 h-10" />
  </LandingShowcaseItem>

  <LandingShowcaseItem>
    <InstagramIcon className="w-10 h-10" />
  </LandingShowcaseItem>

  <LandingShowcaseItem>
    <TwitterIcon className="w-10 h-10" />
  </LandingShowcaseItem>

  <LandingShowcaseItem>
    <FramerIcon className="w-10 h-10" />
  </LandingShowcaseItem>

  <LandingShowcaseItem>
    <GithubIcon className="w-10 h-10" />
  </LandingShowcaseItem>

  <LandingShowcaseItem>
    <SlackIcon className="w-10 h-10" />
  </LandingShowcaseItem>
</LandingShowcase>;
```

</ComponentExample>

### Center Text Position

<ComponentExample
  previewComponent={
    <LandingShowcase
      title="Import with ease"
      description="All your video assets in one platform. Import your existing footage from any device with a click."
      textPosition="center"
    >
      <LandingShowcaseItem>
        <FigmaIcon className="w-10 h-10" />
      </LandingShowcaseItem>

      <LandingShowcaseItem>
        <TwitchIcon className="w-10 h-10" />
      </LandingShowcaseItem>

      <LandingShowcaseItem>
        <ChromeIcon className="w-10 h-10" />
      </LandingShowcaseItem>

      <LandingShowcaseItem>
        <InstagramIcon className="w-10 h-10" />
      </LandingShowcaseItem>

      <LandingShowcaseItem>
        <TwitterIcon className="w-10 h-10" />
      </LandingShowcaseItem>

      <LandingShowcaseItem>
        <FramerIcon className="w-10 h-10" />
      </LandingShowcaseItem>

      <LandingShowcaseItem>
        <GithubIcon className="w-10 h-10" />
      </LandingShowcaseItem>

      <LandingShowcaseItem>
        <SlackIcon className="w-10 h-10" />
      </LandingShowcaseItem>
    </LandingShowcase>}

>

```jsx
import {
  FigmaIcon,
  TwitchIcon,
  ChromeIcon,
  InstagramIcon,
  TwitterIcon,
  FramerIcon,
  GithubIcon,
  SlackIcon,
} from 'lucide-react';
import { LandingShowcase } from '@/components/landing/showcase/LandingShowcase';
import { LandingShowcaseItem } from '@/components/landing/showcase/LandingShowcaseItem';

<LandingShowcase
  title="Import with ease"
  description="All your video assets in one platform. Import your existing footage from any device with a click."
  textPosition="center"
>
  <LandingShowcaseItem>
    <FigmaIcon className="w-10 h-10" />
  </LandingShowcaseItem>

  <LandingShowcaseItem>
    <TwitchIcon className="w-10 h-10" />
  </LandingShowcaseItem>

  <LandingShowcaseItem>
    <ChromeIcon className="w-10 h-10" />
  </LandingShowcaseItem>

  <LandingShowcaseItem>
    <InstagramIcon className="w-10 h-10" />
  </LandingShowcaseItem>

  <LandingShowcaseItem>
    <TwitterIcon className="w-10 h-10" />
  </LandingShowcaseItem>

  <LandingShowcaseItem>
    <FramerIcon className="w-10 h-10" />
  </LandingShowcaseItem>

  <LandingShowcaseItem>
    <GithubIcon className="w-10 h-10" />
  </LandingShowcaseItem>

  <LandingShowcaseItem>
    <SlackIcon className="w-10 h-10" />
  </LandingShowcaseItem>
</LandingShowcase>
```

</ComponentExample>

### With Background

<ComponentExample
  previewComponent={
    <LandingShowcase
      title="Import with ease"
      description="All your video assets in one platform. Import your existing footage from any device with a click."
      withBackground
      variant="secondary"
    >
      <LandingShowcaseItem>
        <FigmaIcon className="w-10 h-10" />
      </LandingShowcaseItem>

      <LandingShowcaseItem>
        <TwitchIcon className="w-10 h-10" />
      </LandingShowcaseItem>

      <LandingShowcaseItem>
        <ChromeIcon className="w-10 h-10" />
      </LandingShowcaseItem>

      <LandingShowcaseItem>
        <InstagramIcon className="w-10 h-10" />
      </LandingShowcaseItem>

      <LandingShowcaseItem>
        <TwitterIcon className="w-10 h-10" />
      </LandingShowcaseItem>

      <LandingShowcaseItem>
        <FramerIcon className="w-10 h-10" />
      </LandingShowcaseItem>

      <LandingShowcaseItem>
        <GithubIcon className="w-10 h-10" />
      </LandingShowcaseItem>

      <LandingShowcaseItem>
        <SlackIcon className="w-10 h-10" />
      </LandingShowcaseItem>
    </LandingShowcase>}

>

```jsx
import {
  FigmaIcon,
  TwitchIcon,
  ChromeIcon,
  InstagramIcon,
  TwitterIcon,
  FramerIcon,
  GithubIcon,
  SlackIcon,
} from 'lucide-react';
import { LandingShowcase } from '@/components/landing/showcase/LandingShowcase';
import { LandingShowcaseItem } from '@/components/landing/showcase/LandingShowcaseItem';

<LandingShowcase
  title="Import with ease"
  description="All your video assets in one platform. Import your existing footage from any device with a click."
  withBackground
  variant="secondary"
>
  <LandingShowcaseItem>
    <FigmaIcon className="w-10 h-10" />
  </LandingShowcaseItem>

  <LandingShowcaseItem>
    <TwitchIcon className="w-10 h-10" />
  </LandingShowcaseItem>

  <LandingShowcaseItem>
    <ChromeIcon className="w-10 h-10" />
  </LandingShowcaseItem>

  <LandingShowcaseItem>
    <InstagramIcon className="w-10 h-10" />
  </LandingShowcaseItem>

  <LandingShowcaseItem>
    <TwitterIcon className="w-10 h-10" />
  </LandingShowcaseItem>

  <LandingShowcaseItem>
    <FramerIcon className="w-10 h-10" />
  </LandingShowcaseItem>

  <LandingShowcaseItem>
    <GithubIcon className="w-10 h-10" />
  </LandingShowcaseItem>

  <LandingShowcaseItem>
    <SlackIcon className="w-10 h-10" />
  </LandingShowcaseItem>
</LandingShowcase>;
```

</ComponentExample>

### Background Glow

<ComponentExample
  previewComponent={
    <LandingShowcase
      title="Import with ease"
      description="All your video assets in one platform. Import your existing footage from any device with a click."
      withBackground
      variant="secondary"
      withBackgroundGlow
      backgroundGlowVariant="secondary"
    >
      <LandingShowcaseItem>
        <FigmaIcon className="w-10 h-10" />
      </LandingShowcaseItem>

      <LandingShowcaseItem>
        <TwitchIcon className="w-10 h-10" />
      </LandingShowcaseItem>

      <LandingShowcaseItem>
        <ChromeIcon className="w-10 h-10" />
      </LandingShowcaseItem>

      <LandingShowcaseItem>
        <InstagramIcon className="w-10 h-10" />
      </LandingShowcaseItem>

      <LandingShowcaseItem>
        <TwitterIcon className="w-10 h-10" />
      </LandingShowcaseItem>

      <LandingShowcaseItem>
        <FramerIcon className="w-10 h-10" />
      </LandingShowcaseItem>

      <LandingShowcaseItem>
        <GithubIcon className="w-10 h-10" />
      </LandingShowcaseItem>

      <LandingShowcaseItem>
        <SlackIcon className="w-10 h-10" />
      </LandingShowcaseItem>
    </LandingShowcase>}

>

```jsx
import {
  FigmaIcon,
  TwitchIcon,
  ChromeIcon,
  InstagramIcon,
  TwitterIcon,
  FramerIcon,
  GithubIcon,
  SlackIcon,
} from 'lucide-react';
import { LandingShowcase } from '@/components/landing/showcase/LandingShowcase';
import { LandingShowcaseItem } from '@/components/landing/showcase/LandingShowcaseItem';

<LandingShowcase
  title="Import with ease"
  description="All your video assets in one platform. Import your existing footage from any device with a click."
  withBackground
  variant="secondary"
  withBackgroundGlow
  backgroundGlowVariant="secondary"
>
  <LandingShowcaseItem>
    <FigmaIcon className="w-10 h-10" />
  </LandingShowcaseItem>

  <LandingShowcaseItem>
    <TwitchIcon className="w-10 h-10" />
  </LandingShowcaseItem>

  <LandingShowcaseItem>
    <ChromeIcon className="w-10 h-10" />
  </LandingShowcaseItem>

  <LandingShowcaseItem>
    <InstagramIcon className="w-10 h-10" />
  </LandingShowcaseItem>

  <LandingShowcaseItem>
    <TwitterIcon className="w-10 h-10" />
  </LandingShowcaseItem>

  <LandingShowcaseItem>
    <FramerIcon className="w-10 h-10" />
  </LandingShowcaseItem>

  <LandingShowcaseItem>
    <GithubIcon className="w-10 h-10" />
  </LandingShowcaseItem>

  <LandingShowcaseItem>
    <SlackIcon className="w-10 h-10" />
  </LandingShowcaseItem>
</LandingShowcase>;
```

</ComponentExample>

## API Reference

| Prop Name                                                                                                                             | Prop Type                    | Required | Default     |
| ------------------------------------------------------------------------------------------------------------------------------------- | ---------------------------- | -------- | ----------- |
| **children** <Tippy>React nodes to be rendered within the component (below the text on smaller screen, right side on larger).</Tippy> | `React.ReactNode`            | No       | -           |
| **innerClassName** <Tippy>Additional class names for the inner div element.</Tippy>                                                   | `string`                     | No       | -           |
| **title** <Tippy>The title text or React node to be displayed in the component.</Tippy>                                               | `string` ǀ `React.ReactNode` | No       | -           |
| **titleComponent** <Tippy>React node to be rendered as the title, if `title` is not provided.</Tippy>                                 | `React.ReactNode`            | No       | -           |
| **description** <Tippy>The description text or React node to be displayed in the component.</Tippy>                                   | `string` ǀ `React.ReactNode` | No       | -           |
| **descriptionComponent** <Tippy>React node to be rendered as the description, if `description` is not provided.</Tippy>               | `React.ReactNode`            | No       | -           |
| **textPosition** <Tippy>Position of the text within the component. Can be 'left' or 'right' or 'center'.</Tippy>                                  | `'left'` ǀ `'right'`         | No       | `'left'`    |
| **withBackground** <Tippy>Flag indicating whether the background should be applied.</Tippy>                                           | `boolean`                    | No       | `false`     |
| **withBackgroundGlow** <Tippy>Flag indicating whether the background glow should be applied.</Tippy>                                  | `boolean`                    | No       | `false`     |
| **variant** <Tippy>Variant of the background style. Can be 'primary' or 'secondary'.</Tippy>                                          | `'primary'` ǀ `'secondary'`  | No       | `'primary'` |
| **backgroundGlowVariant** <Tippy>Variant of the background glow style. Can be 'primary' or 'secondary'.</Tippy>                       | `'primary'` ǀ `'secondary'`  | No       | `'primary'` |

## More Examples

For more even more examples, see our <a href="/demo/landing-page-component-examples" className="fancy-link">Landing Page Component Examples</a> page or see complete landing page examples by <a href="/demo/landing-page-templates" className="fancy-link">Exploring Our Landing Page Templates</a>.

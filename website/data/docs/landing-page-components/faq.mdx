---
title: 'Landing Page FAQ Component'
date: '2023-11-19'
lastmod: '2024-07-06'
tags:
  - 'guide'
  - 'components'
  - 'documentation'
  - 'landing-page'
  - 'ui-library'
  - 'react'
  - 'nextjs'
  - 'tailwind-css'
  - 'frequently-asked-questions'
  - 'shadcn-ui'
  - 'shadcn-ui-frequently-asked-questions'
  - 'shadcn-ui-landing-page'

images: ['/static/images/blog/docs/landing-page-faq.webp']
summary: 'This component is used to display frequently asked questions & answers on the landing page.'
layout: PostHub

showThemeSelector: true
showInstallNotice: true

canonicalUrl: 'https://shipixen.com/boilerplate-documentation/landing-page-components/faq'
---

Use this component to display frequently asked questions & answers on the landing page.

This can also be added on a separate page, but it's usually a good idea to have it on the landing page so people can search for information. <br/>
Moreover, it can give you a bit of an SEO boost too.

<ComponentExample
  previewComponent={<LandingFaqSection
    title={'FAQ'}
    description="Looking to learn more about Shipixen? Here are some of the most common
    questions."
    faqItems={[
      {
        question: 'What is Shipixen exactly?',
        answer:
          'Shipixen is an app that generates boilerplate code with your branding. You get the git repository & can modify the code as you want.',
      },
      {
        question: 'Where can I deploy the generated code?',
        answer:
          'We make a deployment to Vercel for you as part of the generation process. However, you own the code, so you can technically deploy it on any host that support Next.js.',
      },
      {
        question: 'What do I get if I pre-order?',
        answer:
          'With the pre-order, you get a 50% discount on the final price and a lifetime license for the generated code.',
      },
    ]}
  />}
>

```jsx
import { LandingFaqSection } from '@/components/landing/LandingFaq';

const faqItems = [
  {
    question: 'What is Shipixen exactly?',
    answer:
      'Shipixen is an app that generates boilerplate code with your branding. You get the git repository & can modify the code as you want.',
  },
  {
    question: 'Where can I deploy the generated code?',
    answer:
      'We make a deployment to Vercel for you as part of the generation process. However, you own the code, so you can technically deploy it on any host that support Next.js.',
  },
  {
    question: 'What do I get if I pre-order?',
    answer:
      'With the pre-order, you get a 50% discount on the final price and a lifetime license for the generated code.',
  },
];

<LandingFaqSection
  title={'FAQ'}
  description="Looking to learn more about Shipixen? Here are some of the most common
  questions."
  faqItems={faqItems}
/>}/>
```

</ComponentExample>

## Usage

```jsx
import { LandingFaqSection } from '@/components/landing/LandingFaq';
```

```jsx
<LandingFaqSection
  title={'Frequently Asked Questions'}
  description="Looking to learn more about Shipixen? Here are some of the most common
  questions we get asked."
  faqItems={[
    {
      question: 'What is Shipixen exactly?',
      answer:
        'Shipixen is an app that generates boilerplate code with your branding. You get the git repository & can modify the code as you want.',
    },
  ]}
/>
```

## Examples

### With Background

<ComponentExample
  previewComponent={<LandingFaqSection
    withBackground
    variant="secondary"
    title='FAQ'
    description="Looking to learn more about our product? Here are some of the most common
    questions."
    faqItems={[
      {
        question: 'Can I get a refund?',
        answer:
          'Yes, you can get a refund within 30 days of your purchase. No questions asked.',
      },
      {
        question: 'What technologies are used?',
        answer:
          'We use Next.js, Tailwind CSS, and Vercel for the deployment.',
      },
      {
        question: 'What do I get if I pre-order?',
        answer:
          'With the pre-order, you get a 50% discount on the final price and a lifetime license for the generated code.',
      },
    ]}
  />}
>

```jsx
import { LandingFaqSection } from '@/components/landing/LandingFaq';

const faqItems = [
  {
    question: 'Can I get a refund?',
    answer:
      'Yes, you can get a refund within 30 days of your purchase. No questions asked.',
  },
  {
    question: 'What technologies are used?',
    answer:
      'We use Next.js, Tailwind CSS, and Vercel for the deployment.',
  },
  {
    question: 'What do I get if I pre-order?',
    answer:
      'With the pre-order, you get a 50% discount on the final price and a lifetime license for the generated code.',
  },
]

<LandingFaqSection
  withBackground
  variant="secondary"
  title='FAQ'
  description="Looking to learn more about our product? Here are some of the most common
  questions."
  faqItems={faqItems}
/>
```

</ComponentExample>

### With Background Glow

<ComponentExample
  previewComponent={<LandingFaqSection
    withBackgroundGlow
    backgroundGlowVariant="secondary"
    title='FAQ'
    description="Looking to learn more about our product? Here are some of the most common
    questions."
    faqItems={[
      {
        question: 'Can I get a refund?',
        answer:
          'Yes, you can get a refund within 30 days of your purchase. No questions asked.',
      },
      {
        question: 'What technologies are used?',
        answer:
          'We use Next.js, Tailwind CSS, and Vercel for the deployment.',
      },
      {
        question: 'What do I get if I pre-order?',
        answer:
          'With the pre-order, you get a 50% discount on the final price and a lifetime license for the generated code.',
      },
    ]}
  />}
>

```jsx
import { LandingFaqSection } from '@/components/landing/LandingFaq';

const faqItems = [
  {
    question: 'Can I get a refund?',
    answer:
      'Yes, you can get a refund within 30 days of your purchase. No questions asked.',
  },
  {
    question: 'What technologies are used?',
    answer:
      'We use Next.js, Tailwind CSS, and Vercel for the deployment.',
  },
  {
    question: 'What do I get if I pre-order?',
    answer:
      'With the pre-order, you get a 50% discount on the final price and a lifetime license for the generated code.',
  },
]

<LandingFaqSection
  withBackgroundGlow
  backgroundGlowVariant="secondary"
  title='FAQ'
  description="Looking to learn more about our product? Here are some of the most common
  questions."
  faqItems={faqItems}
/>
```

</ComponentExample>

## API Reference

| Prop Name                                                                                                                   | Prop Type                    | Required | Default     |
| --------------------------------------------------------------------------------------------------------------------------- | ---------------------------- | -------- | ----------- |
| **title** <Tippy>A string or React node representing the title of the FAQ section.</Tippy>                                  | `string` ǀ `React.ReactNode` | No       | -           |
| **titleComponent** <Tippy>A React node representing the title of the FAQ section.</Tippy>                                   | `React.ReactNode`            | No       | -           |
| **description** <Tippy>A string or React node representing the description of the FAQ section.</Tippy>                      | `string` ǀ `React.ReactNode` | No       | -           |
| **descriptionComponent** <Tippy>A React node representing the description of the FAQ section.</Tippy>                       | `React.ReactNode`            | No       | -           |
| **faqItems** <Tippy>An array of objects containing question and answer strings for the FAQ items.</Tippy>                   | `FaqItem[]`                  | Yes      | -           |
| **withBackground** <Tippy>A boolean indicating whether the FAQ section should have a background.</Tippy>                    | `boolean`                    | No       | `false`     |
| **withBackgroundGlow** <Tippy>A boolean indicating whether the FAQ section should have a glowing background effect.</Tippy> | `boolean`                    | No       | `false`     |
| **variant** <Tippy>The variant of the background color, either `'primary'` or `'secondary'`.</Tippy>                        | `'primary'` ǀ `'secondary'`  | No       | `'primary'` |
| **backgroundGlowVariant** <Tippy>The variant of the glowing background, either `'primary'` or `'secondary'`.</Tippy>        | `'primary'` ǀ `'secondary'`  | No       | `'primary'` |

```ts
export interface FaqItem {
  question: string;
  answer: string;
}
```

## More Examples

For more even more examples, see our <a href="/demo/landing-page-component-examples" className="fancy-link">Landing Page Component Examples</a> page or see complete landing page examples by <a href="/demo/landing-page-templates" className="fancy-link">Exploring Our Landing Page Templates</a>.

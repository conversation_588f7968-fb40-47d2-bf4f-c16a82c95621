---
title: 'Landing Page Feature Component'
date: '2023-11-19'
lastmod: '2025-06-07'
tags:
  - 'guide'
  - 'components'
  - 'documentation'
  - 'landing-page'
  - 'product-feature'
  - 'ui-library'
  - 'react'
  - 'nextjs'
  - 'tailwind-css'
  - 'shadcn-ui'
  - 'shadcn-ui-landing-page'

images:
  - '/static/images/blog/docs/landing-page-features-list.webp'
summary: 'This component is used to display a single product feature. It has a title, description and icon. Used in the Features List component.'
layout: PostHub

showThemeSelector: true
showInstallNotice: true

canonicalUrl: 'https://shipixen.com/boilerplate-documentation/landing-page-components/feature'
---

Use this component to display a single product feature. It has a title, description and icon.

It should be used with the [FeatureList](/boilerplate-documentation/landing-page-components/feature-list) component, but can also be used standalone.

<ComponentExample
  previewComponent={<LandingFeature
  className="p-4"
    title='Automatic deployment to Vercel'
    description='Deploying the generated template to Vercel is as easy as clicking a button. There is no need to configure anything.'
    icon={<SparklesIcon />}
  />}
>

```jsx
import { LandingFeature } from '@/components/landing/feature/LandingFeature';
import { SparklesIcon } from 'lucide-react';

<LandingFeature
  title="Automatic deployment to Vercel"
  description="Deploying the generated template to Vercel is as easy as clicking a button. There is no need to configure anything."
  icon={<SparklesIcon />}
/>;
```

</ComponentExample>

## Usage

```jsx
import { LandingFeature } from '@/components/landing/feature/LandingFeature';
import { SparklesIcon } from 'lucide-react';
```

```
<LandingFeature
  title='Automatic deployment to Vercel'
  description='Deploying the generated template to Vercel is as easy as clicking a button. There is no need to configure anything.'
  icon={<SparklesIcon />}
/>
```

## API Reference

| Prop Name                                                                               | Prop Type                 | Required | Default     |
| --------------------------------------------------------------------------------------- | ------------------------- | -------- | ----------- |
| **title** <Tippy>This is the title of the feature.</Tippy>                              | `string`                  | Yes      | -           |
| **titleComponent** <Tippy>This is the title of the feature.</Tippy>                  | `React.ReactNode`                  | No      | -           |
| **description** <Tippy>This is the description of the feature.</Tippy>                  | `string`                  | Yes      | -           |
| **descriptionComponent** <Tippy>This is the description of the feature.</Tippy>                  | `React.ReactNode`                  | No      | -           |
| **icon** <Tippy>The icon representing the feature.</Tippy>                              | `React.ReactNode`         | Yes      | -           |
| **variant** <Tippy>The variant of the feature, either 'primary' or 'secondary'.</Tippy> | `'primary' ǀ 'secondary'` | No       | `'primary'` |

## More Examples

For more even more examples, see our <a href="/demo/landing-page-component-examples" className="fancy-link">Landing Page Component Examples</a> page or see complete landing page examples by <a href="/demo/landing-page-templates" className="fancy-link">Exploring Our Landing Page Templates</a>.

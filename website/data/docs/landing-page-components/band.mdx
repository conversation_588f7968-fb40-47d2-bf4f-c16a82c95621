---
title: 'Landing Page Band Component'
date: '2023-11-19'
lastmod: '2024-07-06'
tags:
  - 'guide'
  - 'components'
  - 'documentation'
  - 'landing-page'
  - 'ui-library'
  - 'react'
  - 'nextjs'
  - 'tailwind-css'
  - 'showcase'
  - 'social-proof'
  - 'shadcn-ui'
  - 'shadcn-ui-social-proof'
  - 'shadcn-ui-landing-page'

images:
  - '/static/images/blog/docs/landing-page-band-component.webp'

summary: 'This component is used to display a fullscreen, brand-colored section that displays a title, description & some product logos or icons.'
layout: PostHub

showThemeSelector: true
showInstallNotice: true

canonicalUrl: 'https://shipixen.com/boilerplate-documentation/landing-page-components/band'
---

Use this component to 'break' the layout flow of your landing page and either present social proof or showcase the value proposition of the product.

With this component you display a full-width, brand-colored section that displays a title, description and some product logos or icons.

<ComponentExample
previewComponent={<LandingBandSection
  title={'20-100h'}
  description={'Saved on development by using Shipixen'}
  supportingComponent={
    <>
      <ChromeIcon className="w-12 h-12" />
      <FigmaIcon className="w-12 h-12" />
      <GithubIcon className="w-12 h-12" />
    </>
  }
/>}
>

```jsx
import { LandingBandSection } from '@/components/landing/LandingBand';
import { ChromeIcon, FigmaIcon, GithubIcon } from 'lucide-react';

<LandingBandSection
  title={'20-100h'}
  description={'Saved on development by using Shipixen'}
  supportingComponent={
    <>
      <ChromeIcon className="w-12 h-12" />
      <FigmaIcon className="w-12 h-12" />
      <GithubIcon className="w-12 h-12" />
    </>
  }
/>;
```

</ComponentExample>

## Usage

```jsx
import { LandingBandSection } from '@/components/landing/LandingBand';
import { ChromeIcon, FigmaIcon, GithubIcon } from 'lucide-react';
```

```jsx
<LandingBandSection
  title={'20-100h'}
  description={'Saved on development by using Shipixen'}
  supportingComponent={
    <>
      <ChromeIcon className="w-12 h-12" />
      <FigmaIcon className="w-12 h-12" />
      <GithubIcon className="w-12 h-12" />
    </>
  }
/>
```

## Examples

### With Social Proof

<ComponentExample
  previewComponent={<LandingBandSection
    title='4.9/5 stars'
    description='Our customers love our product'
    supportingComponent={
      <LandingSocialProof
        showRating
        numberOfUsers={99}
        avatarItems={[
          {
            imageSrc: '/static/images/people/1.webp',
            name: 'John Doe',
          },
          {
            imageSrc: '/static/images/people/2.webp',
            name: 'Jane Doe',
          },
          {
            imageSrc: '/static/images/people/3.webp',
            name: 'Alice Doe',
          },
        ]}
      />
    }
  />}
>

```jsx
import { LandingBandSection } from '@/components/landing/LandingBand';
import { LandingSocialProof } from '@/components/landing/social-proof/LandingSocialProof';

const avatarItems = [
  {
    imageSrc: '/static/images/people/1.webp',
    name: 'John Doe',
  },
  {
    imageSrc: '/static/images/people/2.webp',
    name: 'Jane Doe',
  },
  {
    imageSrc: '/static/images/people/3.webp',
    name: 'Alice Doe',
  },
]

<LandingBandSection
  title='4.9/5 stars'
  description='Our customers love our product'
  supportingComponent={
    <LandingSocialProof
      showRating
      numberOfUsers={99}
      avatarItems={avatarItems}
    />
  }
/>
```

</ComponentExample>

### Customization

<ComponentExample
  previewComponent={<LandingBandSection
    variant="secondary"
    title='20-100h'
    description='Saved on design'
    supportingComponent={
      <>
        <ChromeIcon className="w-12 h-12" />
        <FigmaIcon className="w-12 h-12" />
        <GithubIcon className="w-12 h-12" />
      </>
    }
  />}
>

```jsx
import { ChromeIcon, FigmaIcon, GithubIcon } from 'lucide-react';
import { LandingBandSection } from '@/components/landing/LandingBand';

<LandingBandSection
  variant="secondary"
  title="20-100h"
  description="Saved on design"
  supportingComponent={
    <>
      <ChromeIcon className="w-12 h-12" />
      <FigmaIcon className="w-12 h-12" />
      <GithubIcon className="w-12 h-12" />
    </>
  }
/>;
```

</ComponentExample>

## API Reference

| Prop Name                                                                                                                             | Prop Type                   | Required | Default     |
| ------------------------------------------------------------------------------------------------------------------------------------- | --------------------------- | -------- | ----------- |
| **children** <Tippy>React nodes to be rendered within the component (below the text on smaller screen, right side on larger).</Tippy> | `React.ReactNode`           | No       | -           |
| **title** <Tippy>The title to display. Can be a string or a React node for custom components</Tippy>                                  | `string \| React.ReactNode` | No       | -           |
| **titleComponent** <Tippy>Custom React component to use instead of the default title</Tippy>                                          | `React.ReactNode`           | No       | -           |
| **description** <Tippy>Description text or a React node for custom description components</Tippy>                                     | `string \| React.ReactNode` | No       | -           |
| **descriptionComponent** <Tippy>Custom React component to use instead of the default description</Tippy>                              | `React.ReactNode`           | No       | -           |
| **supportingComponent** <Tippy>A React component to support the main content, such as images or illustrations</Tippy>                 | `React.ReactNode`           | No       | -           |
| **withBackground** <Tippy>Whether the section should have a background color</Tippy>                                                  | `boolean`                   | No       | `true`      |
| **variant** <Tippy>Color variant of the background</Tippy>                                                                            | `'primary' \| 'secondary'`  | No       | `'primary'` |

## More Examples

For more even more examples, see our <a href="/demo/landing-page-component-examples" className="fancy-link">Landing Page Component Examples</a> page or see complete landing page examples by <a href="/demo/landing-page-templates" className="fancy-link">Exploring Our Landing Page Templates</a>.

---
title: 'Landing Page Social Proof Band Item Component'
date: '2024-04-04'
lastmod: '2024-07-06'
tags:
  - 'guide'
  - 'components'
  - 'documentation'
  - 'landing-page'
  - 'ui-library'
  - 'react'
  - 'nextjs'
  - 'tailwind-css'
  - 'social-proof'
  - 'shadcn-ui'
  - 'shadcn-ui-social-proof'
  - 'shadcn-ui-landing-page'

images:
  - '/static/images/blog/docs/landing-page-social-proof-band-item-component.webp'

summary: 'A component meant to be used in the landing page, as a child of Social Proof Band. Shows a social proof/key feature/milestone item with an optional graphic.'
layout: PostHub

showThemeSelector: true
showInstallNotice: true

canonicalUrl: 'https://shipixen.com/boilerplate-documentation/landing-page-components/social-proof-band-item'
---

A component meant to be used in the landing page, as a child of Social Proof Band. Shows a social proof/key feature/milestone item with an optional graphic.

<ComponentExample
  previewComponent={
    <div className="p-6">
      <LandingSocialProofBandItem>
        100% encrypted and secure
      </LandingSocialProofBandItem>
    </div>
}

>

```jsx
import { LandingSocialProofBandItem } from '@/components/landing/social-proof/LandingSocialProofBandItem';

<LandingSocialProofBandItem>
  100% encrypted and secure
</LandingSocialProofBandItem>;
```

</ComponentExample>

## Usage

```jsx
import { LandingSocialProofBandItem } from '@/components/landing/social-proof/LandingSocialProofBandItem';
```

```jsx
<LandingSocialProofBandItem>
  100% encrypted and secure
</LandingSocialProofBandItem>
```

## Examples

### With custom graphics

<ComponentExample
  previewComponent={
    <div className="p-6">
      <LandingSocialProofBandItem graphic="magic">
        Easy setup
      </LandingSocialProofBandItem>
    </div>
}

>

```jsx
import { LandingSocialProofBandItem } from '@/components/landing/social-proof/LandingSocialProofBandItem';

<LandingSocialProofBandItem graphic="magic">
  Easy setup
</LandingSocialProofBandItem>;
```

</ComponentExample>

<ComponentExample
  previewComponent={
    <div className="p-6">
      <LandingSocialProofBandItem graphic="rating">
        Trusted by 1000+ customers
      </LandingSocialProofBandItem>
    </div>
}

>

```jsx
import { LandingSocialProofBandItem } from '@/components/landing/social-proof/LandingSocialProofBandItem';

<LandingSocialProofBandItem graphic="rating">
  Trusted by 1000+ customers
</LandingSocialProofBandItem>;
```

</ComponentExample>

<ComponentExample
  previewComponent={
    <div className="p-6">
      <LandingSocialProofBandItem graphic="gift">
        20% off today
      </LandingSocialProofBandItem>
    </div>
}

>

```jsx
import { LandingSocialProofBandItem } from '@/components/landing/social-proof/LandingSocialProofBandItem';

<LandingSocialProofBandItem graphic="gift">
  20% off today
</LandingSocialProofBandItem>;
```

</ComponentExample>

## API Reference

| Prop Name                                                                                                                             | Prop Type                                                                 | Required | Default       |
| ------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------- | -------- | ------------- |
| **children** <Tippy>React nodes to be rendered within the component (below the text on smaller screen, right side on larger).</Tippy> | `React.ReactNode` ǀ `string`                                              | Yes      | -             |
| **graphic** <Tippy>Specifies the type of graphic to display alongside the content.</Tippy>                                            | `'none'` ǀ `'checkmark'` ǀ `'gift'` ǀ `'magic'` ǀ `'trophy'` ǀ `'rating'` ǀ `'zap'` ǀ `'rocket'` ǀ `'time'` | No       | `'checkmark'` |
| **customGraphic** <Tippy>Custom SVG graphic to display alongside the content.</Tippy>                                                 | `React.ReactNode`                                                         | No       | -             |

## More Examples

For more even more examples, see our <a href="/demo/landing-page-component-examples" className="fancy-link">Landing Page Component Examples</a> page or see complete landing page examples by <a href="/demo/landing-page-templates" className="fancy-link">Exploring Our Landing Page Templates</a>.

---
title: 'Landing Page Discount Component'
date: '2024-04-04'
lastmod: '2024-07-06'
tags:
  - 'guide'
  - 'components'
  - 'documentation'
  - 'landing-page'
  - 'ui-library'
  - 'react'
  - 'nextjs'
  - 'tailwind-css'
  - 'discount'
  - 'shadcn-ui'
  - 'shadcn-ui-discount'
  - 'shadcn-ui-landing-page'

images:
  - '/static/images/blog/docs/landing-page-discount-component.webp'

summary: 'A component meant to be used in the landing page. Use this to show a discount or offer to encourage users to take action, typically used under call to action buttons.'
layout: PostHub

showThemeSelector: true
showInstallNotice: true

canonicalUrl: 'https://shipixen.com/boilerplate-documentation/landing-page-components/discount'
---

Use this component to show a discount or offer to encourage users to take action, typically used under call to action buttons.

<ComponentExample
  previewComponent={
    <div className="p-6">
      <LandingDiscount
        discountValueText="$350 off"
        discountDescriptionText="for the first 10 customers (2 left)"
      />
    </div>
  }
>
```jsx
import { LandingDiscount } from '@/components/landing/discount/LandingDiscount';

<LandingDiscount
  discountValueText="$350 off"
  discountDescriptionText="for the first 10 customers (2 left)"
/>
```
</ComponentExample>

## Usage

```jsx
import { LandingDiscount } from '@/components/landing/discount/LandingDiscount';
```

```jsx
<LandingDiscount
  discountValueText="$350 off"
  discountDescriptionText="for the first 10 customers (2 left)"
/>
```

## Examples

### No animation

<ComponentExample
  previewComponent={
    <div className="p-6">
      <LandingDiscount
        animated={false}
        discountValueText="$99 off"
        discountDescriptionText="for a limited time"
      />
    </div>
  }
>
  ```jsx
  <LandingDiscount
    animated={false}
    discountValueText="$99 off"
    discountDescriptionText="for a limited time"
  />
  ```
</ComponentExample>

## API Reference

| Prop Name                                                                        | Prop Type | Required | Default                                 |
| -------------------------------------------------------------------------------- | --------- | -------- | --------------------------------------- |
| **discountValueText** <Tippy>Text to display the value of the discount</Tippy>   | `string`  | Yes      | `'$200 off'`                            |
| **discountDescriptionText** <Tippy>Text to describe the discount details</Tippy> | `string`  | Yes      | `'for the first 50 customers (5 left)'` |
| **animated** <Tippy>Controls whether the icon should have an animation</Tippy>   | `boolean` | No       | `true`                                  |

## More Examples

For more even more examples, see our <a href="/demo/landing-page-component-examples" className="fancy-link">Landing Page Component Examples</a> page or see complete landing page examples by <a href="/demo/landing-page-templates" className="fancy-link">Exploring Our Landing Page Templates</a>.

---
title: 'Introduction'
date: '2024-05-01'
lastmod: '2024-05-01'
tags:
  - 'documentation'
  - 'guide'
  - 'getting-started'

summary: 'Find out what Page UI is and how it can help you build your landing page with React and Next.js, using Tailwind CSS and Shadcn UI.'
layout: PostHub
---

<span className="text-xl not-prose">
  A collection of templates, components and examples to create beautiful,
  high-converting landing pages with React and Next.js. Open source & themeable
  with Tailwind CSS.
</span>

Similar to [Shadcn UI](https://ui.shadcn.com/), Page UI is a collection of re-usable components and not a library you can install.<br/>
You can copy paste the components you need and then they become part of your codebase.

This is not a dependency you install, but more like code snippets you can use to build your landing page.<br/>
If you are familiar with Shadcn UI, this should feel similar. In fact, some components are built on top of Shadcn UI.

## Adding components

To add components to your codebase, [install Page UI](/docs/installation#main), then navigate to the [component documentation](/docs/landing-page-components), [templates](https://shipixen.com/demo/landing-page-templates) or [usage examples](https://shipixen.com/demo/landing-page-component-examples) and copy-paste the code into your project.

<Protip
  description={
    <>
      Alternatively, use{' '}
      <a className="fancy-link" href="https://shipixen.com">
        Shipixen
      </a>{' '}
      to get a codebase with all components set up.
    </>
  }
/>

## Motivation

Designing and building landing pages that look good and convert well is hard business.<br/>
Most UI libraries focus on application UI, so when you set up a starer or boilerplate you end up staring at a blank canvas.<br/>
The time spent browsing through bloated templates, figuring out how to port them to your app and then customizing them is time you could spend on your product.

> Start from a blank canvas to create, start from Page UI to innovate.

## Next Steps

Install Page UI in your app by following the [installation guide](/docs/installation).<br/>
Then, check out the [landing page components](/docs/landing-page-components) to see what you can use in your app.

{"$schema": "https://frontmatter.codes/frontmatter.schema.json", "frontMatter.content.pageFolders": [{"title": "blog", "path": "[[workspace]]/"}], "frontMatter.framework.id": "next", "frontMatter.content.publicFolder": "public", "frontMatter.preview.host": "http://localhost:6006", "frontMatter.taxonomy.contentTypes": [{"name": "default", "pageBundle": false, "fields": [{"title": "title", "name": "title", "type": "string"}, {"title": "date", "name": "date", "type": "datetime"}, {"title": "lastmod", "name": "lastmod", "type": "datetime"}, {"title": "tags", "name": "tags", "type": "tags"}, {"title": "summary", "name": "summary", "type": "string"}, {"title": "layout", "name": "layout", "type": "string"}]}]}
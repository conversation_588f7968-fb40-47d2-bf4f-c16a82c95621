{"compilerOptions": {"incremental": true, "target": "ES6", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": false, "forceConsistentCasingInFileNames": true, "noEmit": true, "composite": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "baseUrl": ".", "paths": {"@/components/*": ["components/*"], "@/data/*": ["data/*"], "@/layouts/*": ["layouts/*"], "@/css/*": ["css/*"], "@/lib/*": ["lib/*"], "@/theme/*": ["theme/*"], "@/app/*": ["app/*"], "shipixen-contentlayer/generated": ["./.contentlayer/generated"]}, "plugins": [{"name": "next"}], "strictNullChecks": true}, "include": ["next-env.d.ts", "**/*.js", "**/*.mjs", "**/*.ts", "**/*.tsx", "**/*.mdx", "**/*.jsx", "**/*.json", ".contentlayer/generated", ".contentlayer/generated/**/*.json", ".next/types/**/*.ts", "../components/**/*.ts"], "exclude": ["node_modules"]}
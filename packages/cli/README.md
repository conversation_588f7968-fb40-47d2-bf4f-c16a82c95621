## @page-ui/wizard

[![Page UI CLI version](https://img.shields.io/npm/v/@page-ui/wizard?label=cli&style=flat)](https://github.com/danmindru/page-ui/tree/master/packages/cli)

**Wizard to install Page UI - Landing page components & templates that you can copy 📋 & paste 🍝**<br/>
[pageui.dev](https://pageui.dev)

![ogImg](https://github.com/danmindru/page-ui/assets/1515742/f750b862-360e-467d-b00b-f3435fcb1ef9)

A collection of templates, components and examples to create beautiful,
high-converting landing pages with React and Next.js. Open source & themeable
with Tailwind CSS, similar to [Shadcn UI](https://ui.shadcn.com/).

Quick links:
- [📀 Installation](https://pageui.shipixen.com/docs/installation)
- [📄 Templates](https://shipixen.com/demo/landing-page-templates)
- [👩‍💻 A ton of demos](https://shipixen.com/demo/landing-page-component-examples)

[Read more](https://pageui.dev/docs/introduction) about Page UI.

## Commands

### init
```bash
Usage: page-ui [options] [command]

Options:
  -V, --version   output the version number
  -h, --help      display help for command

Commands:
  init [options]  Setup Page UI: adds required files to your project
  help [command]  display help for command
❯ node ../../danmindru/page-ui/packages/cli/index.mjs init --help

```

#### Options
```bash
Usage: page-ui init [options]

Setup Page UI: adds required files to your project

Options:
  -t, --template <template>  Choose a template from: nextjs, react. Default: nextjs (default: "nextjs")
  -src, --source <source>    Source directory. Defaults to root, but you might be using "src". (default: "")
  -h, --help                 display help for command
```

## 📝 License
Licensed under the [MIT license](https://github.com/danmindru/page-ui/blob/main/LICENSE.md)

-----------------

<a href="https://apihustle.com" target="_blank">
  <img height="60px" src="https://user-images.githubusercontent.com/1515742/215217833-c07183d2-f688-4d1c-86ea-329f3b28f81c.svg" alt="Apihustle Logo" />
</a>

-----------------

Save 10s of hours of work by using Shipixen to generate a customized codebases with your branding, pages and blog <br/>
― then deploy it to Vercel with 1 click.

|                                                                                                                                                                                                                                                                                                                                                                         |                                                                                                                                                                                                         |
| :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| <a href="https://shipixen.com" target="_blank"><img height="60px" src="https://user-images.githubusercontent.com/1515742/281071510-d5c0095d-d336-4857-ad80-d18cf65f4acb.png" alt="Shipixen Logo" /></a> <br/> <b>Shipixen</b> <br/> Create a blog & landing page in minutes with <b>Shipixen</b>. <br/> Try the app on <a href="https://shipixen.com">shipixen.com</a>. | <a href="https://shipixen.com" target="_blank"><img width="300px" src="https://user-images.githubusercontent.com/1515742/281077548-57b24773-3c2a-4e89-b088-cc3945d7037b.png" alt="Shipixen Logo" /></a> |

-----------------

Apihustle is a collection of tools to test, improve and get to know your API inside and out. <br/>
[apihustle.com](https://apihustle.com) <br/>

|                                                                                                                                                                                                   |              |                                                      |                                              |
| :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | :----------- | :--------------------------------------------------- | :------------------------------------------- |
| <a href="https://shipixen.com" target="_blank"><img height="70px" src="https://github.com/apihustle/apihustle/assets/1515742/3af97560-d774-4149-96c5-65d3cc530a5a" alt="Shipixen Logo" /></a>     | **Shipixen** | Create a personalized blog & landing page in minutes | [shipixen.com](https://shipixen.com)         |
| <a href="https://pageui.dev" target="_blank"><img height="70px" src="https://github.com/apihustle/apihustle/assets/1515742/953cc5ab-bbf4-4a19-9b16-c74d218b63b4" alt="Page UI Logo" /></a>        | **Page UI**  | Landing page UI components for React & Next.js       | [pageui.dev](https://pageui.dev)             |
| <a href="https://clobbr.app" target="_blank"><img height="70px" src="https://github.com/apihustle/apihustle/assets/1515742/50c11d46-a025-40fd-b154-0a5984556f6e" alt="Clobbr Logo" /></a>         | **Clobbr**   | Load test your API endpoints | [clobbr.app](https://clobbr.app)             |
| <a href="https://crontap.com" target="_blank"><img height="70px" src="https://github.com/apihustle/apihustle/assets/1515742/fe1aac71-b663-4f8e-a225-0c47b2eee14d" alt="Crontap Logo" /></a>       | **Crontap**  | Schedule API calls using cron syntax.                | [crontap.com](https://crontap.com)           |
| <a href="https://tool.crontap.com" target="_blank"><img height="70px" src="https://github.com/apihustle/apihustle/assets/1515742/713ff923-b03c-43ec-9cfd-75e542d0f5c4" alt="CronTool Logo" /></a> | **CronTool** | Debug multiple cron expressions on a calendar.       | [tool.crontap.com](https://tool.crontap.com) |

-----------------

<img height="60px" src="https://github.com/danmindru/page-ui/assets/1515742/30259ef0-6085-401d-ab24-e9d1f9b5fc05" alt="Page UI logo" /> <br/>
Page UI ❤️ Open Source

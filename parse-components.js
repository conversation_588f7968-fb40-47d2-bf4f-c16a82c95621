// 解析landing-page-components目录下的mdx文件，提取组件的描述和api，并保存到components-data.json文件中

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Directory containing the MDX files
const DOCS_DIR = path.join(__dirname, 'website/data/docs/landing-page-components');
const OUTPUT_FILE = './components-data.json';

function extractDescription(content) {
  // Remove frontmatter first
  const withoutFrontmatter = content.replace(/^---\n[\s\S]*?\n---\n/, '');
  
  // Find the first ComponentExample tag
  const componentExampleMatch = withoutFrontmatter.match(/<ComponentExample[\s\S]*?>/);
  if (!componentExampleMatch) return '';
  
  // Extract content before the first ComponentExample
  const beforeComponentExample = withoutFrontmatter.substring(0, componentExampleMatch.index);
  
  // Clean up the description - remove empty lines and trim
  const description = beforeComponentExample
    .split('\n')
    .map(line => line.trim())
    .filter(line => line.length > 0)
    .join(' ')
    .trim();
  
  return description;
}

function extractSectionContent(content, sectionTitle) {
  const regex = new RegExp(`## ${sectionTitle}\\n([\\s\\S]*?)(?=\\n## |$)`, 'i');
  const match = content.match(regex);
  return match ? match[1].trim() : '';
}

function removeComponentExampleTags(content) {
  // 更智能地移除ComponentExample标签，正确处理嵌套的JSX结构
  let result = content;
  let startIndex = 0;
  
  while (true) {
    // 查找ComponentExample开始标签
    const componentExampleStart = result.indexOf('<ComponentExample', startIndex);
    if (componentExampleStart === -1) break;
    
    // 从ComponentExample开始位置查找匹配的结束标签
    let pos = componentExampleStart + '<ComponentExample'.length;
    let braceCount = 0;
    let inString = false;
    let stringChar = '';
    let tagClosed = false;
    
    // 解析ComponentExample开始标签，正确处理其中的JSX
    while (pos < result.length && !tagClosed) {
      const char = result[pos];
      
      if (!inString) {
        if (char === '"' || char === "'") {
          inString = true;
          stringChar = char;
        } else if (char === '{') {
          braceCount++;
        } else if (char === '}') {
          braceCount--;
        } else if (char === '>' && braceCount === 0) {
          tagClosed = true;
        }
      } else {
        if (char === stringChar && result[pos - 1] !== '\\') {
          inString = false;
          stringChar = '';
        }
      }
      
      pos++;
    }
    
    if (!tagClosed) break; // 如果没有找到标签结束，跳出循环
    
    const tagEndPos = pos;
    
    // 查找对应的</ComponentExample>结束标签
    const endTag = '</ComponentExample>';
    const endTagPos = result.indexOf(endTag, tagEndPos);
    
    if (endTagPos === -1) break; // 如果没有找到结束标签，跳出循环
    
    // 提取ComponentExample标签内的内容
    const innerContent = result.substring(tagEndPos, endTagPos);
    
    // 替换整个ComponentExample标签为其内容
    result = result.substring(0, componentExampleStart) + innerContent + result.substring(endTagPos + endTag.length);
    
    // 继续从替换后的位置开始查找
    startIndex = componentExampleStart + innerContent.length;
  }
  
  return result;
}

function formatApiContent(usage, examples, apiReference) {
  let apiContent = '';
  
  if (usage) {
    apiContent += `<Usage>\n${usage}\n</Usage>\n\n`;
  }
  
  if (examples) {
    const cleanExamples = removeComponentExampleTags(examples);
    apiContent += `<Examples>\n${cleanExamples}\n</Examples>\n\n`;
  }
  
  if (apiReference) {
    apiContent += `<PropsReference>\n${apiReference}\n</PropsReference>`;
  }
  
  return apiContent.trim();
}

function getComponentNameFromFilename(filename) {
  // Remove .mdx extension and convert kebab-case to PascalCase
  const baseName = path.basename(filename, '.mdx');
  return baseName
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join('');
}

function parseComponentFiles() {
  const result = {};
  
  try {
    const files = fs.readdirSync(DOCS_DIR);
    const mdxFiles = files.filter(file => file.endsWith('.mdx'));
    
    console.log(`Found ${mdxFiles.length} MDX files to process...`);
    
    for (const file of mdxFiles) {
      const filePath = path.join(DOCS_DIR, file);
      const content = fs.readFileSync(filePath, 'utf8');
      
      console.log(`Processing: ${file}`);
      
      // Extract component name from filename
      const componentName = getComponentNameFromFilename(file);
      
      // Extract description
      const description = extractDescription(content);
      
      // Extract API sections
      const usage = extractSectionContent(content, 'Usage');
      const examples = extractSectionContent(content, 'Examples');
      const apiReference = extractSectionContent(content, 'API Reference');
      
      // Format API content
      const api = formatApiContent(usage, examples, apiReference);
      
      // Add to result
      result[componentName] = {
        description: description,
        api: api
      };
      
      console.log(`✓ Processed ${componentName}`);
    }
    
    console.log(`\nSuccessfully processed ${Object.keys(result).length} components`);
    return result;
    
  } catch (error) {
    console.error('Error processing files:', error);
    return {};
  }
}

function main() {
  console.log('Starting component documentation parsing...\n');
  
  const componentsData = parseComponentFiles();
  
  if (Object.keys(componentsData).length > 0) {
    try {
      fs.writeFileSync(OUTPUT_FILE, JSON.stringify(componentsData, null, 2));
      console.log(`\n✅ Successfully generated ${OUTPUT_FILE}`);
      console.log(`📊 Total components: ${Object.keys(componentsData).length}`);
    } catch (error) {
      console.error('❌ Error writing output file:', error);
    }
  } else {
    console.log('❌ No components data to write');
  }
}

// Run the script
main(); 
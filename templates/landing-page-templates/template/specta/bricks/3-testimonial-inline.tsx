import { LandingTestimonialInline } from '@/components/landing/testimonial/LandingTestimonialInline';
import { LandingTestimonialInlineItem } from '@/components/landing/testimonial/LandingTestimonialInlineItem';

export default function Component() {
  return (
    <LandingTestimonialInline>
      <LandingTestimonialInlineItem
        name="<PERSON>"
        text="I've already seen a tangible impact on engagement and growth"
        suffix="Marketing at Google"
      />

      <LandingTestimonialInlineItem
        name="<PERSON>"
        text="Best app on the market without a doubt"
      />

      <LandingTestimonialInlineItem
        name="<PERSON> Doe"
        text="I've created twenty videos in two days without any issues"
        suffix="CEO of Instagram"
      />

      <LandingTestimonialInlineItem
        name="<PERSON>"
        text="I've been able to automate my entire workflow. 6/5 stars"
        suffix="DevOps at Meta"
      />
    </LandingTestimonialInline>
  );
}

{"About": {"description": "The About section provides a sleek, modern way to showcase your company information, mission, vision, and key statistics. These components are designed to build trust and communicate your core values to your audience. ## About Section The `LandingAboutSection` component displays a heading, description, and an image side by side, perfect for introducing your company or team.", "api": "<Usage>\n```jsx\nimport { LandingAboutSection } from '@/components/landing/about/LandingAboutSection';\n```\n\n```jsx\n<LandingAboutSection\n  title=\"About Us\"\n  description=\"We are committed to creating a safe and supportive environment where you can explore your thoughts and feelings, develop coping strategies, and achieve your mental health goals.\"\n  imageSrc=\"/static/images/backdrop-1.webp\"\n  imageAlt=\"About us image\"\n/>\n```\n</Usage>\n\n<Examples>\n### About Section with Background\n\n\n\n```jsx\nimport { LandingAboutSection } from '@/components/landing/about/LandingAboutSection';\n\n<LandingAboutSection\n  title=\"Our Story\"\n  description=\"Founded in 2010, we've grown from a small team of passionate developers to a global agency serving clients worldwide. Our journey has been defined by innovation, quality, and a commitment to excellence in everything we do.\"\n  imageSrc=\"/static/images/backdrop-2.webp\"\n  imageAlt=\"Team working together\"\n  withBackground={true}\n  variant=\"secondary\"\n/>\n```\n\n\n\n### About Section with Background Glow\n\n\n\n```jsx\nimport { LandingAboutSection } from '@/components/landing/about/LandingAboutSection';\n\n<LandingAboutSection\n  title=\"About Our Platform\"\n  description=\"Our platform combines cutting-edge technology with user-friendly design to create a seamless experience for our users. We're constantly innovating and improving to provide the best possible service.\"\n  imageSrc=\"/static/images/backdrop-3.webp\"\n  imageAlt=\"Platform showcase\"\n  withBackground={true}\n  withBackgroundGlow={true}\n  variant=\"primary\"\n/>\n```\n\n\n\n### Vision & Mission Section\n\nThe `LandingVisionMissionSection` component displays your company's vision and mission statements in a clean, organized layout.\n\n\n\n```jsx\nimport { LandingVisionMissionSection } from '@/components/landing/about/LandingVisionMissionSection';\n\n<LandingVisionMissionSection\n  title=\"Empowering Global Mental Health Access\"\n  visionTitle=\"OUR VISION\"\n  visionDescription=\"To be the leading mental health platform, providing accessible, compassionate, and innovative care for emotional and mental well-being worldwide.\"\n  missionTitle=\"OUR MISSION\"\n  missionDescription=\"To support individuals in achieving mental and emotional balance through tailored therapy, education, ensuring everyone has access to professional care.\"\n/>\n```\n\n\n\n### Vision & Mission with Background\n\n\n\n```jsx\nimport { LandingVisionMissionSection } from '@/components/landing/about/LandingVisionMissionSection';\n\n<LandingVisionMissionSection\n  title=\"Our Core Values\"\n  visionTitle=\"WHY WE EXIST\"\n  visionDescription=\"To transform how businesses approach digital solutions, making advanced technology accessible to companies of all sizes.\"\n  missionTitle=\"HOW WE WORK\"\n  missionDescription=\"By combining innovative design thinking with cutting-edge development practices to create solutions that are both powerful and user-friendly.\"\n  withBackground={true}\n  variant=\"secondary\"\n/>\n```\n\n\n\n### Complete About Page Example\n\nThis example combines the About Section, Vision & Mission Section, and Stats Section to create a comprehensive About page.\n\n\n\n```jsx\nimport { LandingAboutSection } from '@/components/landing/about/LandingAboutSection';\nimport { LandingVisionMissionSection } from '@/components/landing/about/LandingVisionMissionSection';\nimport { LandingStatsSection } from '@/components/landing/stats/LandingStatsSection';\n\n<div className=\"flex flex-col w-full\">\n  <LandingAboutSection\n    title=\"About Our Agency\"\n    description=\"We are a full-service digital agency specializing in web development, design, and marketing. With over a decade of experience, we've helped hundreds of clients achieve their goals and grow their online presence.\"\n    imageSrc=\"/static/images/backdrop-4.webp\"\n    imageAlt=\"Agency team photo\"\n  />\n\n  <LandingStatsSection\n    columnsDesktop={3}\n    hasBorders={true}\n    stats={[\n      { value: '150+', description: 'Over 150 projects successfully delivered.' },\n      { value: '9', label: 'members', description: 'Our core team consists of 9 experts.' },\n      { value: '300+', label: 'creative freelancers', description: 'We collaborate with 300+ creative freelancers.' },\n      { value: '25+', label: 'awards & featured', description: 'Recognized with 25+ awards and featured in industry publications.' },\n      { value: '10+', label: 'years experience', description: 'Bringing 10+ years of design experience to every project.' },\n      { value: '$25B+', label: 'revenue', description: 'Our work has contributed to over $25 billion in revenue.' }\n    ]}\n  />\n\n  <LandingVisionMissionSection\n    title=\"Our Guiding Principles\"\n    visionTitle=\"VISION\"\n    visionDescription=\"To be the most trusted partner for businesses seeking to transform their digital presence and achieve meaningful growth.\"\n    missionTitle=\"MISSION\"\n    missionDescription=\"To deliver exceptional digital solutions that drive real business results through a combination of creativity, technical excellence, and strategic thinking.\"\n    withBackground={true}\n    withBackgroundGlow={true}\n    variant=\"primary\"\n  />\n</div>\n```\n\n\n\n### Complete example with custom stats\n\n\n\n```jsx\nimport { LandingAboutSection } from '@/components/landing/about/LandingAboutSection';\nimport { LandingStatItem } from '@/components/landing/stats/LandingStatItem';\n\n<div className=\"flex flex-col w-full\">\n  <LandingAboutSection\n    title=\"About Us\"\n    description=\"We're a team of passionate designers and developers building exceptional digital experiences.\"\n    imageSrc=\"/static/images/backdrop-5.webp\"\n    imageAlt=\"Our team\"\n  />\n\n  <div className=\"py-8\">\n    <div className=\"container-wide px-6\">\n      <h2 className=\"w-full text-3xl font-semibold mb-6 text-left\">Key Statistics</h2>\n      <div className=\"grid grid-cols-2 md:grid-cols-3\">\n        <LandingStatItem\n          value=\"150+\"\n          label=\"projects\"\n          description=\"Successfully completed projects\"\n          variant=\"primary\"\n        />\n        <LandingStatItem\n          value=\"9\"\n          label=\"members\"\n          description=\"Dedicated members\"\n          variant=\"primary\"\n        />\n        <LandingStatItem\n          value=\"10+\"\n          label=\"years\"\n          description=\"Years of industry experience\"\n          variant=\"primary\"\n        />\n      </div>\n    </div>\n  </div>\n</div>\n```\n\n\n</Examples>\n\n<PropsReference>\n### LandingAboutSection Props\n\n| Prop Name | Prop Type | Required | Default |\n| --- | --- | ----- | ---- |\n| **title** <Tippy>The main heading for the About section</Tippy> | `string` | No | `'About Us'` |\n| **titleComponent** <Tippy>Custom component to replace the default title</Tippy> | `React.ReactNode` | No | `-` |\n| **description** <Tippy>The description text for the About section</Tippy> | `string \\| React.ReactNode` | No | `-` |\n| **descriptionComponent** <Tippy>Custom component to replace the default description</Tippy> | `React.ReactNode` | No | `-` |\n| **imageSrc** <Tippy>The URL for the image to display</Tippy> | `string` | No | `'/static/images/about-image.webp'` |\n| **imageAlt** <Tippy>The alt text for the image</Tippy> | `string` | No | `'About us image'` |\n| **textPosition** <Tippy>The alignment of the text content</Tippy> | `'center' \\| 'left'` | No | `'left'` |\n| **withBackground** <Tippy>Whether to display a background color</Tippy> | `boolean` | No | `false` |\n| **withBackgroundGlow** <Tippy>Whether to display a background glow effect</Tippy> | `boolean` | No | `false` |\n| **variant** <Tippy>Visual style variant of the section</Tippy> | `'primary' \\| 'secondary'` | No | `'primary'` |\n| **backgroundGlowVariant** <Tippy>Visual style variant of the background glow</Tippy> | `'primary' \\| 'secondary'` | No | `'primary'` |\n| **className** <Tippy>Additional classes to apply to the section wrapper</Tippy> | `string` | No | `-` |\n| **innerClassName** <Tippy>Additional classes to apply to the inner container</Tippy> | `string` | No | `-` |\n\n### LandingVisionMissionSection Props\n\n| Prop Name | Prop Type | Required | Default |\n| --- | --- | ----- | ---- |\n| **title** <Tippy>The main heading for the section</Tippy> | `string` | No | `-` |\n| **titleComponent** <Tippy>Custom component to replace the default title</Tippy> | `React.ReactNode` | No | `-` |\n| **visionTitle** <Tippy>The heading for the vision section</Tippy> | `string` | No | `-` |\n| **visionDescription** <Tippy>The description text for the vision</Tippy> | `string \\| React.ReactNode` | No | `-` |\n| **missionTitle** <Tippy>The heading for the mission section</Tippy> | `string` | No | `-` |\n| **missionDescription** <Tippy>The description text for the mission</Tippy> | `string \\| React.ReactNode` | No | `-` |\n| **variant** <Tippy>Visual style variant of the section</Tippy> | `'primary' \\| 'secondary'` | No | `'primary'` |\n| **withBackground** <Tippy>Whether to display a background color</Tippy> | `boolean` | No | `false` |\n| **withBackgroundGlow** <Tippy>Whether to display a background glow effect</Tippy> | `boolean` | No | `false` |\n| **backgroundGlowVariant** <Tippy>Visual style variant of the background glow</Tippy> | `'primary' \\| 'secondary'` | No | `'primary'` |\n| **textPosition** <Tippy>The alignment of the text content</Tippy> | `'center' \\| 'left'` | No | `'left'` |\n| **className** <Tippy>Additional classes to apply to the section wrapper</Tippy> | `string` | No | `-` |\n| **innerClassName** <Tippy>Additional classes to apply to the inner container</Tippy> | `string` | No | `-` |\n| **children** <Tippy>Additional content to display below the vision and mission content</Tippy> | `React.ReactNode` | No | `-` |\n</PropsReference>"}, "AppstoreButton": {"description": "This component displays app store download buttons with proper branding. It supports multiple app stores and black/white variants.", "api": "<Usage>\n```jsx\nimport { LandingAppStoreButton } from '@/components/landing/app-store-button/LandingAppStoreButton';\n```\n\n```jsx\n<LandingAppStoreButton appStore=\"ios-appstore\" />\n```\n</Usage>\n\n<Examples>\n### Use as Link\n\n\n\n```jsx\n<LandingAppStoreButton appStore=\"ios-appstore\" asChild>\n  <a href=\"https://www.apple.com/app-store\">Download</a>\n</LandingAppStoreButton>\n```\n\n\n\n### Use with onClick Event\nYou can also use the onClick event handler for custom interactions.\n\n\n\n```jsx\n<LandingAppStoreButton\n  appStore=\"google-playstore\"\n  onClick={() => console.log('Button clicked!')}\n/>\n```\n\n\n\n### Explicit Theme Variant\nYou can explicitly set the button variant to white or black, overriding the default theme.\n\n\n\n```jsx\n<div className=\"flex flex-wrap gap-4\">\n  <LandingAppStoreButton appStore=\"ios-appstore\" variant=\"white\" />\n  <LandingAppStoreButton appStore=\"ios-appstore\" variant=\"black\" />\n</div>\n```\n\n\n\n### Different Sizes\nThe button supports different sizes, corresponding to regular `<Button>` component sizes.\n\n\n\n```jsx\n<div className=\"flex flex-wrap gap-4\">\n  <LandingAppStoreButton appStore=\"ios-appstore\" size=\"sm\" />\n  <LandingAppStoreButton appStore=\"ios-appstore\" size=\"default\" />\n  <LandingAppStoreButton appStore=\"ios-appstore\" size=\"lg\" />\n  <LandingAppStoreButton appStore=\"ios-appstore\" size=\"xl\" />\n</div>\n```\n\n\n\n### Custom Styling\nYou can add custom styling using the className prop.\n\n\n\n```jsx\n<LandingAppStoreButton\n  appStore=\"ios-appstore\"\n  className=\"shadow-lg hover:shadow-xl transition-shadow duration-300\"\n/>\n```\n\n\n</Examples>\n\n<PropsReference>\n| Prop Name | Prop Type | Required | Default |\n| --------- | --------- | -------- | ------- |\n| **appStore** <Tippy>The app store type to display.</Tippy> | `'ios-appstore'` ǀ `'mac-appstore'` ǀ `'microsoft-store'` ǀ `'google-playstore'` | Yes | - |\n| **variant** <Tippy>Explicitly set the button theme variant.</Tippy> | `'white'` ǀ `'black'` | No | `'black'` |\n| **size** <Tippy>Size of the button.</Tippy> | `'default'` ǀ `'sm'` ǀ `'lg'` ǀ `'xl'` | No | `'default'` |\n| **onClick** <Tippy>Event handler for the button click.</Tippy> | `(event: React.MouseEvent<HTMLButtonElement>) => void` | No | - |\n| **asChild** <Tippy>When true, the component will render its child component instead of a button element.</Tippy> | `boolean` | No | `false` |\n| **className** <Tippy>Additional CSS classes to apply to the button.</Tippy> | `string` | No | `''` |\n</PropsReference>"}, "Band": {"description": "Use this component to 'break' the layout flow of your landing page and either present social proof or showcase the value proposition of the product. With this component you display a full-width, brand-colored section that displays a title, description and some product logos or icons.", "api": "<Usage>\n```jsx\nimport { LandingBandSection } from '@/components/landing/LandingBand';\nimport { ChromeIcon, FigmaIcon, GithubIcon } from 'lucide-react';\n```\n\n```jsx\n<LandingBandSection\n  title={'20-100h'}\n  description={'Saved on development by using Shipixen'}\n  supportingComponent={\n    <>\n      <ChromeIcon className=\"w-12 h-12\" />\n      <FigmaIcon className=\"w-12 h-12\" />\n      <GithubIcon className=\"w-12 h-12\" />\n    </>\n  }\n/>\n```\n</Usage>\n\n<Examples>\n### With Social Proof\n\n\n\n```jsx\nimport { LandingBandSection } from '@/components/landing/LandingBand';\nimport { LandingSocialProof } from '@/components/landing/social-proof/LandingSocialProof';\n\nconst avatarItems = [\n  {\n    imageSrc: '/static/images/people/1.webp',\n    name: '<PERSON>',\n  },\n  {\n    imageSrc: '/static/images/people/2.webp',\n    name: '<PERSON>',\n  },\n  {\n    imageSrc: '/static/images/people/3.webp',\n    name: '<PERSON>',\n  },\n]\n\n<LandingBandSection\n  title='4.9/5 stars'\n  description='Our customers love our product'\n  supportingComponent={\n    <LandingSocialProof\n      showRating\n      numberOfUsers={99}\n      avatarItems={avatarItems}\n    />\n  }\n/>\n```\n\n\n\n### Customization\n\n\n\n```jsx\nimport { ChromeIcon, FigmaIcon, GithubIcon } from 'lucide-react';\nimport { LandingBandSection } from '@/components/landing/LandingBand';\n\n<LandingBandSection\n  variant=\"secondary\"\n  title=\"20-100h\"\n  description=\"Saved on design\"\n  supportingComponent={\n    <>\n      <ChromeIcon className=\"w-12 h-12\" />\n      <FigmaIcon className=\"w-12 h-12\" />\n      <GithubIcon className=\"w-12 h-12\" />\n    </>\n  }\n/>;\n```\n\n\n</Examples>\n\n<PropsReference>\n| Prop Name                                                                                                                             | Prop Type                   | Required | Default     |\n| ------------------------------------------------------------------------------------------------------------------------------------- | --------------------------- | -------- | ----------- |\n| **children** <Tippy>React nodes to be rendered within the component (below the text on smaller screen, right side on larger).</Tippy> | `React.ReactNode`           | No       | -           |\n| **title** <Tippy>The title to display. Can be a string or a React node for custom components</Tippy>                                  | `string \\| React.ReactNode` | No       | -           |\n| **titleComponent** <Tippy>Custom React component to use instead of the default title</Tippy>                                          | `React.ReactNode`           | No       | -           |\n| **description** <Tippy>Description text or a React node for custom description components</Tippy>                                     | `string \\| React.ReactNode` | No       | -           |\n| **descriptionComponent** <Tippy>Custom React component to use instead of the default description</Tippy>                              | `React.ReactNode`           | No       | -           |\n| **supportingComponent** <Tippy>A React component to support the main content, such as images or illustrations</Tippy>                 | `React.ReactNode`           | No       | -           |\n| **withBackground** <Tippy>Whether the section should have a background color</Tippy>                                                  | `boolean`                   | No       | `true`      |\n| **variant** <Tippy>Color variant of the background</Tippy>                                                                            | `'primary' \\| 'secondary'`  | No       | `'primary'` |\n</PropsReference>"}, "BentoGrid": {"description": "The `LandingBentoGridSection` component is a flexible container for creating visually appealing grid layouts for landing pages. It provides a grid layout with customizable columns and rows, and can be used to display a variety of content types. It can contain generic items or specialized Bento Grid items as children. These specialized items extend the basic bento grid with pre-designed layouts for displaying tech specs, feature highlights, and product information.", "api": "<Usage>\n```jsx\nimport { LandingBentoGridSection } from '@/components/landing/bento-grid/LandingBentoGridSection';\nimport { LandingBentoGridIconItem } from '@/components/landing/bento-grid/LandingBentoGridIconItem';\nimport { LandingBentoGridNumberItem } from '@/components/landing/bento-grid/LandingBentoGridNumberItem';\nimport { LandingBentoGridImageItem } from '@/components/landing/bento-grid/LandingBentoGridImageItem';\n```\n\n```jsx\n<LandingBentoGridSection\n  title=\"Processor Specifications\"\n  description=\"Technical details of our latest chipset\"\n>\n  <LandingBentoGridIconItem\n    icon={<SparklesIcon className=\"w-12 h-12\" />}\n    bottomText=\"High-speed I/O\"\n  />\n  <LandingBentoGridNumberItem\n    topText=\"Up to\"\n    number=\"20%\"\n    bottomText=\"faster CPU\"\n  />\n  <LandingBentoGridImageItem\n    topText=\"Data-center-class\"\n    imageSrc=\"/static/images/backdrop-1.webp\"\n    imageAlt=\"Data-center-class\"\n    bottomText=\"performance per watt\"\n    colSpan={2}\n    rowSpan={2}\n  />\n\n  {/* more items */}\n</LandingBentoGridSection>\n```\n</Usage>\n\n<Examples>\n### Icon-based Grid Items\n\n\n\n```jsx\nimport { LandingBentoGridSection } from '@/components/landing/bento-grid/LandingBentoGridSection';\nimport { LandingBentoGridIconItem } from '@/components/landing/bento-grid/LandingBentoGridIconItem';\n\n<LandingBentoGridSection\n  title=\"Technical Specifications\"\n  description=\"Cutting-edge technology in every component\"\n>\n  <LandingBentoGridIconItem\n    icon={<SparklesIcon className=\"w-10 h-10\" />}\n    bottomText=\"High-speed I/O\"\n  />\n  <LandingBentoGridIconItem\n    topText=\"Dedicated\"\n    icon={<LayersIcon className=\"w-10 h-10\" />}\n    bottomText=\"media engine\"\n  />\n  <LandingBentoGridIconItem\n    topText=\"Advanced\"\n    icon={<LineChartIcon className=\"w-10 h-10\" />}\n    bottomText=\"process technology\"\n  />\n  <LandingBentoGridIconItem\n    icon={<BatteryIcon className=\"w-10 h-10\" />}\n    bottomText=\"More battery\"\n    topText=\"More power\"\n  />\n</LandingBentoGridSection>\n```\n\n\n\n### Number-based Grid Items\n\n\n\n```jsx\nimport { LandingBentoGridSection } from '@/components/landing/bento-grid/LandingBentoGridSection';\nimport { LandingBentoGridNumberItem } from '@/components/landing/bento-grid/LandingBentoGridNumberItem';\n\n<LandingBentoGridSection\n  title=\"Performance Metrics\"\n  description=\"Benchmark results for our latest generation\"\n>\n  <LandingBentoGridNumberItem\n    topText=\"Up to\"\n    number=\"20%\"\n    bottomText=\"faster CPU\"\n  />\n  <LandingBentoGridNumberItem\n    topText=\"Up to\"\n    number=\"30%\"\n    bottomText=\"faster GPU\"\n  />\n  <LandingBentoGridNumberItem\n    number=\"60 billion\"\n    bottomText=\"transistors\"\n    topText=\"Over\"\n  />\n  <LandingBentoGridNumberItem\n    topText=\"Up to\"\n    number=\"800 GB/s\"\n    bottomText=\"Memory bandwidth\"\n    colSpan={4}\n  />\n</LandingBentoGridSection>\n```\n\n\n\n### Image-based Grid Items\n\n\n\n```jsx\nimport { LandingBentoGridSection } from '@/components/landing/bento-grid/LandingBentoGridSection';\nimport { LandingBentoGridImageItem } from '@/components/landing/bento-grid/LandingBentoGridImageItem';\n\n<LandingBentoGridSection\n  title=\"Product Gallery\"\n  description=\"Our latest innovations in images\"\n>\n  <LandingBentoGridImageItem\n    imageSrc=\"/static/images/backdrop-1.webp\"\n    bottomText=\"Next-generation display\"\n    colSpan={2}\n  />\n  <LandingBentoGridImageItem\n    topText=\"Pro Camera\"\n    imageSrc=\"/static/images/backdrop-2.webp\"\n    bottomText=\"128MP\"\n  />\n  <LandingBentoGridImageItem\n    topText=\"Redesigned\"\n    imageSrc=\"/static/images/backdrop-3.webp\"\n    bottomText=\"Architecture\"\n  />\n</LandingBentoGridSection>\n```\n\n\n</Examples>\n\n<PropsReference>\n### LandingBentoGridIconItem Props\n\n| Prop Name | Prop Type | Required | Default |\n| --- | --- | ----- | ---- |\n| **variant** <Tippy>Color variant for the item</Tippy> | `'default' \\| 'primary' \\| 'secondary'` | No | `'default'` |\n| **topText** <Tippy>Text displayed above the icon</Tippy> | `string` | No | - |\n| **topTextComponent** <Tippy>Custom component for top text</Tippy> | `React.ReactNode` | No | - |\n| **icon** <Tippy>Icon to display in the center</Tippy> | `React.ReactNode` | No | - |\n| **bottomText** <Tippy>Text displayed below the icon</Tippy> | `string` | No | - |\n| **bottomTextComponent** <Tippy>Custom component for bottom text</Tippy> | `React.ReactNode` | No | - |\n| **colSpan** <Tippy>Number of columns the item should span</Tippy> | `1 \\| 2 \\| 3 \\| 4` | No | `1` |\n| **rowSpan** <Tippy>Number of rows the item should span</Tippy> | `1 \\| 2 \\| 3` | No | `1` |\n| **className** <Tippy>Additional CSS classes</Tippy> | `string` | No | `''` |\n| **href** <Tippy>Link URL to make the item clickable</Tippy> | `string` | No | - |\n| **asChild** <Tippy>Whether to render children as the main component</Tippy> | `boolean` | No | `false` |\n\n### LandingBentoGridNumberItem Props\n\n| Prop Name | Prop Type | Required | Default |\n| --- | --- | ----- | ---- |\n| **variant** <Tippy>Color variant for the item</Tippy> | `'default' \\| 'primary' \\| 'secondary'` | No | `'default'` |\n| **topText** <Tippy>Text displayed above the number</Tippy> | `string` | No | - |\n| **topTextComponent** <Tippy>Custom component for top text</Tippy> | `React.ReactNode` | No | - |\n| **number** <Tippy>Number or text to display in the center</Tippy> | `string \\| number` | No | - |\n| **bottomText** <Tippy>Text displayed below the number</Tippy> | `string` | No | - |\n| **bottomTextComponent** <Tippy>Custom component for bottom text</Tippy> | `React.ReactNode` | No | - |\n| **colSpan** <Tippy>Number of columns the item should span</Tippy> | `1 \\| 2 \\| 3 \\| 4` | No | `1` |\n| **rowSpan** <Tippy>Number of rows the item should span</Tippy> | `1 \\| 2 \\| 3` | No | `1` |\n| **className** <Tippy>Additional CSS classes</Tippy> | `string` | No | `''` |\n| **href** <Tippy>Link URL to make the item clickable</Tippy> | `string` | No | - |\n| **asChild** <Tippy>Whether to render children as the main component</Tippy> | `boolean` | No | `false` |\n\n### LandingBentoGridImageItem Props\n\n| Prop Name | Prop Type | Required | Default |\n| --- | --- | ----- | ---- |\n| **variant** <Tippy>Color variant for the item</Tippy> | `'default' \\| 'primary' \\| 'secondary'` | No | `'default'` |\n| **topText** <Tippy>Text displayed above the image</Tippy> | `string` | No | - |\n| **topTextComponent** <Tippy>Custom component for top text</Tippy> | `React.ReactNode` | No | - |\n| **imageSrc** <Tippy>URL of the image to display</Tippy> | `string` | No | - |\n| **imageAlt** <Tippy>Alt text for the image</Tippy> | `string` | No | `''` |\n| **imageComponent** <Tippy>Custom component instead of default Image</Tippy> | `React.ReactNode` | No | - |\n| **imageFill** <Tippy>Whether the image should fill its container</Tippy> | `boolean` | No | `true` |\n| **imageHeight** <Tippy>Height of the image in pixels</Tippy> | `number` | No | `100` |\n| **imageWidth** <Tippy>Width of the image in pixels</Tippy> | `number` | No | `100` |\n| **bottomText** <Tippy>Text displayed below the image</Tippy> | `string` | No | - |\n| **bottomTextComponent** <Tippy>Custom component for bottom text</Tippy> | `React.ReactNode` | No | - |\n| **colSpan** <Tippy>Number of columns the item should span</Tippy> | `1 \\| 2 \\| 3 \\| 4` | No | `1` |\n| **rowSpan** <Tippy>Number of rows the item should span</Tippy> | `1 \\| 2 \\| 3` | No | `1` |\n| **className** <Tippy>Additional CSS classes</Tippy> | `string` | No | `''` |\n| **href** <Tippy>Link URL to make the item clickable</Tippy> | `string` | No | - |\n| **asChild** <Tippy>Whether to render children as the main component</Tippy> | `boolean` | No | `false` |\n</PropsReference>"}, "BlogList": {"description": "The `LandingBlogList` component displays a collection of blog posts (`LandingBlogPost`) in either a grid or list format. It includes customizable titles, descriptions, and various styling options.", "api": "<Usage>\nImport the component and use it to display a collection of blog posts:\n\n```jsx\nimport { LandingBlogList } from '@/components/landing/LandingBlogList';\nimport { LandingBlogPost } from '@/components/landing/blog/LandingBlogPost';\n```\n\n```jsx\n<LandingBlogList\n  title=\"Latest Articles\"\n  description=\"Check out our latest blog posts and stay updated with the newest trends.\"\n>\n  <LandingBlogPost\n    post={{\n      slug: \"getting-started\",\n      date: \"2023-08-10\",\n      title: \"Getting Started with Shipixen\",\n      summary: \"Learn how to set up your first project using Shipixen templates and components.\",\n      tags: [\"Tutorial\", \"Beginners\"],\n      images: [\"/static/images/backdrop-1.webp\"],\n      readingTime: 5,\n      author: {\n        name: \"<PERSON>\",\n        avatar: \"/static/images/people/1.webp\"\n      }\n    }}\n  />\n</LandingBlogList>\n```\n</Usage>\n\n<Examples>\n### Grid Display\n\nDisplay blog posts in a grid format instead of a list.\n\n\n\n```jsx\n<LandingBlogList\n  title=\"Featured Articles\"\n  description=\"Our most popular content curated for you.\"\n  display=\"grid\"\n>\n  <LandingBlogPost\n    post={{\n      slug: \"react-patterns\",\n      date: \"2023-07-15\",\n      title: \"Essential React Patterns\",\n      summary: \"Learn the essential patterns that every React developer should know.\",\n      tags: [\"React\", \"Patterns\"],\n      images: [\"/static/images/backdrop-3.webp\"],\n      readingTime: 6,\n      author: {\n        name: \"Alex Johnson\",\n        avatar: \"/static/images/people/3.webp\"\n      }\n    }}\n  />\n  <LandingBlogPost\n    post={{\n      slug: \"tailwind-tips\",\n      date: \"2023-07-10\",\n      title: \"Tailwind CSS Pro Tips\",\n      summary: \"Improve your Tailwind workflow with these professional tips and tricks.\",\n      tags: [\"CSS\", \"Tailwind\"],\n      images: [\"/static/images/backdrop-4.webp\"],\n      readingTime: 4,\n      author: {\n        name: \"Brett Carlsen\",\n        avatar: \"/static/images/people/4.webp\"\n      }\n    }}\n  />\n</LandingBlogList>\n```\n\n\n\n### With Background and Left-Aligned Text\n\nAdd a background and left-align the title and description.\n\n\n\n```jsx\n<LandingBlogList\n  title=\"Latest Updates\"\n  description=\"Stay updated with our newest content and announcements.\"\n  textPosition=\"left\"\n  withBackground={true}\n>\n  <LandingBlogPost\n    post={{\n      slug: \"nextjs-13\",\n      date: \"2023-08-01\",\n      title: \"Next.js 13 Features You Should Know\",\n      summary: \"Explore the most important features and improvements in Next.js 13.\",\n      tags: [\"Next.js\", \"Features\"],\n      images: [\"/static/images/backdrop-5.webp\"],\n      readingTime: 7,\n      author: {\n        name: \"Michael Brown\",\n        avatar: \"/static/images/people/5.webp\"\n      }\n    }}\n  />\n</LandingBlogList>\n```\n\n\n\n### Secondary Variant with Background Glow\n\nUse the secondary color variant with a background glow effect.\n\n\n\n```jsx\n<LandingBlogList\n  title=\"Editor's Picks\"\n  description=\"Hand-picked articles from our editorial team.\"\n  variant=\"secondary\"\n  withBackgroundGlow={true}\n>\n  <LandingBlogPost\n    post={{\n      slug: \"typescript-tips\",\n      date: \"2023-07-25\",\n      title: \"TypeScript Tips for React Developers\",\n      summary: \"Improve your React applications with these TypeScript best practices.\",\n      tags: [\"TypeScript\", \"React\"],\n      images: [\"/static/images/backdrop-6.webp\"],\n      readingTime: 6,\n      author: {\n        name: \"Timothy Billow\",\n        avatar: \"/static/images/people/6.webp\"\n      }\n    }}\n  />\n</LandingBlogList>\n```\n\n\n</Examples>\n\n<PropsReference>\n| Prop Name | Prop Type | Required | Default |\n| --- | --- | ----- | ---- |\n| **children** <Tippy>The blog post components to render inside the list</Tippy> | `ReactNode` | Yes | - |\n| **title** <Tippy>The main heading for the blog list section</Tippy> | `string` | No | - |\n| **titleComponent** <Tippy>Custom component to use instead of the default title</Tippy> | `ReactNode` | No | - |\n| **description** <Tippy>The description text displayed below the title</Tippy> | `string` | No | - |\n| **descriptionComponent** <Tippy>Custom component to use instead of the default description</Tippy> | `ReactNode` | No | - |\n| **className** <Tippy>Additional CSS classes to apply to the section</Tippy> | `string` | No | - |\n| **variant** <Tippy>Color theme variant to use for styling</Tippy> | `'primary' \\| 'secondary'` | No | `'primary'` |\n| **withBackground** <Tippy>Whether to show a colored background</Tippy> | `boolean` | No | `false` |\n| **withBackgroundGlow** <Tippy>Whether to show a gradient background glow effect</Tippy> | `boolean` | No | `false` |\n| **backgroundGlowVariant** <Tippy>Color variant for the background glow</Tippy> | `'primary' \\| 'secondary'` | No | `'primary'` |\n| **textPosition** <Tippy>Alignment of the title and description</Tippy> | `'center' \\| 'left'` | No | `'center'` |\n| **display** <Tippy>How to display the blog posts - as a grid or list</Tippy> | `'grid' \\| 'list'` | No | `'list'` |\n\n```ts\nexport interface BlogPost {\n  path: string; // URL path of the blog post\n  date: string; // Publication date of the post\n  title: string; // Title of the blog post\n  summary?: string; // Brief summary of the blog post content\n  tags?: string[] | Array<{url: string; text: string}>; // Array of category tags (strings) or objects with url and text properties\n  images?: string[]; // Array of image URLs, with the first one used as the post thumbnail\n  readingTime?: number; // Estimated reading time in minutes\n  author?: {\n    name?: string; // Name of the blog post author\n    avatar?: string; // URL to the author's avatar image\n  };\n}\n```\n</PropsReference>"}, "BlogPost": {"description": "The `LandingBlogPost` component displays a single blog post card with an image, author information, date, title, summary, reading time, and tags. It's designed to be used within a blog list component but can also function as a standalone element. Can be used in a `LandingBlogList` component or as a standalone element.", "api": "<Usage>\nImport the component and provide a post object with the required properties:\n\n```jsx\nimport { LandingBlogPost } from '@/components/landing/blog/LandingBlogPost';\n```\n\n```jsx\n<LandingBlogPost\n  post={{\n    slug: 'blog-post-slug',\n    date: 'November 15, 2023',\n    title: 'Blog Post Title',\n    summary: 'This is a summary of the blog post content.',\n    tags: ['Tag 1', 'Tag 2'],\n    images: ['/static/images/backdrop-1.webp'],\n    readingTime: 5,\n    author: {\n      name: '<PERSON>',\n      avatar: '/static/images/people/1.webp'\n    }\n  }}\n/>\n```\n</Usage>\n\n<Examples>\n### Basic Blog Post\n\nA minimal blog post card with only the essential information:\n\n\n\n```jsx\nimport { LandingBlogPost } from '@/components/landing/blog/LandingBlogPost';\n\n<LandingBlogPost\n  post={{\n    slug: 'minimal-post',\n    date: 'November 10, 2023',\n    title: 'Minimal Blog Post Example',\n    summary: 'This is a minimal blog post example with only the essential information.',\n  }}\n/>\n```\n\n\n\n### Image Position Variants\n\nThe `LandingBlogPost` component supports three image position layouts:\n\n#### Left Image Layout\n\n\n\n```jsx\nimport { LandingBlogPost } from '@/components/landing/blog/LandingBlogPost';\n\n<LandingBlogPost\n  post={{\n    slug: 'left-image-post',\n    date: 'November 12, 2023',\n    title: 'Blog Post with Left Image',\n    summary: 'This example shows a blog post with the image positioned on the left side.',\n    images: ['/static/images/backdrop-5.webp'],\n    readingTime: 4\n  }}\n  imagePosition=\"left\"\n/>\n```\n\n\n\n#### Center Image Layout\n\nUsed by default in grid displays:\n\n\n\n```jsx\nimport { LandingBlogPost } from '@/components/landing/blog/LandingBlogPost';\n\n<LandingBlogPost\n  post={{\n    slug: 'center-image-post',\n    date: 'November 13, 2023',\n    title: 'Blog Post with Center Image',\n    summary: 'This example shows a blog post with the image positioned at the top (center layout).',\n    images: ['/static/images/backdrop-9.webp'],\n    readingTime: 5\n  }}\n  imagePosition=\"center\"\n/>\n```\n\n\n\n#### Right Image Layout (Default)\n\nUsed by default in list displays:\n\n\n\n```jsx\nimport { LandingBlogPost } from '@/components/landing/blog/LandingBlogPost';\n\n<LandingBlogPost\n  post={{\n    slug: 'right-image-post',\n    date: 'November 14, 2023',\n    title: 'Blog Post with Right Image',\n    summary: 'This example shows a blog post with the image positioned on the right side.',\n    images: ['/static/images/backdrop-7.webp'],\n    readingTime: 6\n  }}\n  imagePosition=\"right\" // This is the default, so it can be omitted\n/>\n```\n\n\n\n### With Image and Summary\n\nA blog post card featuring an image and summary text:\n\n\n\n```jsx\nimport { LandingBlogPost } from '@/components/landing/blog/LandingBlogPost';\n\n<LandingBlogPost\n  post={{\n    slug: 'image-post',\n    date: 'November 12, 2023',\n    title: 'Blog Post with Image and Summary',\n    summary: 'This is a brief summary of the blog post that gives readers an idea of what to expect.',\n    images: ['/static/images/backdrop-5.webp']\n  }}\n/>\n```\n\n\n\n### With Author and Reading Time\n\nA blog post with author information and reading time displayed:\n\n\n\n```jsx\nimport { LandingBlogPost } from '@/components/landing/blog/LandingBlogPost';\n\n<LandingBlogPost\n  post={{\n    slug: 'author-post',\n    date: 'November 14, 2023',\n    title: 'Blog Post with Author Information',\n    summary: 'A comprehensive guide written by an expert in the field.',\n    readingTime: 8,\n    author: {\n      name: 'Sarah Williams',\n      avatar: '/static/images/people/3.webp'\n    }\n  }}\n/>\n```\n\n\n\n### With Tags\n\nA blog post card displaying category tags:\n\n\n\n```jsx\nimport { LandingBlogPost } from '@/components/landing/blog/LandingBlogPost';\n\n<LandingBlogPost\n  post={{\n    slug: 'tagged-post',\n    date: 'November 16, 2023',\n    title: 'Blog Post with Category Tags',\n    summary: 'Learn about the latest features and updates in our newest release.',\n    tags: ['New Features', 'Updates', 'Tutorial'],\n    readingTime: 6\n  }}\n/>\n```\n\n\n\n### With Clickable Tags\n\nA blog post with tags that link to other pages:\n\n\n\n```jsx\nimport { LandingBlogPost } from '@/components/landing/blog/LandingBlogPost';\n\n<LandingBlogPost\n  post={{\n    slug: 'clickable-tags-post',\n    date: 'November 20, 2023',\n    title: 'Blog Post with Clickable Tags',\n    summary: 'This example shows a blog post with tags that link to other pages when clicked.',\n    tags: [\n      { url: '/tags/react', text: 'React' },\n      { url: '/tags/nextjs', text: 'Next.js' }\n    ],\n    readingTime: 7\n  }}\n/>\n```\n\n\n\n### Complete Example\n\nA fully-featured blog post card with all available properties:\n\n\n\n```jsx\nimport { LandingBlogPost } from '@/components/landing/blog/LandingBlogPost';\n\n<LandingBlogPost\n  post={{\n    path: '/blog/complete-example',\n    slug: 'complete-example',\n    date: 'November 18, 2023',\n    title: 'The Complete Guide to Modern Web Development',\n    summary: 'Explore the latest tools, frameworks, and best practices for building exceptional web experiences in 2023 and beyond.',\n    tags: ['Web Development', 'Tutorial', 'Best Practices'],\n    images: ['/static/images/backdrop-10.webp'],\n    readingTime: 12,\n    author: {\n      name: 'Michael Chen',\n      avatar: '/static/images/people/4.webp'\n    }\n  }}\n/>\n```\n\n\n</Examples>\n\n<PropsReference>\n| Prop Name | Prop Type | Required | Default |\n| --- | --- | ----- | ---- |\n| **post** <Tippy>Blog post object with post details</Tippy> | `BlogPost` | Yes | - |\n| **imagePosition** <Tippy>Controls the layout of the post image</Tippy> | `'left' \\| 'center' \\| 'right'` | No | `'right'` |\n\n\n```ts\nexport interface BlogPost {\n  slug: string; // Unique identifier for the blog post\n  date: string; // Publication date of the post\n  title: string; // Title of the blog post\n  summary?: string; // Brief summary of the blog post content\n  tags?: string[] | Array<{url: string; text: string}>; // Array of category tags (strings) or objects with url and text properties\n  images?: string[]; // Array of image URLs, with the first one used as the post thumbnail\n  readingTime?: number; // Estimated reading time in minutes\n  author?: {\n    name?: string; // Name of the blog post author\n    avatar?: string; // URL to the author's avatar image\n  };\n}\n```\n</PropsReference>"}, "Discount": {"description": "Use this component to show a discount or offer to encourage users to take action, typically used under call to action buttons.", "api": "<Usage>\n```jsx\nimport { LandingDiscount } from '@/components/landing/discount/LandingDiscount';\n```\n\n```jsx\n<LandingDiscount\n  discountValueText=\"$350 off\"\n  discountDescriptionText=\"for the first 10 customers (2 left)\"\n/>\n```\n</Usage>\n\n<Examples>\n### No animation\n\n\n  ```jsx\n  <LandingDiscount\n    animated={false}\n    discountValueText=\"$99 off\"\n    discountDescriptionText=\"for a limited time\"\n  />\n  ```\n\n</Examples>\n\n<PropsReference>\n| Prop Name                                                                        | Prop Type | Required | Default                                 |\n| -------------------------------------------------------------------------------- | --------- | -------- | --------------------------------------- |\n| **discountValueText** <Tippy>Text to display the value of the discount</Tippy>   | `string`  | Yes      | `'$200 off'`                            |\n| **discountDescriptionText** <Tippy>Text to describe the discount details</Tippy> | `string`  | Yes      | `'for the first 50 customers (5 left)'` |\n| **animated** <Tippy>Controls whether the icon should have an animation</Tippy>   | `boolean` | No       | `true`                                  |\n</PropsReference>"}, "FaqCollapsible": {"description": "This component displays a collapsible list of frequently asked questions and their answers.", "api": "<Usage>\n```jsx\nimport { LandingFaqCollapsibleSection } from '@/components/landing/LandingFaqCollapsible';\n```\n\n```jsx\nconst faqItems = [\n  {\n    question: 'Can I get a refund?',\n    answer:\n      'Yes, you can get a refund within 30 days of your purchase. No questions asked.',\n  },\n  {\n    question: 'What technologies are used?',\n    answer: 'We use Next.js, Tailwind CSS, and Vercel for the deployment.',\n  },\n  {\n    question: 'What do I get if I pre-order?',\n    answer:\n      'With the pre-order, you get a 50% discount on the final price and a lifetime license for the generated code.',\n  },\n];\n```\n\n```jsx\n<LandingFaqCollapsibleSection\n  title=\"FAQ\"\n  description=\"Looking to learn more about our product? Here are some of the most common\n  questions.\"\n  faqItems={faqItems}\n/>\n```\n</Usage>\n\n<Examples>\n### With Background\n\n\n\n```jsx\nimport { LandingFaqCollapsibleSection } from '@/components/landing/LandingFaqCollapsible';\n\nconst faqItems = [\n  {\n    question: 'Can I get a refund?',\n    answer:\n      'Yes, you can get a refund within 30 days of your purchase. No questions asked.',\n  },\n  {\n    question: 'What technologies are used?',\n    answer:\n      'We use Next.js, Tailwind CSS, and Vercel for the deployment.',\n  },\n  {\n    question: 'What do I get if I pre-order?',\n    answer:\n      'With the pre-order, you get a 50% discount on the final price and a lifetime license for the generated code.',\n  },\n]\n\n<LandingFaqCollapsibleSection\n  withBackground\n  variant=\"secondary\"\n  title='FAQ'\n  description=\"Looking to learn more about our product? Here are some of the most common\n  questions.\"\n  faqItems={faqItems}\n/>\n```\n\n\n\n### With Background Glow\n\n\n\n```jsx\nimport { LandingFaqCollapsibleSection } from '@/components/landing/LandingFaqCollapsible';\n\nconst faqItems = [\n  {\n    question: 'Can I get a refund?',\n    answer:\n      'Yes, you can get a refund within 30 days of your purchase. No questions asked.',\n  },\n  {\n    question: 'What technologies are used?',\n    answer:\n      'We use Next.js, Tailwind CSS, and Vercel for the deployment.',\n  },\n  {\n    question: 'What do I get if I pre-order?',\n    answer:\n      'With the pre-order, you get a 50% discount on the final price and a lifetime license for the generated code.',\n  },\n]\n\n<LandingFaqCollapsibleSection\n  withBackgroundGlow\n  backgroundGlowVariant=\"secondary\"\n  title='FAQ'\n  description=\"Looking to learn more about our product? Here are some of the most common\n  questions.\"\n  faqItems={faqItems}\n/>\n```\n\n\n</Examples>\n\n<PropsReference>\n| Prop Name                                                                                                                      | Prop Type                    | Required | Default     |\n| ------------------------------------------------------------------------------------------------------------------------------ | ---------------------------- | -------- | ----------- |\n| **title** <Tippy>A title to be displayed above the FAQ section.</Tippy>                                                        | `string` ǀ `React.ReactNode` | No       | -           |\n| **titleComponent** <Tippy>A custom component to be used as the title instead of a string.</Tippy>                              | `React.ReactNode`            | No       | -           |\n| **description** <Tippy>A description to be displayed below the title.</Tippy>                                                  | `string` ǀ `React.ReactNode` | No       | -           |\n| **descriptionComponent** <Tippy>A custom component to be used as the description instead of a string.</Tippy>                  | `React.ReactNode`            | No       | -           |\n| **faqItems** <Tippy>An array of FAQ items containing question and answer strings.</Tippy>                                      | `FaqItem[]`                  | Yes      | -           |\n| **withBackground** <Tippy>Determines whether to display a background or not.</Tippy>                                           | `boolean`                    | No       | `false`     |\n| **withBackgroundGlow** <Tippy>Determines whether to display a glowing background effect or not.</Tippy>                        | `boolean`                    | No       | `false`     |\n| **variant** <Tippy>Determines the color variant of the section (primary or secondary).</Tippy>                                 | `'primary'` ǀ `'secondary'`  | No       | `'primary'` |\n| **backgroundGlowVariant** <Tippy>Determines the color variant of the glowing background effect (primary or secondary).</Tippy> | `'primary'` ǀ `'secondary'`  | No       | `'primary'` |\n\n```ts\nexport interface FaqItem {\n  question: string;\n  answer: string;\n}\n```\n</PropsReference>"}, "Faq": {"description": "Use this component to display frequently asked questions & answers on the landing page. This can also be added on a separate page, but it's usually a good idea to have it on the landing page so people can search for information. <br/> Moreover, it can give you a bit of an SEO boost too.", "api": "<Usage>\n```jsx\nimport { LandingFaqSection } from '@/components/landing/LandingFaq';\n```\n\n```jsx\n<LandingFaqSection\n  title={'Frequently Asked Questions'}\n  description=\"Looking to learn more about Shipixen? Here are some of the most common\n  questions we get asked.\"\n  faqItems={[\n    {\n      question: 'What is Shipixen exactly?',\n      answer:\n        'Shipixen is an app that generates boilerplate code with your branding. You get the git repository & can modify the code as you want.',\n    },\n  ]}\n/>\n```\n</Usage>\n\n<Examples>\n### With Background\n\n\n\n```jsx\nimport { LandingFaqSection } from '@/components/landing/LandingFaq';\n\nconst faqItems = [\n  {\n    question: 'Can I get a refund?',\n    answer:\n      'Yes, you can get a refund within 30 days of your purchase. No questions asked.',\n  },\n  {\n    question: 'What technologies are used?',\n    answer:\n      'We use Next.js, Tailwind CSS, and Vercel for the deployment.',\n  },\n  {\n    question: 'What do I get if I pre-order?',\n    answer:\n      'With the pre-order, you get a 50% discount on the final price and a lifetime license for the generated code.',\n  },\n]\n\n<LandingFaqSection\n  withBackground\n  variant=\"secondary\"\n  title='FAQ'\n  description=\"Looking to learn more about our product? Here are some of the most common\n  questions.\"\n  faqItems={faqItems}\n/>\n```\n\n\n\n### With Background Glow\n\n\n\n```jsx\nimport { LandingFaqSection } from '@/components/landing/LandingFaq';\n\nconst faqItems = [\n  {\n    question: 'Can I get a refund?',\n    answer:\n      'Yes, you can get a refund within 30 days of your purchase. No questions asked.',\n  },\n  {\n    question: 'What technologies are used?',\n    answer:\n      'We use Next.js, Tailwind CSS, and Vercel for the deployment.',\n  },\n  {\n    question: 'What do I get if I pre-order?',\n    answer:\n      'With the pre-order, you get a 50% discount on the final price and a lifetime license for the generated code.',\n  },\n]\n\n<LandingFaqSection\n  withBackgroundGlow\n  backgroundGlowVariant=\"secondary\"\n  title='FAQ'\n  description=\"Looking to learn more about our product? Here are some of the most common\n  questions.\"\n  faqItems={faqItems}\n/>\n```\n\n\n</Examples>\n\n<PropsReference>\n| Prop Name                                                                                                                   | Prop Type                    | Required | Default     |\n| --------------------------------------------------------------------------------------------------------------------------- | ---------------------------- | -------- | ----------- |\n| **title** <Tippy>A string or React node representing the title of the FAQ section.</Tippy>                                  | `string` ǀ `React.ReactNode` | No       | -           |\n| **titleComponent** <Tippy>A React node representing the title of the FAQ section.</Tippy>                                   | `React.ReactNode`            | No       | -           |\n| **description** <Tippy>A string or React node representing the description of the FAQ section.</Tippy>                      | `string` ǀ `React.ReactNode` | No       | -           |\n| **descriptionComponent** <Tippy>A React node representing the description of the FAQ section.</Tippy>                       | `React.ReactNode`            | No       | -           |\n| **faqItems** <Tippy>An array of objects containing question and answer strings for the FAQ items.</Tippy>                   | `FaqItem[]`                  | Yes      | -           |\n| **withBackground** <Tippy>A boolean indicating whether the FAQ section should have a background.</Tippy>                    | `boolean`                    | No       | `false`     |\n| **withBackgroundGlow** <Tippy>A boolean indicating whether the FAQ section should have a glowing background effect.</Tippy> | `boolean`                    | No       | `false`     |\n| **variant** <Tippy>The variant of the background color, either `'primary'` or `'secondary'`.</Tippy>                        | `'primary'` ǀ `'secondary'`  | No       | `'primary'` |\n| **backgroundGlowVariant** <Tippy>The variant of the glowing background, either `'primary'` or `'secondary'`.</Tippy>        | `'primary'` ǀ `'secondary'`  | No       | `'primary'` |\n\n```ts\nexport interface FaqItem {\n  question: string;\n  answer: string;\n}\n```\n</PropsReference>"}, "FeatureKeyPoints": {"description": "Use this to display a bullet point in the description of a [Product Feature](/boilerplate-documentation/landing-page-components/product-feature). This component can clarify the key features of the product or service, especially when the description is long and detailed. Can also be used as a standalone component.", "api": "<Usage>\n```jsx\nimport { LandingProductFeatureKeyPoints } from '@/components/landing/LandingProductFeatureKeyPoints';\n```\n\n```jsx\n<LandingProductFeatureKeyPoints\n  keyPoints={[\n    {\n      title: 'Intelligent Assistance',\n      description:\n        'Receive personalized recommendations and insights tailored to your workflow.',\n    },\n    {\n      title: 'Seamless Collaboration',\n      description:\n        'Easily collaborate with team members and clients in real-time.',\n    },\n    {\n      title: 'Advanced Customization',\n      description:\n        'Tailor your app to fit your unique requirements with extensive customization.',\n    },\n  ]}\n/>\n```\n</Usage>\n\n<Examples>\n### With Cta Section\n\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryImageCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\nimport { LandingProductFeatureKeyPoints } from '@/components/landing/LandingProductFeatureKeyPoints';\n\n<LandingPrimaryImageCtaSection\n  title=\"Want more?\"\n  descriptionComponent={\n    <LandingProductFeatureKeyPoints\n      keyPoints={[\n        {\n          title: 'Intelligent Assistance',\n          description:\n            'Receive personalized recommendations and insights tailored to your workflow.',\n        },\n        {\n          title: 'Seamless Collaboration',\n          description:\n            'Easily collaborate with team members and clients in real-time.',\n        },\n        {\n          title: 'Advanced Customization',\n          description:\n            'Tailor your app to fit your unique requirements with extensive customization.',\n        },\n      ]}\n    />\n  }\n  imageSrc=\"/static/images/shipixen/product/1.webp\"\n  imageAlt=\"Sample image\"\n>\n  <Button size=\"xl\" asChild>\n    <a href=\"#\">Buy now</a>\n  </Button>\n</LandingPrimaryImageCtaSection>;\n```\n\n\n</Examples>\n\n<PropsReference>\n| Prop Name                                                                                                               | Prop Type                   | Required | Default     |\n| ----------------------------------------------------------------------------------------------------------------------- | --------------------------- | -------- | ----------- |\n| **keyPoints** <Tippy>Array of objects each containing a title and description to be displayed as bullet points.</Tippy> | `KeyPoint[]`                | Yes      | -           |\n| **variant** <Tippy>Styling variant for the bullet points, determining the icon and text color.</Tippy>                  | `'primary'` ǀ `'secondary'` | No       | `'primary'` |\n| **icon** <Tippy>Custom icon to be displayed instead of the default checkmark.</Tippy>                                  | `React.ReactNode` ǀ `LucideIcon`          | No       | -           |\n| **descriptionStyle** <Tippy>Determines if the description should be displayed inline or as a block.</Tippy>             | `'inline'` ǀ `'block'`                  | No       | `'block'`   |\n| **iconClassName** <Tippy>Custom class name for the icon, use to override color and size etc.</Tippy>                                                      | `string`                        | No       | -           |\n\n```ts\nexport interface KeyPoint {\n  title: string;\n  description: string;\n}\n```\n</PropsReference>"}, "FeatureList": {"description": "Use this component to showcase a list of features on the landing page. Each feature has a title, description and icon. Under the hood, this component uses the [Feature](/boilerplate-documentation/landing-page-components/feature) component.", "api": "<Usage>\n```jsx\nimport { LandingFeatureList } from '@/components/landing/feature/LandingFeatureList';\nimport { SparklesIcon } from 'lucide-react';\n```\n\n```\n<LandingFeatureList\n  title={\"Nothing quite like it.\"}\n  description={\n    '<PERSON><PERSON><PERSON> sets up everything you need to start working on your blog, website or product.'\n  }\n  featureItems={[\n    {\n      title: 'Automatic deployment to Vercel',\n      description:\n        'Deploying the generated template to Vercel is as easy as clicking a button. ',\n      icon: <SparklesIcon />,\n    },\n  ]}\n/>\n```\n</Usage>\n\n<Examples>\n### With background\n\n\n\n```jsx\nimport { SparklesIcon, LineChartIcon, LayersIcon } from 'lucide-react';\nimport { LandingFeatureList } from '@/components/landing/feature/LandingFeatureList';\n\nconst featureItems = [\n  {\n    title: 'Deploy now',\n    description: 'Deploying to Vercel with a click.',\n    icon: <SparklesIcon />,\n  },\n  {\n    title: 'SEO optimized',\n    description: 'Shipixen is optimized for search engines.',\n    icon: <LineChartIcon />,\n  },\n  {\n    title: 'MDX blog ready',\n    description: 'Shipixen comes with a fully featured MDX blog. ',\n    icon: <LayersIcon />,\n  },\n]\n\n<LandingFeatureList\n  withBackground\n  variant=\"secondary\"\n  title={\"Nothing quite like it.\"}\n  description={\n    'Shipixen sets up everything you need to start working on your blog, website or product.'\n  }\n  featureItems={featureItems}\n/>\n```\n\n\n\n### With Background Glow\n\n\n\n```jsx\nimport { SparklesIcon, LineChartIcon, LayersIcon } from 'lucide-react';\nimport { LandingFeatureList } from '@/components/landing/feature/LandingFeatureList';\n\nconst featureItems = [\n  {\n    title: 'Automatic deployment to Vercel',\n    description:\n      'Deploying the generated template to Vercel is as easy as clicking a button. ',\n    icon: <SparklesIcon />,\n  },\n  {\n    title: 'Dynamic Social Image',\n    description:\n      'We generate an open graph image that will be visible when you share your site online.',\n    icon: <LineChartIcon />,\n  },\n  {\n    title: 'MDX blog, no server required',\n    description:\n      'Shipixen comes with a fully featured MDX blog. ',\n    icon: <LayersIcon />,\n  },\n]\n\n<LandingFeatureList\n  withBackgroundGlow\n  backgroundGlowVariant=\"primary\"\n  variant=\"secondary\"\n  title={\"Nothing quite like it.\"}\n  description={\n    'Shipixen sets up everything you need to start working on your blog, website or product.'\n  }\n  featureItems={featureItems}\n/>\n```\n\n\n</Examples>\n\n<PropsReference>\n| Prop Name                                                                                                                                     | Prop Type                    | Required | Default     |\n| --------------------------------------------------------------------------------------------------------------------------------------------- | ---------------------------- | -------- | ----------- |\n| **title** <Tippy>A title for the feature list section.</Tippy>                                                                                | `string` ǀ `React.ReactNode` | No       | -           |\n| **titleComponent** <Tippy>A custom React component to be used instead of a string for the title.</Tippy>                                      | `React.ReactNode`            | No       | -           |\n| **description** <Tippy>A description for the feature list section.</Tippy>                                                                    | `string` ǀ `React.ReactNode` | No       | -           |\n| **descriptionComponent** <Tippy>A custom React component to be used instead of a string for the description.</Tippy>                          | `React.ReactNode`            | No       | -           |\n| **featureItems** <Tippy>An array of objects representing feature items. Each feature item should have a title, description, and icon.</Tippy> | `FeatureListItem[]`          | Yes      | -           |\n| **withBackground** <Tippy>Determines if the feature list section should have a background.</Tippy>                                            | `boolean`                    | No       | `false`     |\n| **withBackgroundGlow** <Tippy>Determines if the feature list section should have a glowing background effect.</Tippy>                         | `boolean`                    | No       | `false`     |\n| **variant** <Tippy>Determines the variant of the feature list section (primary or secondary).</Tippy>                                         | `'primary'` ǀ `'secondary'`  | No       | `'primary'` |\n| **backgroundGlowVariant** <Tippy>Determines the variant of the glowing background effect (primary or secondary).</Tippy>                      | `'primary'` ǀ `'secondary'`  | No       | `'primary'` |\n\n```ts\nexport interface FeatureListItem {\n  title: string;\n  description: string;\n  icon: React.ReactNode;\n}\n```\n</PropsReference>"}, "Feature": {"description": "Use this component to display a single product feature. It has a title, description and icon. It should be used with the [FeatureList](/boilerplate-documentation/landing-page-components/feature-list) component, but can also be used standalone.", "api": "<Usage>\n```jsx\nimport { LandingFeature } from '@/components/landing/feature/LandingFeature';\nimport { SparklesIcon } from 'lucide-react';\n```\n\n```\n<LandingFeature\n  title='Automatic deployment to Vercel'\n  description='Deploying the generated template to Vercel is as easy as clicking a button. There is no need to configure anything.'\n  icon={<SparklesIcon />}\n/>\n```\n</Usage>\n\n<PropsReference>\n| Prop Name                                                                               | Prop Type                 | Required | Default     |\n| --------------------------------------------------------------------------------------- | ------------------------- | -------- | ----------- |\n| **title** <Tippy>This is the title of the feature.</Tippy>                              | `string`                  | Yes      | -           |\n| **titleComponent** <Tippy>This is the title of the feature.</Tippy>                  | `React.ReactNode`                  | No      | -           |\n| **description** <Tippy>This is the description of the feature.</Tippy>                  | `string`                  | Yes      | -           |\n| **descriptionComponent** <Tippy>This is the description of the feature.</Tippy>                  | `React.ReactNode`                  | No      | -           |\n| **icon** <Tippy>The icon representing the feature.</Tippy>                              | `React.ReactNode`         | Yes      | -           |\n| **variant** <Tippy>The variant of the feature, either 'primary' or 'secondary'.</Tippy> | `'primary' ǀ 'secondary'` | No       | `'primary'` |\n</PropsReference>"}, "Footer": {"description": "This component displays a footer on the landing page. It provides additional information and links related to the website. On smaller screens, it changes to a horizontal layout to ensure usability.", "api": "<Usage>\n```jsx\nimport { LandingFooter } from '@/components/landing/footer/LandingFooter';\nimport { LandingFooterColumn } from '@/components/landing/footer/LandingFooterColumn';\nimport { LandingFooterLink } from '@/components/landing/footer/LandingFooterLink';\n```\n\n```jsx\n<LandingFooter\n  title=\"Beautiful landing pages in minutes\"\n  description=\"The easiest way to make a landing page for your startup: SaaS, iOS/Android app, directory, personal page and more.\"\n  footnote=\"© 2025 Page AI. All rights reserved.\"\n>\n  <LandingFooterColumn title=\"Product\">\n    <LandingFooterLink href=\"#\">Features</LandingFooterLink>\n    <LandingFooterLink href=\"#\">Pricing</LandingFooterLink>\n    <LandingFooterLink href=\"#\">Integrations</LandingFooterLink>\n    <LandingFooterLink href=\"#\">FAQ</LandingFooterLink>\n  </LandingFooterColumn>\n\n  <LandingFooterColumn title=\"Company\">\n    <LandingFooterLink href=\"#\">About</LandingFooterLink>\n    <LandingFooterLink href=\"#\">Careers</LandingFooterLink>\n    <LandingFooterLink href=\"#\">Press</LandingFooterLink>\n    <LandingFooterLink href=\"#\">Blog</LandingFooterLink>\n  </LandingFooterColumn>\n</LandingFooter>\n```\n</Usage>\n\n<Examples>\n### With custom logo\n\n\n```jsx\nimport { LandingFooter } from '@/components/landing/footer/LandingFooter';\nimport { LandingFooterColumn } from '@/components/landing/footer/LandingFooterColumn';\nimport { LandingFooterLink } from '@/components/landing/footer/LandingFooterLink';\n\n<LandingFooter\n  title=\"Beautiful landing pages in minutes\"\n  description=\"The easiest way to make a landing page for your startup: SaaS, iOS/Android app, directory, personal page and more.\"\n  footnote=\"© 2025 Page AI. All rights reserved.\" logoComponent={<Image width={40} height={40} className=\"rounded-full\" src=\"https://picsum.photos/id/250/200/200\" />}\n>\n  <LandingFooterColumn title=\"Product\">\n    <LandingFooterLink href=\"#\">Features</LandingFooterLink>\n    <LandingFooterLink href=\"#\">Pricing</LandingFooterLink>\n    <LandingFooterLink href=\"#\">Integrations</LandingFooterLink>\n    <LandingFooterLink href=\"#\">FAQ</LandingFooterLink>\n  </LandingFooterColumn>\n\n  <LandingFooterColumn title=\"Company\">\n    <LandingFooterLink href=\"#\">About</LandingFooterLink>\n    <LandingFooterLink href=\"#\">Careers</LandingFooterLink>\n    <LandingFooterLink href=\"#\">Press</LandingFooterLink>\n    <LandingFooterLink href=\"#\">Blog</LandingFooterLink>\n  </LandingFooterColumn>\n</LandingFooter>\n```\n\n\n\n### With more columns\n\n\n\n```jsx\nimport { LandingFooter } from '@/components/landing/footer/LandingFooter';\nimport { LandingFooterColumn } from '@/components/landing/footer/LandingFooterColumn';\nimport { LandingFooterLink } from '@/components/landing/footer/LandingFooterLink';\n\n<LandingFooter\n  title=\"Beautiful landing pages in minutes\"\n  description=\"The easiest way to make a landing page for your startup: SaaS, iOS/Android app, directory, personal page and more.\"\n  footnote=\"© 2025 Page AI. All rights reserved.\"\n>\n  <LandingFooterColumn title=\"Product\">\n    <LandingFooterLink href=\"#\">Features</LandingFooterLink>\n    <LandingFooterLink href=\"#\">Pricing</LandingFooterLink>\n    <LandingFooterLink href=\"#\">API</LandingFooterLink>\n    <LandingFooterLink href=\"#\">FAQ</LandingFooterLink>\n  </LandingFooterColumn>\n\n  <LandingFooterColumn title=\"Help\">\n    <LandingFooterLink href=\"#\">Docs</LandingFooterLink>\n    <LandingFooterLink href=\"#\">Support</LandingFooterLink>\n    <LandingFooterLink href=\"#\">Contact</LandingFooterLink>\n  </LandingFooterColumn>\n\n  <LandingFooterColumn title=\"More\">\n    <LandingFooterLink href=\"#\">About</LandingFooterLink>\n    <LandingFooterLink href=\"#\">Careers</LandingFooterLink>\n    <LandingFooterLink href=\"#\">Press</LandingFooterLink>\n    <LandingFooterLink href=\"#\">Blog</LandingFooterLink>\n  </LandingFooterColumn>\n</LandingFooter>\n```\n\n\n\n### With background\n\n\n```jsx\nimport { LandingFooter } from '@/components/landing/footer/LandingFooter';\nimport { LandingFooterColumn } from '@/components/landing/footer/LandingFooterColumn';\nimport { LandingFooterLink } from '@/components/landing/footer/LandingFooterLink';\n\n<LandingFooter\n  title=\"Beautiful landing pages in minutes\"\n  description=\"The easiest way to make a landing page for your startup: SaaS, iOS/Android app, directory, personal page and more.\"\n  footnote=\"© 2025 Page AI. All rights reserved.\"\n  withBackground\n>\n  <LandingFooterColumn title=\"Product\">\n    <LandingFooterLink href=\"#\">Features</LandingFooterLink>\n    <LandingFooterLink href=\"#\">Pricing</LandingFooterLink>\n    <LandingFooterLink href=\"#\">Integrations</LandingFooterLink>\n    <LandingFooterLink href=\"#\">FAQ</LandingFooterLink>\n  </LandingFooterColumn>\n\n  <LandingFooterColumn title=\"Company\">\n    <LandingFooterLink href=\"#\">About</LandingFooterLink>\n    <LandingFooterLink href=\"#\">Careers</LandingFooterLink>\n    <LandingFooterLink href=\"#\">Press</LandingFooterLink>\n    <LandingFooterLink href=\"#\">Blog</LandingFooterLink>\n  </LandingFooterColumn>\n</LandingFooter>\n```\n\n\n\n### With background glow\n\n\n```jsx\nimport { LandingFooter } from '@/components/landing/footer/LandingFooter';\nimport { LandingFooterColumn } from '@/components/landing/footer/LandingFooterColumn';\nimport { LandingFooterLink } from '@/components/landing/footer/LandingFooterLink';\n\n<LandingFooter\n  title=\"Beautiful landing pages in minutes\"\n  description=\"The easiest way to make a landing page for your startup: SaaS, iOS/Android app, directory, personal page and more.\"\n  footnote=\"© 2025 Page AI. All rights reserved.\"\n  withBackgroundGlow\n>\n  <LandingFooterColumn title=\"Product\">\n    <LandingFooterLink href=\"#\">Features</LandingFooterLink>\n    <LandingFooterLink href=\"#\">Pricing</LandingFooterLink>\n    <LandingFooterLink href=\"#\">Integrations</LandingFooterLink>\n    <LandingFooterLink href=\"#\">FAQ</LandingFooterLink>\n  </LandingFooterColumn>\n\n  <LandingFooterColumn title=\"Company\">\n    <LandingFooterLink href=\"#\">About</LandingFooterLink>\n    <LandingFooterLink href=\"#\">Careers</LandingFooterLink>\n    <LandingFooterLink href=\"#\">Press</LandingFooterLink>\n    <LandingFooterLink href=\"#\">Blog</LandingFooterLink>\n  </LandingFooterColumn>\n</LandingFooter>\n```\n\n\n\n### With background gradient\n\n\n```jsx\nimport { LandingFooter } from '@/components/landing/footer/LandingFooter';\nimport { LandingFooterColumn } from '@/components/landing/footer/LandingFooterColumn';\nimport { LandingFooterLink } from '@/components/landing/footer/LandingFooterLink';\n\n<LandingFooter\n  title=\"Beautiful landing pages in minutes\"\n  description=\"The easiest way to make a landing page for your startup: SaaS, iOS/Android app, directory, personal page and more.\"\n  footnote=\"© 2025 Page AI. All rights reserved.\"\n  withBackgroundGradient\n>\n  <LandingFooterColumn title=\"Product\">\n    <LandingFooterLink href=\"#\">Features</LandingFooterLink>\n    <LandingFooterLink href=\"#\">Pricing</LandingFooterLink>\n    <LandingFooterLink href=\"#\">Integrations</LandingFooterLink>\n    <LandingFooterLink href=\"#\">FAQ</LandingFooterLink>\n  </LandingFooterColumn>\n\n  <LandingFooterColumn title=\"Company\">\n    <LandingFooterLink href=\"#\">About</LandingFooterLink>\n    <LandingFooterLink href=\"#\">Careers</LandingFooterLink>\n    <LandingFooterLink href=\"#\">Press</LandingFooterLink>\n    <LandingFooterLink href=\"#\">Blog</LandingFooterLink>\n  </LandingFooterColumn>\n</LandingFooter>\n```\n\n\n</Examples>\n\n<PropsReference>\n| Prop Name | Prop Type | Required | Default |\n| --------- | --------- | -------- | ------- |\n| **children** <Tippy>React nodes to be rendered within the footer.</Tippy> | `React.ReactNode` | No | - |\n| **title** <Tippy>The title text displayed in the footer.</Tippy> | `string` | No | - |\n| **description** <Tippy>A brief description displayed below the title.</Tippy> | `string` | No | - |\n| **footnote** <Tippy>Text or React nodes to be displayed as a footnote.</Tippy> | `string` ǀ `React.ReactNode` | No | - |\n| **logoComponent** <Tippy>A custom React node to replace the default logo.</Tippy> | `React.ReactNode` | No | - |\n| **withBackground** <Tippy>Determines if the footer should have a background color.</Tippy> | `boolean` | No | `false` |\n| **withBackgroundGlow** <Tippy>Adds a glowing background effect to the footer.</Tippy> | `boolean` | No | `false` |\n| **variant** <Tippy>The color variant of the footer.</Tippy> | `'primary'` ǀ `'secondary'` | No | `'primary'` |\n| **backgroundGlowVariant** <Tippy>The color variant of the glowing background.</Tippy> | `'primary'` ǀ `'secondary'` | No | `'primary'` |\n| **withBackgroundGradient** <Tippy>Determines if the footer should have a gradient background.</Tippy> | `boolean` | No | `false` |\n</PropsReference>"}, "LeadingPill": {"description": "The `LandingLeadingPill` is a versatile inline component perfect for announcements, badges, feature highlights, and call-to-action elements. It features **SVG-based borders** for all variants ensuring perfect rounded corners, supports multiple color variants including rainbow gradients, glass backgrounds, and can be made clickable as either a link or button. It also supports left and right components, custom text components, children, and custom opacity. **Key Features:** - **Rainbow gradient borders** with smooth color transitions - **Glass background effects** with backdrop blur - **Flexible content** support - **Clickable variants** (link or button) - **Responsive design** with proper dimension handling These work well together with `LandingPrimaryImageCtaSection`, `LandingPrimaryTextCtaSection`, `LandingPrimaryVideoCtaSection`, `LandingProductFeature`, `LandingProductVideoFeature` as the `leadingComponent` prop.", "api": "<Usage>\n```js\nimport { LandingLeadingPill } from '@/components/landing/leading/LandingLeadingPill';\n```\n\n```jsx\n<LandingLeadingPill\n  text=\"New Feature\"\n  borderVariant=\"primary\"\n  textVariant=\"primary\"\n/>\n```\n</Usage>\n\n<PropsReference>\n| Prop Name | Prop Type | Required | Default |\n| --- | --- | ----- | ---- |\n| **className** <Tippy>Additional CSS classes to apply to the pill</Tippy> | `string` | No | `-` |\n| **textVariant** <Tippy>Text color variant of the pill</Tippy> | `'default' \\| 'primary' \\| 'secondary' \\| 'lightGray' \\| 'darkGray' \\| 'white' \\| 'black'` | No | `'default'` |\n| **borderVariant** <Tippy>Border style variant of the pill (all rendered with SVG)</Tippy> | `'default' \\| 'primary' \\| 'secondary' \\| 'lightGray' \\| 'darkGray' \\| 'pinkRainbow' \\| 'purpleRainbow' \\| 'yellowRainbow' \\| 'greenRainbow'` | No | `'default'` |\n| **backgroundVariant** <Tippy>Background style of the pill (only applied when withBackground is true)</Tippy> | `'default' \\| 'primary' \\| 'secondary' \\| 'glass' \\| 'primaryGlass' \\| 'secondaryGlass'` | No | `'default'` |\n| **withBackground** <Tippy>Whether to apply background styling</Tippy> | `boolean` | No | `false` |\n| **withBorder** <Tippy>Whether to apply border styling</Tippy> | `boolean` | No | `true` |\n| **borderWidth** <Tippy>Width of the border in pixels</Tippy> | `number` | No | `1` |\n| **text** <Tippy>Text content to display in the pill</Tippy> | `string` | No | `-` |\n| **textComponent** <Tippy>Custom React component to display instead of text</Tippy> | `React.ReactNode` | No | `-` |\n| **children** <Tippy>Children components to display (takes precedence over text and textComponent)</Tippy> | `React.ReactNode` | No | `-` |\n| **textStyle** <Tippy>Text styling variant</Tippy> | `'default' \\| 'capitalize' \\| 'uppercase'` | No | `'default'` |\n| **leftComponent** <Tippy>Component to display on the left side of the text</Tippy> | `React.ReactNode` | No | `-` |\n| **rightComponent** <Tippy>Component to display on the right side of the text</Tippy> | `React.ReactNode` | No | `-` |\n| **href** <Tippy>URL to navigate to when clicked (makes the pill a link)</Tippy> | `string` | No | `-` |\n| **onClick** <Tippy>Function to call when clicked (makes the pill a button)</Tippy> | `() => void` | No | `-` |\n</PropsReference>"}, "Marquee": {"description": "This component displays an animated marquee that can loop through images, icons, or text.", "api": "<Usage>\n```jsx\nimport { LandingMarquee } from '@/components/landing/LandingMarquee';\n```\n\n```jsx\n<LandingMarquee>\n  <ChromeIcon className=\"w-12 h-12 mx-8\" />\n  <FigmaIcon className=\"w-12 h-12 mx-8\" />\n  <GithubIcon className=\"w-12 h-12 mx-8\" />\n  <FramerIcon className=\"w-12 h-12 mx-8\" />\n  <TwitchIcon className=\"w-12 h-12 mx-8\" />\n  <TwitterIcon className=\"w-12 h-12 mx-8\" />\n  <GitlabIcon className=\"w-12 h-12 mx-8\" />\n  <InstagramIcon className=\"w-12 h-12 mx-8\" />\n  <SlackIcon className=\"w-12 h-12 mx-8\" />\n</LandingMarquee>\n```\n</Usage>\n\n<Examples>\n### Animation Direction\n\n\n\n```jsx\nimport {\n  ChromeIcon,\n  FigmaIcon,\n  GithubIcon,\n  FramerIcon,\n  TwitchIcon,\n  TwitterIcon,\n  GitlabIcon,\n  InstagramIcon,\n  SlackIcon,\n} from 'lucide-react';\nimport { LandingMarquee } from '@/components/landing/LandingMarquee';\n\n<LandingMarquee animationDirection=\"left\">\n  <ChromeIcon className=\"w-12 h-12 mx-8\" />\n  <FigmaIcon className=\"w-12 h-12 mx-8\" />\n  <GithubIcon className=\"w-12 h-12 mx-8\" />\n  <FramerIcon className=\"w-12 h-12 mx-8\" />\n  <TwitchIcon className=\"w-12 h-12 mx-8\" />\n  <TwitterIcon className=\"w-12 h-12 mx-8\" />\n  <GitlabIcon className=\"w-12 h-12 mx-8\" />\n  <InstagramIcon className=\"w-12 h-12 mx-8\" />\n  <SlackIcon className=\"w-12 h-12 mx-8\" />\n</LandingMarquee>;\n```\n\n\n\n### With images\n\n\n\n```jsx\n<LandingMarquee>\n  <div className=\"flex gap-8 px-4\">\n    {[\n      {\n        imageSrc: '/static/images/people/1.webp',\n        name: 'John Doe',\n      },\n      {\n        imageSrc: '/static/images/people/2.webp',\n        name: 'Jane Doe',\n      },\n      {\n        imageSrc: '/static/images/people/3.webp',\n        name: 'Alice Doe',\n      },\n      {\n        imageSrc: '/static/images/people/4.webp',\n        name: 'Bob Doe',\n      },\n      {\n        imageSrc: '/static/images/people/5.webp',\n        name: 'Eve Doe',\n      },\n    ].map((person, index) => (\n      <LandingAvatar\n        size=\"large\"\n        key={index}\n        imageSrc={person.imageSrc}\n        name={person.name}\n      />\n    ))}\n  </div>\n</LandingMarquee>\n```\n\n\n\n### With background\n\n\n\n    ```jsx\n    import { ChromeIcon, FigmaIcon, GithubIcon, FramerIcon, TwitchIcon, TwitterIcon, GitlabIcon, InstagramIcon, SlackIcon } from 'lucide-react';\n    import { LandingMarquee } from '@/components/landing/LandingMarquee';\n\n    <LandingMarquee withBackground variant=\"secondary\" animationDurationInSeconds=\"10s\">\n      <ChromeIcon className=\"w-12 h-12 mx-8\" />\n      <FigmaIcon className=\"w-12 h-12 mx-8\" />\n      <GithubIcon className=\"w-12 h-12 mx-8\" />\n      <FramerIcon className=\"w-12 h-12 mx-8\" />\n      <TwitchIcon className=\"w-12 h-12 mx-8\" />\n      <TwitterIcon className=\"w-12 h-12 mx-8\" />\n      <GitlabIcon className=\"w-12 h-12 mx-8\" />\n      <InstagramIcon className=\"w-12 h-12 mx-8\" />\n      <SlackIcon className=\"w-12 h-12 mx-8\" />\n    </LandingMarquee>;\n    ```\n\n\n</Examples>\n\n<PropsReference>\n| Prop Name <Tippy>Prop Description</Tippy>                                                                                             | Prop Type                    | Required | Default     |\n| ------------------------------------------------------------------------------------------------------------------------------------- | ---------------------------- | -------- | ----------- |\n| **children** <Tippy>React nodes to be rendered within the component (below the text on smaller screen, right side on larger).</Tippy> | `React.ReactNode` ǀ `string` | No       | -           |\n| **innerClassName** <Tippy>Additional class names to apply to the inner marquee container.</Tippy>                                     | `string`                     | No       | -           |\n| **withBackground** <Tippy>Flag to determine if a background should be applied.</Tippy>                                                | `boolean`                    | No       | `false`     |\n| **animationDurationInSeconds** <Tippy>Duration of the marquee animation in seconds e.g. \"10s\".</Tippy>                                | `string`                     | No       | -           |\n| **animationDirection** <Tippy>Direction of the marquee animation ('left' or 'right').</Tippy>                                         | `'left'` ǀ `'right'`         | No       | -           |\n| **variant** <Tippy>Variant of the marquee background ('primary' or 'secondary').</Tippy>                                              | `'primary'` ǀ `'secondary'`  | No       | `'primary'` |\n</PropsReference>"}, "Navigation": {"description": "This component displays a top navigation / header bar with a main menu and a logo. On smaller screens, the main menu is hidden and a menu is displayed instead. The menu will open a side sheet with the main menu items.", "api": "<Usage>\n```jsx\nimport { LandingHeader } from '@/components/landing/navigation/LandingHeader';\nimport { LandingHeaderMenuItem } from '@/components/landing/navigation/LandingHeaderMenuItem';\n```\n\n```jsx\n<LandingHeader>\n  <LandingHeaderMenuItem href=\"#\" label=\"Home\" />\n  <LandingHeaderMenuItem href=\"#\" label=\"Pricing\" />\n  <LandingHeaderMenuItem href=\"#\" label=\"Articles\" />\n</LandingHeader>\n```\n</Usage>\n\n<Examples>\n### With custom logo\n\n\n```jsx\nimport Image from '@/components/shared/Image';\nimport { LandingHeader } from '@/components/landing/navigation/LandingHeader';\nimport { LandingHeaderMenuItem } from '@/components/landing/navigation/LandingHeaderMenuItem';\n\n<LandingHeader\n  logoComponent={<Image width={40} height={40} className=\"rounded-full\" src=\"https://picsum.photos/id/250/200/200\" />}\n>\n  <LandingHeaderMenuItem\n    href=\"#\"\n    label=\"Home\"\n  />\n\n  <LandingHeaderMenuItem\n    href=\"#\"\n    label=\"Pricing\"\n  />\n\n  <LandingHeaderMenuItem\n    href=\"#\"\n    label=\"Articles\"\n  />\n</LandingHeader>\n```\n\n\n\n### With background\n\nThis adds a background to the navigation bar (centered on larger screens).\n\n\n\n```jsx\nimport { LandingHeader } from '@/components/landing/navigation/LandingHeader';\nimport { LandingHeaderMenuItem } from '@/components/landing/navigation/LandingHeaderMenuItem';\n\n<LandingHeader withBackground>\n  <LandingHeaderMenuItem\n    href=\"#\"\n    label=\"Home\"\n  />\n\n  <LandingHeaderMenuItem\n    href=\"#\"\n    label=\"Pricing\"\n  />\n\n  <LandingHeaderMenuItem\n    href=\"#\"\n    label=\"Articles\"\n  />\n</LandingHeader>\n```\n\n\n\n### With button\n\n\n```jsx\nimport { LandingHeader } from '@/components/landing/navigation/LandingHeader';\nimport { LandingHeaderMenuItem } from '@/components/landing/navigation/LandingHeaderMenuItem';\n\n<LandingHeader>\n  <LandingHeaderMenuItem\n    href=\"#\"\n    label=\"Home\"\n  />\n\n  <LandingHeaderMenuItem\n    href=\"#\"\n    label=\"Pricing\"\n  />\n\n  <LandingHeaderMenuItem\n    href=\"#\"\n    label=\"Articles\"\n  />\n\n  <LandingHeaderMenuItem\n    href=\"#\"\n    label=\"Sign In\"\n    type=\"button\"\n  />\n</LandingHeader>\n```\n\n\n\n### With icon button (or other elements)\n\n\n```jsx\nimport { SearchIcon } from 'lucide-react';\nimport { LandingHeader } from '@/components/landing/navigation/LandingHeader';\nimport { LandingHeaderMenuItem } from '@/components/landing/navigation/LandingHeaderMenuItem';\n\n<LandingHeader>\n  <LandingHeaderMenuItem\n    href=\"#\"\n    label=\"Home\"\n  />\n\n  <LandingHeaderMenuItem\n    href=\"#\"\n    label=\"Pricing\"\n  />\n\n  <LandingHeaderMenuItem\n    href=\"#\"\n    label=\"Articles\"\n  />\n\n  <LandingHeaderMenuItem\n    href=\"#\"\n    label=\"Sign In\"\n    type=\"button\"\n  />\n\n  <LandingHeaderMenuItem\n    href=\"#\"\n    label={<SearchIcon />}\n    type=\"icon-button\"\n    variant=\"ghost\"\n  />\n</LandingHeader>\n```\n\n\n\n### Variant\n\n\n```jsx\nimport ThemeSwitch from '@/components/shared/ThemeSwitch';\nimport { SearchIcon } from 'lucide-react';\nimport { LandingHeader } from '@/components/landing/navigation/LandingHeader';\nimport { LandingHeaderMenuItem } from '@/components/landing/navigation/LandingHeaderMenuItem';\n\n<LandingHeader withBackground variant=\"secondary\">\n  <LandingHeaderMenuItem\n    href=\"#\"\n    label=\"Home\"\n  />\n\n  <LandingHeaderMenuItem\n    href=\"#\"\n    label=\"Pricing\"\n  />\n\n  <LandingHeaderMenuItem\n    href=\"#\"\n    label=\"Articles\"\n  />\n\n  <LandingHeaderMenuItem\n    href=\"#\"\n    label=\"Sign In\"\n    type=\"button\"\n  />\n\n  <LandingHeaderMenuItem\n    href=\"#\"\n    label={<SearchIcon />}\n    type=\"icon-button\"\n    variant=\"ghost\"\n  />\n\n  <ThemeSwitch />\n</LandingHeader>\n```\n\n\n</Examples>\n\n<PropsReference>\n| Prop Name | Prop Type | Required | Default |\n| --------- | --------- | -------- | ------- |\n| **logoComponent** <Tippy>An optional React element to be used as the logo.</Tippy> | `React.ReactElement` | No | - |\n| **children** <Tippy>Navigation items to be rendered within the navigation bar, usually `LandingHeaderMenuItem`.</Tippy> | `React.ReactElement` | Yes | - |\n| **withBackground** <Tippy>Determines if the navigation bar should have a background.</Tippy> | `boolean` | No | `false` |\n| **variant** <Tippy>Defines the color variant of the background when `withBackground` is `true`.</Tippy> | `'primary' ǀ 'secondary'` | No | `'primary'` |\n| **fixed** <Tippy>Flag to determine if the navigation bar should be fixed at the top of the screen.</Tippy> | `boolean` | No | `false` |\n</PropsReference>"}, "Newsletter": {"description": "This component displays a newsletter subscription form. On smaller screens, the input field may adjust accordingly to ensure usability.", "api": "<Usage>\n```jsx\nimport { LandingNewsletterSection } from '@/components/landing/newsletter/LandingNewsletterSection';\n```\n\n```jsx\n<LandingNewsletterSection\n  title=\"Never miss an update!\"\n  description=\"Subscribe to our newsletter to get the latest announcements, news and exclusive offers.\"\n>\n</LandingNewsletterSection>\n```\n</Usage>\n\n<Examples>\n### Text position\n\n\n```jsx\n<LandingNewsletterSection\n  title=\"Never miss an update!\"\n  description=\"Subscribe to our newsletter to get the latest announcements, news and exclusive offers.\"\n  textPosition=\"left\"\n>\n</LandingNewsletterSection>\n```\n\n\n\n### With avatar images\nShowing user images is a great way to add social proof to your newsletter section and increase conversions.\n\n\n\n```jsx\n<LandingNewsletterSection\n  title=\"Never miss an update!\"\n  description=\"Subscribe to our newsletter to get the latest announcements, news and exclusive offers.\"\n  withAvatars\n>\n</LandingNewsletterSection>\n```\n\n\n\n### With background\n\n\n```jsx\n<LandingNewsletterSection\n  title=\"Never miss an update!\"\n  description=\"Subscribe to our newsletter to get the latest announcements, news and exclusive offers.\"\n  withBackground\n>\n</LandingNewsletterSection>\n```\n\n\n\n### With background glow\n\n\n```jsx\n<LandingNewsletterSection\n  title=\"Never miss an update!\"\n  description=\"Subscribe to our newsletter to get the latest announcements, news and exclusive offers.\"\n  withBackgroundGlow\n>\n</LandingNewsletterSection>\n```\n\n\n</Examples>\n\n<PropsReference>\n| Prop Name | Prop Type | Required | Default |\n| --------- | --------- | -------- | ------- |\n| **children** <Tippy>React nodes to be rendered within the component.</Tippy> | `React.ReactNode` ǀ `string` | No | - |\n| **innerClassName** <Tippy>Additional class names for the inner container.</Tippy> | `string` | No | - |\n| **title** <Tippy>The title text or React node.</Tippy> | `string` ǀ `React.ReactNode` | No | - |\n| **titleComponent** <Tippy>Optional React node to replace the title.</Tippy> | `React.ReactNode` | No | - |\n| **description** <Tippy>The description text or React node.</Tippy> | `string` ǀ `React.ReactNode` | No | - |\n| **descriptionComponent** <Tippy>Optional React node to replace the description.</Tippy> | `React.ReactNode` | No | - |\n| **buttonLabel** <Tippy>The label for the submit button.</Tippy> | `string` | No | `'Subscribe'` |\n| **placeholderLabel** <Tippy>The placeholder text for the email input.</Tippy> | `string` | No | `'Enter your email'` |\n| **inputLabel** <Tippy>The label for the email input.</Tippy> | `string` | No | `'Email address'` |\n| **textPosition** <Tippy>Position of the text inside the section.</Tippy> | `'center'` ǀ `'left'` | No | `'left'` |\n| **minHeight** <Tippy>Minimum height of the section.</Tippy> | `number` | No | `350` |\n| **withBackground** <Tippy>Whether to display a background.</Tippy> | `boolean` | No | `false` |\n| **withBackgroundGlow** <Tippy>Whether to display a glowing background.</Tippy> | `boolean` | No | `false` |\n| **withAvatars** <Tippy>Whether to display user images.</Tippy> | `boolean` | No | `true` |\n| **variant** <Tippy>Determines the section's visual style.</Tippy> | `'primary'` ǀ `'secondary'` | No | `'primary'` |\n| **backgroundGlowVariant** <Tippy>Determines the glow background style.</Tippy> | `'primary'` ǀ `'secondary'` | No | `'primary'` |\n| **disabled** <Tippy>Whether to disable the component.</Tippy> | `boolean` | No | `false` |\n| **onSubmit** <Tippy>Callback function triggered on form submission.</Tippy> | `(e: React.FormEvent<HTMLFormElement>) => void` | No | `() => {}` |\n</PropsReference>"}, "PricingComparison": {"description": "A versatile price comparison section that enables you to showcase feature differences across multiple products or services. Perfect for SaaS feature comparison pages, product comparisons, and feature matrices. The component supports 2-5 comparison columns, each with customizable headers, feature lists, and optional footers. Featured columns can be highlighted to draw attention to preferred options.", "api": "<Usage>\n```js\nimport {\n  LandingPriceComparisonSection,\n  LandingPriceComparisonColumn,\n  LandingPriceComparisonItem,\n} from '@/components/landing';\n```\n\n```jsx\n<LandingPriceComparisonSection\n  title=\"Product Comparison\"\n  description=\"Compare features across different plans\"\n>\n  <LandingPriceComparisonColumn\n    featured\n    header=\"Premium Plan\"\n    footer=\"$99/month\"\n  >\n    <LandingPriceComparisonItem\n      state=\"check\"\n      text=\"All features included\"\n    />\n  </LandingPriceComparisonColumn>\n</LandingPriceComparisonSection>\n```\n</Usage>\n\n<Examples>\n### Featured Column Highlighting & CTA\n\nAny column can be featured, but only one column can be featured at a time.\n\n\n\n```jsx\n<LandingPriceComparisonSection\n  title=\"Why choose us?\"\n  textPosition=\"center\"\n>\n  <LandingPriceComparisonColumn\n    header=\"Competitor A\"\n    footer=\"$129 /mo\"\n  >\n    <LandingPriceComparisonItem state=\"check\" text=\"All features\" />\n    <LandingPriceComparisonItem state=\"cross\" text=\"Premium support\" />\n  </LandingPriceComparisonColumn>\n\n  <LandingPriceComparisonColumn\n    featured\n    header=\"Our Product\"\n    footer=\"$19 /mo\"\n    ctaText=\"Get Started\"\n    href=\"#\"\n  >\n    <LandingPriceComparisonItem state=\"check\" text=\"All features\" />\n    <LandingPriceComparisonItem state=\"check\" text=\"Premium support\" />\n  </LandingPriceComparisonColumn>\n\n  <LandingPriceComparisonColumn\n    header=\"Competitor B\"\n    footer=\"$299 /mo\"\n  >\n    <LandingPriceComparisonItem state=\"check\" text=\"All features\" />\n    <LandingPriceComparisonItem state=\"check\" text=\"Premium support\" />\n  </LandingPriceComparisonColumn>\n</LandingPriceComparisonSection>\n```\n\n\n\n### With Tooltips and Descriptions\n\n\n\n```jsx\n<LandingPriceComparisonSection\n  title=\"Feature Comparison\"\n  description=\"Detailed comparison with helpful tooltips\"\n>\n  <LandingPriceComparisonColumn\n    featured\n    header=\"Professional\"\n    footer=\"$79 /mo\"\n    ctaText=\"Start Free Trial\"\n    href=\"#\"\n  >\n    <LandingPriceComparisonItem\n      state=\"check\"\n      text=\"Advanced Security\"\n      description=\"Enterprise-grade security with SOC 2 compliance\"\n    />\n    <LandingPriceComparisonItem\n      state=\"check\"\n      text=\"Priority Support\"\n      description=\"24/7 dedicated support with guaranteed response times\"\n    />\n  </LandingPriceComparisonColumn>\n\n  <LandingPriceComparisonColumn\n    header=\"Standard\"\n    footer=\"$39 /mo\"\n  >\n    <LandingPriceComparisonItem state=\"cross\" text=\"Advanced Security\" />\n    <LandingPriceComparisonItem state=\"check\" text=\"Priority Support\" />\n  </LandingPriceComparisonColumn>\n</LandingPriceComparisonSection>\n```\n\n\n\n### Secondary Variant\n\nThe secondary variant provides an alternative color scheme for the comparison section.\n\n\n\n```jsx\n<LandingPriceComparisonSection\n  title=\"Compare Plans\"\n  description=\"Choose the plan that's right for you\"\n  variant=\"secondary\"\n>\n  <LandingPriceComparisonColumn\n    featured\n    header=\"Pro\"\n    footer=\"$49 /mo\"\n    ctaText=\"Get Started\"\n    href=\"#\"\n    variant=\"secondary\"\n  >\n    <LandingPriceComparisonItem state=\"check\" text=\"Unlimited Projects\" />\n    <LandingPriceComparisonItem state=\"check\" text=\"Priority Support\" />\n    <LandingPriceComparisonItem state=\"check\" text=\"Advanced Analytics\" />\n  </LandingPriceComparisonColumn>\n\n\n  <LandingPriceComparisonColumn\n    header=\"Basic\"\n    footer=\"$19 /mo\"\n    variant=\"secondary\"\n  >\n    <LandingPriceComparisonItem state=\"check\" text=\"5 Projects\" />\n    <LandingPriceComparisonItem state=\"cross\" text=\"Priority Support\" />\n    <LandingPriceComparisonItem state=\"cross\" text=\"Advanced Analytics\" />\n  </LandingPriceComparisonColumn>\n\n  <LandingPriceComparisonColumn\n    header=\"Enterprise\"\n    footer=\"$99 /mo\"\n    variant=\"secondary\"\n  >\n    <LandingPriceComparisonItem state=\"check\" text=\"Unlimited Projects\" />\n    <LandingPriceComparisonItem state=\"cross\" text=\"Priority Support\" />\n    <LandingPriceComparisonItem state=\"check\" text=\"Advanced Analytics\" />\n  </LandingPriceComparisonColumn>\n</LandingPriceComparisonSection>\n```\n\n\n\n### Without Price\n\nPerfect for feature comparisons where pricing is not the focus.\n\n\n\n```jsx\n<LandingPriceComparisonSection\n  title=\"Feature Comparison\"\n  description=\"See how different solutions stack up\"\n>\n  <LandingPriceComparisonColumn\n    header=\"Solution A\"\n  >\n    <LandingPriceComparisonItem state=\"check\" text=\"API Access\" />\n    <LandingPriceComparisonItem state=\"cross\" text=\"Real-time Sync\" />\n    <LandingPriceComparisonItem state=\"check\" text=\"Basic Support\" />\n    <LandingPriceComparisonItem state=\"cross\" text=\"Custom Integrations\" />\n  </LandingPriceComparisonColumn>\n\n  <LandingPriceComparisonColumn\n    featured\n    header=\"Our Solution\"\n    ctaText=\"Learn More\"\n    href=\"#\"\n  >\n    <LandingPriceComparisonItem state=\"check\" text=\"API Access\" />\n    <LandingPriceComparisonItem state=\"check\" text=\"Real-time Sync\" />\n    <LandingPriceComparisonItem state=\"check\" text=\"Premium Support\" />\n    <LandingPriceComparisonItem state=\"check\" text=\"Custom Integrations\" />\n  </LandingPriceComparisonColumn>\n\n  <LandingPriceComparisonColumn\n    header=\"Solution B\"\n  >\n    <LandingPriceComparisonItem state=\"cross\" text=\"API Access\" />\n    <LandingPriceComparisonItem state=\"check\" text=\"Real-time Sync\" />\n    <LandingPriceComparisonItem state=\"check\" text=\"Basic Support\" />\n    <LandingPriceComparisonItem state=\"cross\" text=\"Custom Integrations\" />\n  </LandingPriceComparisonColumn>\n</LandingPriceComparisonSection>\n```\n\n\n\n### Without Button or footer\n\nClean comparison tables without call-to-action buttons for informational purposes.\n\n\n\n```jsx\n<LandingPriceComparisonSection\n  title=\"Product Specifications\"\n  description=\"Technical comparison of our products\"\n>\n  <LandingPriceComparisonColumn\n    header=\"Starter Edition\"\n  >\n    <LandingPriceComparisonItem state=\"check\" text=\"2 CPU Cores\" />\n    <LandingPriceComparisonItem state=\"check\" text=\"4GB RAM\" />\n    <LandingPriceComparisonItem state=\"cross\" text=\"SSD Storage\" />\n    <LandingPriceComparisonItem state=\"cross\" text=\"24/7 Monitoring\" />\n  </LandingPriceComparisonColumn>\n\n  <LandingPriceComparisonColumn\n    header=\"Professional Edition\"\n  >\n    <LandingPriceComparisonItem state=\"check\" text=\"8 CPU Cores\" />\n    <LandingPriceComparisonItem state=\"check\" text=\"16GB RAM\" />\n    <LandingPriceComparisonItem state=\"cross\" text=\"NVMe SSD Storage\" />\n    <LandingPriceComparisonItem state=\"check\" text=\"24/7 Monitoring\" />\n  </LandingPriceComparisonColumn>\n\n  <LandingPriceComparisonColumn\n    featured\n    header=\"Enterprise Edition\"\n  >\n    <LandingPriceComparisonItem state=\"check\" text=\"4 CPU Cores\" />\n    <LandingPriceComparisonItem state=\"check\" text=\"8GB RAM\" />\n    <LandingPriceComparisonItem state=\"check\" text=\"SSD Storage\" />\n    <LandingPriceComparisonItem state=\"check\" text=\"24/7 Monitoring\" />\n  </LandingPriceComparisonColumn>\n</LandingPriceComparisonSection>\n```\n\n\n\n### With Background\n\nAdds a subtle background to make the comparison section stand out from the rest of the page.\n\n\n\n```jsx\n<LandingPriceComparisonSection\n  title=\"Service Tiers\"\n  description=\"Choose the right level of service for your needs\"\n  withBackground\n>\n  <LandingPriceComparisonColumn\n    featured\n    header=\"Premium\"\n    footer=\"$79 /mo\"\n    ctaText=\"Upgrade Now\"\n    href=\"#\"\n  >\n    <LandingPriceComparisonItem state=\"check\" text=\"Priority Support\" />\n    <LandingPriceComparisonItem state=\"check\" text=\"All Features\" />\n    <LandingPriceComparisonItem state=\"check\" text=\"Priority Queue\" />\n  </LandingPriceComparisonColumn>\n\n  <LandingPriceComparisonColumn\n    header=\"Enterprise\"\n    footer=\"Custom\"\n  >\n    <LandingPriceComparisonItem state=\"check\" text=\"Dedicated Support\" />\n    <LandingPriceComparisonItem state=\"check\" text=\"Custom Features\" />\n    <LandingPriceComparisonItem state=\"check\" text=\"Dedicated Infrastructure\" />\n  </LandingPriceComparisonColumn>\n</LandingPriceComparisonSection>\n```\n\n\n\n### With Background Glow\n\nCreates an eye-catching glow effect that draws attention to the comparison section.\n\n\n\n```jsx\n<LandingPriceComparisonSection\n  title=\"Platform Comparison\"\n  description=\"See why developers choose our platform\"\n  variant=\"secondary\"\n  withBackgroundGlow\n  backgroundGlowVariant=\"secondary\"\n>\n  <LandingPriceComparisonColumn\n    header=\"Other Platforms\"\n    footer=\"Variable pricing\"\n  >\n    <LandingPriceComparisonItem state=\"cross\" text=\"Easy Setup\" />\n    <LandingPriceComparisonItem state=\"check\" text=\"Basic Documentation\" />\n    <LandingPriceComparisonItem state=\"cross\" text=\"Modern UI Components\" />\n    <LandingPriceComparisonItem state=\"cross\" text=\"One-click Deploy\" />\n  </LandingPriceComparisonColumn>\n\n  <LandingPriceComparisonColumn\n    featured\n    header=\"Our Platform\"\n    footer=\"Free to start\"\n    ctaText=\"Try It Now\"\n    href=\"#\"\n  >\n    <LandingPriceComparisonItem state=\"check\" text=\"Easy Setup\" />\n    <LandingPriceComparisonItem state=\"check\" text=\"Comprehensive Docs\" />\n    <LandingPriceComparisonItem state=\"check\" text=\"Modern UI Components\" />\n    <LandingPriceComparisonItem state=\"check\" text=\"One-click Deploy\" />\n  </LandingPriceComparisonColumn>\n</LandingPriceComparisonSection>\n```\n\n\n\n### Custom Icons and Content\n\n\n\n```jsx\n<LandingPriceComparisonSection title=\"Service Tiers\">\n  <LandingPriceComparisonColumn\n    header={\n      <div className=\"flex items-center gap-2\">\n        <LayersIcon className=\"w-6 h-6\" />\n        <span className='font-bold'>Competitor</span>\n      </div>\n    }\n    footer={\n      <div>\n        <div className=\"text-2xl font-bold\">$29</div>\n        <div className=\"text-sm text-gray-300\">per month</div>\n      </div>\n    }\n  >\n    <LandingPriceComparisonItem\n      icon={<div className=\"w-5 h-5 bg-red-500 rounded-full\"></div>}\n      text=\"5 Projects\"\n    />\n    <LandingPriceComparisonItem\n      icon={<div className=\"w-5 h-5 bg-red-500 rounded-full\"></div>}\n      text=\"Basic Support\"\n    />\n  </LandingPriceComparisonColumn>\n\n  <LandingPriceComparisonColumn\n    featured\n    header={\n      <div className=\"flex items-center gap-2\">\n        <SparklesIcon className=\"w-6 h-6\" />\n        <span className='font-bold'>Our Product</span>\n      </div>\n    }\n    footer={\n      <div>\n        <div className=\"text-2xl font-bold\">$29</div>\n        <div className=\"text-sm text-gray-300\">per month</div>\n      </div>\n    }\n    ctaTextComponent={\n      <Button variant=\"outlineSecondary\">\n        <span>Get Started</span>\n      </Button>\n    }\n  >\n    <LandingPriceComparisonItem\n      icon={<div className=\"w-5 h-5 bg-green-500 rounded-full\"></div>}\n      text=\"10 projects\"\n    />\n    <LandingPriceComparisonItem\n      icon={<div className=\"w-5 h-5 bg-green-500 rounded-full\"></div>}\n      text=\"Priority Support\"\n    />\n  </LandingPriceComparisonColumn>\n</LandingPriceComparisonSection>\n```\n\n\n</Examples>\n\n<PropsReference>\n### LandingPriceComparisonSection\n\n| Prop Name | Prop Type | Required | Default |\n| --- | --- | ----- | ---- |\n| **children** <Tippy>The comparison columns to display</Tippy> | `React.ReactNode` | Yes | - |\n| **className** <Tippy>Additional CSS classes for styling</Tippy> | `string` | No | - |\n| **title** <Tippy>Main heading for the comparison section</Tippy> | `string \\| React.ReactNode` | No | - |\n| **titleComponent** <Tippy>Custom title component override</Tippy> | `React.ReactNode` | No | - |\n| **description** <Tippy>Subtitle text below the main heading</Tippy> | `string \\| React.ReactNode` | No | - |\n| **descriptionComponent** <Tippy>Custom description component override</Tippy> | `React.ReactNode` | No | - |\n| **textPosition** <Tippy>Alignment of title and description text</Tippy> | `'center' \\| 'left'` | No | `'center'` |\n| **withBackground** <Tippy>Adds a subtle background color to the section</Tippy> | `boolean` | No | `false` |\n| **withBackgroundGlow** <Tippy>Adds a glow effect background</Tippy> | `boolean` | No | `false` |\n| **variant** <Tippy>Color scheme variant for backgrounds</Tippy> | `'primary' \\| 'secondary'` | No | `'primary'` |\n| **backgroundGlowVariant** <Tippy>Color variant for the glow effect</Tippy> | `'primary' \\| 'secondary'` | No | `'primary'` |\n\n### LandingPriceComparisonColumn\n\n| Prop Name | Prop Type | Required | Default |\n| --- | --- | ----- | ---- |\n| **children** <Tippy>The comparison items or content to display</Tippy> | `React.ReactNode` | Yes | - |\n| **className** <Tippy>Additional CSS classes for styling</Tippy> | `string` | No | - |\n| **header** <Tippy>Header content (usually product/service name)</Tippy> | `string \\| React.ReactNode` | No | - |\n| **headerComponent** <Tippy>Custom header component override</Tippy> | `React.ReactNode` | No | - |\n| **footer** <Tippy>Footer content (usually pricing information)</Tippy> | `string \\| React.ReactNode` | No | - |\n| **footerComponent** <Tippy>Custom footer component override</Tippy> | `React.ReactNode` | No | - |\n| **featured** <Tippy>Highlights this column as the featured/recommended option</Tippy> | `boolean` | No | `false` |\n| **ctaText** <Tippy>Text for the call-to-action button</Tippy> | `string` | No | - |\n| **ctaTextComponent** <Tippy>Custom call-to-action button component</Tippy> | `React.ReactNode` | No | - |\n| **href** <Tippy>URL for the call-to-action button link</Tippy> | `string` | No | - |\n| **onClick** <Tippy>Click handler for the call-to-action button</Tippy> | `() => void` | No | - |\n| **variant** <Tippy>Color scheme variant for backgrounds</Tippy> | `'primary' \\| 'secondary'` | No | `'primary'` |\n\n### LandingPriceComparisonItem\n\n| Prop Name | Prop Type | Required | Default |\n| --- | --- | ----- | ---- |\n| **className** <Tippy>Additional CSS classes for styling</Tippy> | `string` | No | - |\n| **icon** <Tippy>Custom icon to display (overrides state-based icons)</Tippy> | `React.ReactNode` | No | - |\n| **iconComponent** <Tippy>Alternative way to pass custom icon component</Tippy> | `React.ReactNode` | No | - |\n| **text** <Tippy>The feature text to display</Tippy> | `string \\| React.ReactNode` | No | - |\n| **textComponent** <Tippy>Alternative way to pass custom text component</Tippy> | `React.ReactNode` | No | - |\n| **description** <Tippy>Additional details shown in tooltip on hover</Tippy> | `string \\| React.ReactNode` | No | - |\n| **descriptionComponent** <Tippy>Custom description component for tooltip</Tippy> | `React.ReactNode` | No | - |\n| **state** <Tippy>Predefined state that determines icon and styling</Tippy> | `'check' \\| 'cross' \\| 'neutral' \\| 'custom'` | No | `'neutral'` |\n| **showText** <Tippy>Whether to display the text content visibly</Tippy> | `boolean` | No | `false` |\n| **showDescription** <Tippy>Whether to show the info icon with tooltip</Tippy> | `boolean` | No | `false` |\n</PropsReference>"}, "PricingPlan": {"description": "This component displays a pricing plan (tier) with features and a call-to-action button. On smaller screens, the layout becomes a single column to ensure usability. Meant to be used as a child of: [Landing Page Pricing Section](/boilerplate-documentation/landing-page-components/pricing)", "api": "<Usage>\n```jsx\nimport { LandingPricingPlan } from '@/components/landing/pricing/LandingPricingPlan';\n```\n\n```jsx\n<LandingPricingPlan\n  title=\"Pro\"\n  description=\"For larger teams or businesses.\"\n  ctaText=\"Upgrade now\"\n  price=\"$20\"\n  priceSuffix=\"/mo\"\n  highlighted\n>\n  <p>Unlimited users</p>\n  <p>AI features</p>\n  <p>Priority support</p>\n</LandingPricingPlan>\n```\n</Usage>\n\n<Examples>\n### With Price suffix\nFor some pricing plans, you may want to add a suffix to the price, like /mo or /year etc.\n\n\n\n```jsx\nimport { LandingPricingPlan } from '@/components/landing/pricing/LandingPricingPlan';\n\n<LandingPricingPlan\n  title=\"Pro\"\n  description=\"For larger teams or businesses.\"\n  ctaText=\"Upgrade now\"\n  price=\"$20\"\n  priceSuffix=\"/mo\"\n>\n  <p>Unlimited users</p>\n  <p>AI features</p>\n  <p>Priority support</p>\n</LandingPricingPlan>\n```\n\n\n\n### With highlighted plan\n\n\n```jsx\nimport { LandingPricingPlan } from '@/components/landing/pricing/LandingPricingPlan';\n\n<LandingPricingPlan\n  title=\"Pro\"\n  description=\"For larger teams or businesses.\"\n  ctaText=\"Upgrade now\"\n  price=\"$20\"\n  highlighted\n>\n  <p>Unlimited users</p>\n  <p>AI features</p>\n  <p>Priority support</p>\n</LandingPricingPlan>\n```\n\n\n\n### With featured plan\nFeatured plans are meant to stand out and are usually used for more expensive / enterprise plans.\n\n\n\n```jsx\nimport { LandingPricingPlan } from '@/components/landing/pricing/LandingPricingPlan';\n\n<LandingPricingPlan\n  title=\"Pro\"\n  description=\"For larger teams or businesses.\"\n  ctaText=\"Upgrade now\"\n  price=\"$20\"\n  featured\n>\n  <p>Unlimited users</p>\n  <p>AI features</p>\n  <p>Priority support</p>\n</LandingPricingPlan>\n```\n\n\n\n### With discount\n\n\n```jsx\nimport { LandingPricingPlan } from '@/components/landing/pricing/LandingPricingPlan';\n\n<LandingPricingPlan\n  title=\"Pro\"\n  description=\"For larger teams or businesses.\"\n  ctaText=\"Upgrade now\"\n  price=\"$20\"\n  discountPrice=\"$10\"\n>\n  <p>Unlimited users</p>\n  <p>AI features</p>\n  <p>Priority support</p>\n</LandingPricingPlan>\n```\n\n\n\n### Sold out\n\n\n```jsx\nimport { LandingPricingPlan } from '@/components/landing/pricing/LandingPricingPlan';\n\n<LandingPricingPlan\n  title=\"Pro\"\n  description=\"For larger teams or businesses.\"\n  ctaText=\"Upgrade now\"\n  price=\"$20\"\n  soldOut\n>\n  <p>Unlimited users</p>\n  <p>AI features</p>\n  <p>Priority support</p>\n</LandingPricingPlan>\n```\n\n\n</Examples>\n\n<PropsReference>\n| Prop Name | Prop Type | Required | Default |\n| --------- | --------- | -------- | ------- |\n| **children** <Tippy>React nodes to be rendered within the component.</Tippy> | `React.ReactNode` | Yes | - |\n| **title** <Tippy>The title of the pricing plan.</Tippy> | `string` | No | - |\n| **titleComponent** <Tippy>Custom React node to replace the title.</Tippy> | `React.ReactNode` | No | - |\n| **description** <Tippy>A brief description of the pricing plan.</Tippy> | `string` | No | - |\n| **descriptionComponent** <Tippy>Custom React node to replace the description.</Tippy> | `React.ReactNode` | No | - |\n| **href** <Tippy>The link for the call-to-action button.</Tippy> | `string` | No | `\"#\"` |\n| **onClick** <Tippy>Click handler for the call-to-action button.</Tippy> | `() => void` | No | `() => {}` |\n| **ctaText** <Tippy>Text displayed on the call-to-action button.</Tippy> | `string` | No | `\"Get started\"` |\n| **price** <Tippy>The main price of the plan.</Tippy> | `string` | Yes | - |\n| **discountPrice** <Tippy>A discounted price if applicable.</Tippy> | `string` | No | - |\n| **priceSuffix** <Tippy>Suffix text displayed after the price.</Tippy> | `string` | No | - |\n| **featured** <Tippy>Marks the plan as featured, affecting styling.</Tippy> | `boolean` | No | - |\n| **highlighted** <Tippy>Highlights the plan visually.</Tippy> | `boolean` | No | - |\n| **soldOut** <Tippy>Disables the call-to-action button if the plan is sold out.</Tippy> | `boolean` | No | - |\n</PropsReference>"}, "Pricing": {"description": "This component displays different pricing tiers with features and a call-to-action button. On smaller screens, the layout becomes a single column to ensure usability. Also see: [Landing Page Pricing Plan](/boilerplate-documentation/landing-page-components/pricing-plan)", "api": "<Usage>\n```jsx\nimport { LandingPricingSection } from '@/components/landing/pricing/LandingPricingSection';\nimport { LandingPricingPlan } from '@/components/landing/pricing/LandingPricingPlan';\n```\n\n```jsx\n<LandingPricingSection\n  title=\"Simple, scalable pricing\"\n  description=\"Affordable pricing plans tailored to your needs. Choose a plan that works best for you.\"\n>\n  <LandingPricingPlan\n    title=\"Free\"\n    description=\"For small teams & personal use.\"\n    price=\"$0\"\n  >\n    <p>Up to 5 users</p>\n    <p>Basic features</p>\n    <p>Discord access</p>\n  </LandingPricingPlan>\n\n  <LandingPricingPlan\n    title=\"Pro\"\n    description=\"For larger teams or businesses.\"\n    ctaText=\"Upgrade now\"\n    price=\"$20\"\n    priceSuffix=\"/mo\"\n    highlighted\n  >\n    <p>Unlimited users</p>\n    <p>AI features</p>\n    <p>Priority support</p>\n  </LandingPricingPlan>\n</LandingPricingSection>\n```\n</Usage>\n\n<Examples>\n### Text position\n\n\n```jsx\nimport { LandingPricingSection } from '@/components/landing/pricing/LandingPricingSection';\nimport { LandingPricingPlan } from '@/components/landing/pricing/LandingPricingPlan';\n\n<LandingPricingSection\n  title=\"Simple, scalable pricing\"\n  description=\"Affordable pricing plans tailored to your needs. Choose a plan that works best for you.\"\n  textPosition=\"left\"\n>\n  <LandingPricingPlan\n    title=\"Free\"\n    description=\"For small teams & personal use.\"\n    price=\"$0\"\n  >\n    <p>Up to 5 users</p>\n    <p>Basic features</p>\n    <p>Discord access</p>\n  </LandingPricingPlan>\n\n  <LandingPricingPlan\n    title=\"Pro\"\n    description=\"For larger teams or businesses.\"\n    ctaText=\"Upgrade now\"\n    price=\"$20\"\n  >\n    <p>Unlimited users</p>\n    <p>AI features</p>\n    <p>Priority support</p>\n  </LandingPricingPlan>\n</LandingPricingSection>\n```\n\n\n\n### With Price suffix\nFor some pricing plans, you may want to add a suffix to the price, like /mo or /year etc.\n\n\n\n```jsx\nimport { LandingPricingSection } from '@/components/landing/pricing/LandingPricingSection';\nimport { LandingPricingPlan } from '@/components/landing/pricing/LandingPricingPlan';\n\n<LandingPricingSection\n  title=\"Simple, scalable pricing\"\n  description=\"Affordable pricing plans tailored to your needs. Choose a plan that works best for you.\"\n>\n  <LandingPricingPlan\n    title=\"Free\"\n    description=\"For small teams & personal use.\"\n    price=\"$0\"\n    priceSuffix=\"/forever\"\n  >\n    <p>Up to 5 users</p>\n    <p>Basic features</p>\n    <p>Discord access</p>\n  </LandingPricingPlan>\n\n  <LandingPricingPlan\n    title=\"Pro\"\n    description=\"For larger teams or businesses.\"\n    ctaText=\"Upgrade now\"\n    price=\"$20\"\n    priceSuffix=\"/mo\"\n  >\n    <p>Unlimited users</p>\n    <p>AI features</p>\n    <p>Priority support</p>\n  </LandingPricingPlan>\n</LandingPricingSection>\n```\n\n\n\n### With highlighted plan\n\n\n```jsx\nimport { LandingPricingSection } from '@/components/landing/pricing/LandingPricingSection';\nimport { LandingPricingPlan } from '@/components/landing/pricing/LandingPricingPlan';\n\n<LandingPricingSection\n  title=\"Simple, scalable pricing\"\n  description=\"Affordable pricing plans tailored to your needs. Choose a plan that works best for you.\"\n>\n  <LandingPricingPlan\n    title=\"Free\"\n    description=\"For small teams & personal use.\"\n    price=\"$0\"\n  >\n    <p>Up to 5 users</p>\n    <p>Basic features</p>\n    <p>Discord access</p>\n  </LandingPricingPlan>\n\n  <LandingPricingPlan\n    title=\"Pro\"\n    description=\"For larger teams or businesses.\"\n    ctaText=\"Upgrade now\"\n    price=\"$20\"\n    highlighted\n  >\n    <p>Unlimited users</p>\n    <p>AI features</p>\n    <p>Priority support</p>\n  </LandingPricingPlan>\n</LandingPricingSection>\n```\n\n\n\n### With featured plan\nFeatured plans are meant to stand out and are usually used for more expensive / enterprise plans.\n\n\n\n```jsx\nimport { LandingPricingSection } from '@/components/landing/pricing/LandingPricingSection';\nimport { LandingPricingPlan } from '@/components/landing/pricing/LandingPricingPlan';\n\n<LandingPricingSection\n  title=\"Simple, scalable pricing\"\n  description=\"Affordable pricing plans tailored to your needs. Choose a plan that works best for you.\"\n>\n  <LandingPricingPlan\n    title=\"Free\"\n    description=\"For small teams & personal use.\"\n    price=\"$0\"\n  >\n    <p>Up to 5 users</p>\n    <p>Basic features</p>\n    <p>Discord access</p>\n  </LandingPricingPlan>\n\n  <LandingPricingPlan\n    title=\"Pro\"\n    description=\"For larger teams or businesses.\"\n    ctaText=\"Upgrade now\"\n    price=\"$20\"\n    featured\n  >\n    <p>Unlimited users</p>\n    <p>AI features</p>\n    <p>Priority support</p>\n  </LandingPricingPlan>\n</LandingPricingSection>\n```\n\n\n\n### With discount\n\n\n```jsx\nimport { LandingPricingSection } from '@/components/landing/pricing/LandingPricingSection';\nimport { LandingPricingPlan } from '@/components/landing/pricing/LandingPricingPlan';\n\n<LandingPricingSection\n  title=\"Simple, scalable pricing\"\n  description=\"Affordable pricing plans tailored to your needs. Choose a plan that works best for you.\"\n>\n  <LandingPricingPlan\n    title=\"Free\"\n    description=\"For small teams & personal use.\"\n    price=\"$0\"\n  >\n    <p>Up to 5 users</p>\n    <p>Basic features</p>\n    <p>Discord access</p>\n  </LandingPricingPlan>\n\n  <LandingPricingPlan\n    title=\"Pro\"\n    description=\"For larger teams or businesses.\"\n    ctaText=\"Upgrade now\"\n    price=\"$20\"\n    discountPrice=\"$10\"\n  >\n    <p>Unlimited users</p>\n    <p>AI features</p>\n    <p>Priority support</p>\n  </LandingPricingPlan>\n</LandingPricingSection>\n```\n\n\n\n### Sold out\n\n\n```jsx\nimport { LandingPricingSection } from '@/components/landing/pricing/LandingPricingSection';\nimport { LandingPricingPlan } from '@/components/landing/pricing/LandingPricingPlan';\n\n<LandingPricingSection\n  title=\"Simple, scalable pricing\"\n  description=\"Affordable pricing plans tailored to your needs. Choose a plan that works best for you.\"\n>\n  <LandingPricingPlan\n    title=\"Free\"\n    description=\"For small teams & personal use.\"\n    price=\"$0\"\n  >\n    <p>Up to 5 users</p>\n    <p>Basic features</p>\n    <p>Discord access</p>\n  </LandingPricingPlan>\n\n  <LandingPricingPlan\n    title=\"Pro\"\n    description=\"For larger teams or businesses.\"\n    ctaText=\"Upgrade now\"\n    price=\"$20\"\n    soldOut\n  >\n    <p>Unlimited users</p>\n    <p>AI features</p>\n    <p>Priority support</p>\n  </LandingPricingPlan>\n</LandingPricingSection>\n```\n\n\n\n### Multiple columns\nYou can use the pricing section to display between 1 to 4 pricing plans. The layout will automatically adjust based on the number of plans you provide.\n\n\n\n```jsx\nimport { LandingPricingSection } from '@/components/landing/pricing/LandingPricingSection';\nimport { LandingPricingPlan } from '@/components/landing/pricing/LandingPricingPlan';\n\n<LandingPricingSection\n  title=\"Simple, scalable pricing\"\n  description=\"Affordable pricing plans tailored to your needs. Choose a plan that works best for you.\"\n>\n  <LandingPricingPlan\n    title=\"Free\"\n    description=\"For small teams & personal use.\"\n    price=\"$0\"\n  >\n    <p>Up to 5 users</p>\n    <p>Basic features</p>\n    <p>Discord access</p>\n  </LandingPricingPlan>\n\n  <LandingPricingPlan\n    title=\"Pro\"\n    description=\"For larger teams or businesses.\"\n    ctaText=\"Upgrade\"\n    price=\"$20\"\n    priceSuffix=\"/mo\"\n    highlighted\n  >\n    <p>Unlimited users</p>\n    <p>AI features</p>\n    <p>Priority support</p>\n  </LandingPricingPlan>\n\n  <LandingPricingPlan\n    title=\"Enterprise\"\n    description=\"For enterprise teams & businesses.\"\n    ctaText=\"Sign up\"\n    price=\"$100\"\n    priceSuffix=\"/mo\"\n    featured\n  >\n    <p>Unlimited users</p>\n    <p>AI features</p>\n    <p>Priority support</p>\n  </LandingPricingPlan>\n</LandingPricingSection>\n```\n\n\n\n\n### With background\n\n\n```jsx\nimport { LandingPricingSection } from '@/components/landing/pricing/LandingPricingSection';\nimport { LandingPricingPlan } from '@/components/landing/pricing/LandingPricingPlan';\n\n<LandingPricingSection\n  title=\"Simple, scalable pricing\"\n  description=\"Affordable pricing plans tailored to your needs. Choose a plan that works best for you.\"\n  withBackground\n>\n  <LandingPricingPlan\n    title=\"Free\"\n    description=\"For small teams & personal use.\"\n    price=\"$0\"\n  >\n    <p>Up to 5 users</p>\n    <p>Basic features</p>\n    <p>Discord access</p>\n  </LandingPricingPlan>\n\n  <LandingPricingPlan\n    title=\"Pro\"\n    description=\"For larger teams or businesses.\"\n    ctaText=\"Upgrade now\"\n    price=\"$20\"\n  >\n    <p>Unlimited users</p>\n    <p>AI features</p>\n    <p>Priority support</p>\n  </LandingPricingPlan>\n</LandingPricingSection>\n```\n\n\n\n### With background glow\n\n\n```jsx\nimport { LandingPricingSection } from '@/components/landing/pricing/LandingPricingSection';\nimport { LandingPricingPlan } from '@/components/landing/pricing/LandingPricingPlan';\n\n<LandingPricingSection\n  title=\"Simple, scalable pricing\"\n  description=\"Affordable pricing plans tailored to your needs. Choose a plan that works best for you.\"\n  withBackgroundGlow\n>\n  <LandingPricingPlan\n    title=\"Free\"\n    description=\"For small teams & personal use.\"\n    price=\"$0\"\n  >\n    <p>Up to 5 users</p>\n    <p>Basic features</p>\n    <p>Discord access</p>\n  </LandingPricingPlan>\n\n  <LandingPricingPlan\n    title=\"Pro\"\n    description=\"For larger teams or businesses.\"\n    ctaText=\"Upgrade now\"\n    price=\"$20\"\n  >\n    <p>Unlimited users</p>\n    <p>AI features</p>\n    <p>Priority support</p>\n  </LandingPricingPlan>\n</LandingPricingSection>\n```\n\n\n</Examples>\n\n<PropsReference>\n| Prop Name | Prop Type | Required | Default |\n| --------- | --------- | -------- | ------- |\n| **children** <Tippy>React nodes to be rendered within the component (below the text on smaller screen, right side on larger).</Tippy> | `React.ReactNode` | No | - |\n| **title** <Tippy>The main title text or React node.</Tippy> | `string` ǀ `React.ReactNode` | No | - |\n| **titleComponent** <Tippy>A React node to render as the title instead of text.</Tippy> | `React.ReactNode` | No | - |\n| **description** <Tippy>The description text or React node.</Tippy> | `string` ǀ `React.ReactNode` | No | - |\n| **descriptionComponent** <Tippy>A React node to render as the description instead of text.</Tippy> | `React.ReactNode` | No | - |\n| **textPosition** <Tippy>Position of the text content.</Tippy> | `'center'` ǀ `'left'` | No | `'center'` |\n| **withBackground** <Tippy>Whether to display a background.</Tippy> | `boolean` | No | `false` |\n| **withBackgroundGlow** <Tippy>Whether to add a glowing background effect.</Tippy> | `boolean` | No | `false` |\n| **withAvatars** <Tippy>Whether to display avatars.</Tippy> | `boolean` | No | `false` |\n| **variant** <Tippy>The visual style of the component.</Tippy> | `'primary'` ǀ `'secondary'` | No | `'primary'` |\n| **backgroundGlowVariant** <Tippy>The variant of the glowing background effect.</Tippy> | `'primary'` ǀ `'secondary'` | No | `'primary'` |\n</PropsReference>"}, "PrimaryCtaEffects": {"description": "A collection of animated background effects for the primary CTA section. These components are meant to be used together with `LandingPrimaryImageCtaSection`, `LandingPrimaryTextCtaSection` and `LandingPrimaryVideoCtaSection`. On a landing page, they can make it stand out and make it more memorable and depending on the background effect - more engaging. ## `LandingDotParticleCtaBg` **Dot Particle with CTA**", "api": ""}, "PrimaryCtaTextEffects": {"description": "A collection of text effects for the primary CTA section. These are meant to be used together with `LandingPrimaryImageCtaSection`, `LandingPrimaryTextCtaSection` and `LandingPrimaryVideoCtaSection`, as part of the `titleComponent` prop. On a landing page, they can be used to emphasize parts of the CTA title and increase conversion rates. There are not components per say, but a collection of patterns using Tailwind CSS effects and text effects. ## Monochrome Gradient Text **Monochrome Gradient Text**<br/> This works with `primary`, `secondary` and Tailwind color schemes.", "api": ""}, "PrimaryImageCta": {"description": "Use this component on landing page as the primary Call to Action section. A section that shows a title & description. Optionally, it can have actions (children), leading components and a background glow. This is the most important section of your landing page. Use it to grab the attention of your visitors and encourage them to take action.", "api": "<Usage>\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryImageCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\n```\n\n```jsx\n<LandingPrimaryImageCtaSection\n  title=\"Landing page in minutes\"\n  description=\"Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back.\"\n  imageSrc=\"/static/images/shipixen/product/1.webp\"\n  imageAlt=\"Sample image\"\n>\n  <Button size=\"xl\" asChild>\n    <a href=\"#\">Buy now</a>\n  </Button>\n</LandingPrimaryImageCtaSection>\n```\n</Usage>\n\n<Examples>\n### Image Perspective\n\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryImageCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\n\n<LandingPrimaryImageCtaSection\n  title=\"Landing page in minutes\"\n  description=\"Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back.\"\n  imageSrc=\"/static/images/shipixen/product/1.webp\"\n  imageAlt=\"Sample image\"\n  imagePerspective=\"paper\"\n>\n  <Button size=\"xl\" asChild>\n    <a href=\"#\">Buy now</a>\n  </Button>\n\n  <Button size=\"xl\" variant=\"outlinePrimary\">\n    <a href=\"#\">Read more</a>\n  </Button>\n</LandingPrimaryImageCtaSection>;\n```\n\n\n\n### Image Position\n\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryImageCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\n\n<LandingPrimaryImageCtaSection\n  title=\"Landing page in minutes\"\n  description=\"Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back.\"\n  imageSrc=\"/static/images/shipixen/product/1.webp\"\n  imageAlt=\"Sample image\"\n  imagePosition=\"center\"\n  textPosition=\"center\"\n>\n  <Button size=\"xl\" asChild>\n    <a href=\"#\">Buy now</a>\n  </Button>\n\n  <Button size=\"xl\" variant=\"outlinePrimary\">\n    <a href=\"#\">Read more</a>\n  </Button>\n</LandingPrimaryImageCtaSection>;\n```\n\n\n\n### With Social Proof\n\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryImageCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\nimport { LandingSocialProof } from '@/components/landing/social-proof/LandingSocialProof';\n\n<LandingPrimaryImageCtaSection\n  title=\"Landing page in minutes\"\n  description=\"Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back.\"\n  imageSrc=\"/static/images/shipixen/product/1.webp\"\n  imageAlt=\"Sample image\"\n  imagePosition=\"center\"\n  textPosition=\"center\"\n>\n  <Button size=\"xl\" asChild>\n    <a href=\"#\">Buy now</a>\n  </Button>\n\n  <Button size=\"xl\" variant=\"outlinePrimary\">\n    <a href=\"#\">Read more</a>\n  </Button>\n\n  <LandingSocialProof\n    className=\"mt-6 w-full flex justify-center\"\n    showRating\n    numberOfUsers={99}\n    avatarItems={[\n      {\n        imageSrc: '/static/images/people/1.webp',\n        name: 'John Doe',\n      },\n      {\n        imageSrc: '/static/images/people/2.webp',\n        name: 'Jane Doe',\n      },\n      {\n        imageSrc: '/static/images/people/3.webp',\n        name: 'Alice Doe',\n      },\n    ]}\n  />\n</LandingPrimaryImageCtaSection>;\n```\n\n\n\n### With Discount/Offer\n\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryImageCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\nimport { LandingDiscount } from '@/components/landing/discount/LandingDiscount';\n\n<LandingPrimaryImageCtaSection\n  title=\"Landing page in minutes\"\n  description=\"Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back.\"\n  imageSrc=\"/static/images/shipixen/product/1.webp\"\n  imageAlt=\"Sample image\"\n  imagePosition=\"center\"\n  textPosition=\"center\"\n>\n  <Button size=\"xl\" asChild>\n    <a href=\"#\">Buy now</a>\n  </Button>\n\n  <Button size=\"xl\" variant=\"outlinePrimary\">\n    <a href=\"#\">Read more</a>\n  </Button>\n\n  <LandingDiscount\n    className=\"w-full flex justify-center\"\n    discountValueText=\"$350 off\"\n    discountDescriptionText=\"for the first 10 customers (2 left)\"\n  />\n</LandingPrimaryImageCtaSection>;\n```\n\n\n\n### With Discount and Left Alignment\n\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryImageCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\nimport { LandingDiscount } from '@/components/landing/discount/LandingDiscount';\n\n<LandingPrimaryImageCtaSection\n  title=\"Landing page in minutes\"\n  description=\"Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back.\"\n  imageSrc=\"/static/images/shipixen/product/1.webp\"\n  imageAlt=\"Sample image\"\n>\n  <Button size=\"xl\" asChild>\n    <a href=\"#\">Buy now</a>\n  </Button>\n\n  <Button size=\"xl\" variant=\"outlinePrimary\">\n    <a href=\"#\">Read more</a>\n  </Button>\n\n  <LandingDiscount\n    className=\"w-full\"\n    discountValueText=\"$350 off\"\n    discountDescriptionText=\"for the first 10 customers (2 left)\"\n  />\n</LandingPrimaryImageCtaSection>;\n```\n\n\n\n### With Bullet Points\n\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryImageCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\nimport { LandingDiscount } from '@/components/landing/discount/LandingDiscount';\nimport { LandingProductFeatureKeyPoints } from '@/components/landing/LandingProductFeatureKeyPoints';\n\n<LandingPrimaryImageCtaSection\n  title=\"Landing page in minutes\"\n  descriptionComponent={\n    <LandingProductFeatureKeyPoints\n      keyPoints={[\n        {\n          title: 'Intelligent Assistance',\n          description:\n            'Receive personalized recommendations and insights tailored to your workflow.',\n        },\n        {\n          title: 'Seamless Collaboration',\n          description:\n            'Easily collaborate with team members and clients in real-time.',\n        },\n        {\n          title: 'Advanced Customization',\n          description:\n            'Tailor your app to fit your unique requirements with extensive customization options.',\n        },\n      ]}\n    />\n  }\n  textPosition=\"left\"\n  imageSrc=\"/static/images/shipixen/product/1.webp\"\n  imageAlt=\"Sample image\"\n>\n  <Button size=\"xl\" asChild>\n    <a href=\"#\">Buy now</a>\n  </Button>\n\n  <Button size=\"xl\" variant=\"outlinePrimary\">\n    <a href=\"#\">Read more</a>\n  </Button>\n\n  <LandingDiscount\n    className=\"w-full\"\n    discountValueText=\"$350 off\"\n    discountDescriptionText=\"for the first 10 customers (2 left)\"\n  />\n</LandingPrimaryImageCtaSection>;\n```\n\n\n\n### With Product Hunt Award\n\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryImageCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\nimport { LandingDiscount } from '@/components/landing/discount/LandingDiscount';\nimport { LandingProductHuntAward } from '@/components/landing/social-proof/LandingProductHuntAward';\n\n<LandingPrimaryImageCtaSection\n  title=\"Landing page in minutes\"\n  description=\"Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back.\"\n  imageSrc=\"/static/images/shipixen/product/1.webp\"\n  imageAlt=\"Sample image\"\n  leadingComponent={<LandingProductHuntAward />}\n>\n  <Button size=\"xl\" asChild>\n    <a href=\"#\">Buy now</a>\n  </Button>\n\n  <Button size=\"xl\" variant=\"outlinePrimary\">\n    <a href=\"#\">Read more</a>\n  </Button>\n\n  <LandingDiscount\n    className=\"w-full\"\n    discountValueText=\"$350 off\"\n    discountDescriptionText=\"for the first 10 customers (2 left)\"\n  />\n</LandingPrimaryImageCtaSection>;\n```\n\n\n\n### With Social Proof Band\n\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryImageCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\nimport { LandingDiscount } from '@/components/landing/discount/LandingDiscount';\nimport { LandingProductHuntAward } from '@/components/landing/social-proof/LandingProductHuntAward';\nimport { LandingSocialProofBand } from '@/components/landing/social-proof/LandingSocialProofBand';\nimport { LandingSocialProofBandItem } from '@/components/landing/social-proof/LandingSocialProofBandItem';\n\n<LandingSocialProofBand>\n  <LandingSocialProofBandItem>\n    100% encrypted and secure\n  </LandingSocialProofBandItem>\n\n  <LandingSocialProofBandItem>24/7 customer support</LandingSocialProofBandItem>\n\n  <LandingSocialProofBandItem>\n    99% customer satisfaction\n  </LandingSocialProofBandItem>\n</LandingSocialProofBand>\n<LandingPrimaryImageCtaSection\n  title=\"Landing page in minutes\"\n  description=\"Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back.\"\n  imageSrc=\"/static/images/shipixen/product/1.webp\"\n  imageAlt=\"Sample image\"\n  leadingComponent={<LandingProductHuntAward />}\n>\n  <Button size=\"xl\" asChild>\n    <a href=\"#\">\n      Buy now\n    </a>\n  </Button>\n\n  <Button size=\"xl\" variant=\"outlinePrimary\">\n    <a href=\"#\">Read more</a>\n  </Button>\n\n  <LandingDiscount\n    className=\"w-full\"\n    discountValueText=\"$350 off\"\n    discountDescriptionText=\"for the first 10 customers (2 left)\"\n  />\n</LandingPrimaryImageCtaSection>\n```\n\n\n\n### With Background\n\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryImageCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\nimport { LandingDiscount } from '@/components/landing/discount/LandingDiscount';\nimport { LandingProductHuntAward } from '@/components/landing/social-proof/LandingProductHuntAward';\n\n<LandingPrimaryImageCtaSection\n  title=\"Landing page in minutes\"\n  description=\"Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back.\"\n  imageSrc=\"/static/images/shipixen/product/1.webp\"\n  imageAlt=\"Sample image\"\n  withBackground\n  variant=\"secondary\"\n  leadingComponent={<LandingProductHuntAward />}\n>\n  <Button size=\"xl\" variant=\"secondary\" asChild>\n    <a href=\"#\">Buy now</a>\n  </Button>\n\n  <Button size=\"xl\" variant=\"outlineSecondary\">\n    <a href=\"#\">Read more</a>\n  </Button>\n\n  <LandingDiscount\n    className=\"w-full\"\n    discountValueText=\"$350 off\"\n    discountDescriptionText=\"for the first 10 customers (2 left)\"\n  />\n</LandingPrimaryImageCtaSection>;\n```\n\n\n\n### With Background Glow\n\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryImageCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\nimport { LandingDiscount } from '@/components/landing/discount/LandingDiscount';\nimport { LandingProductHuntAward } from '@/components/landing/social-proof/LandingProductHuntAward';\n\n<LandingPrimaryImageCtaSection\n  title=\"Landing page in minutes\"\n  description=\"Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back.\"\n  imageSrc=\"/static/images/shipixen/product/1.webp\"\n  imageAlt=\"Sample image\"\n  withBackground\n  withBackgroundGlow\n  leadingComponent={<LandingProductHuntAward />}\n>\n  <Button size=\"xl\" asChild>\n    <a href=\"#\">Buy now</a>\n  </Button>\n\n  <Button size=\"xl\" variant=\"outlinePrimary\">\n    <a href=\"#\">Read more</a>\n  </Button>\n\n  <LandingDiscount\n    className=\"w-full\"\n    discountValueText=\"$350 off\"\n    discountDescriptionText=\"for the first 10 customers (2 left)\"\n  />\n</LandingPrimaryImageCtaSection>;\n```\n\n\n\n### Left-aligned Full Example\n\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryImageCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\nimport { LandingDiscount } from '@/components/landing/discount/LandingDiscount';\nimport { LandingProductHuntAward } from '@/components/landing/social-proof/LandingProductHuntAward';\nimport { LandingSocialProof } from '@/components/landing/social-proof/LandingSocialProof';\nimport { LandingSocialProofBand } from '@/components/landing/social-proof/LandingSocialProofBand';\nimport { LandingSocialProofBandItem } from '@/components/landing/social-proof/LandingSocialProofBandItem';\n\n<LandingSocialProofBand>\n  <LandingSocialProofBandItem>\n    100% encrypted and secure\n  </LandingSocialProofBandItem>\n\n  <LandingSocialProofBandItem>24/7 customer support</LandingSocialProofBandItem>\n\n  <LandingSocialProofBandItem>\n    99% customer satisfaction\n  </LandingSocialProofBandItem>\n</LandingSocialProofBand>\n\n<LandingPrimaryImageCtaSection\n  title=\"Landing page in minutes\"\n  description=\"Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back.\"\n  imageSrc=\"/static/images/shipixen/product/1.webp\"\n  imageAlt=\"Sample image\"\n  withBackground\n  withBackgroundGlow\n  leadingComponent={<LandingProductHuntAward />}\n>\n  <Button size=\"xl\" asChild>\n    <a href=\"#\">\n      Buy now\n    </a>\n  </Button>\n\n  <Button size=\"xl\" variant=\"outlinePrimary\">\n    <a href=\"#\">Read more</a>\n  </Button>\n\n  <LandingSocialProof\n    className=\"w-full mt-12\"\n    showRating\n    numberOfUsers={99}\n    avatarItems={[\n      {\n        imageSrc: '/static/images/people/1.webp',\n        name: 'John Doe',\n      },\n      {\n        imageSrc: '/static/images/people/2.webp',\n        name: 'Jane Doe',\n      },\n      {\n        imageSrc: '/static/images/people/3.webp',\n        name: 'Alice Doe',\n      },\n    ]}\n  />\n</LandingPrimaryImageCtaSection>\n```\n\n\n\n### Centered full example\n\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryImageCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\nimport { LandingDiscount } from '@/components/landing/discount/LandingDiscount';\nimport { LandingProductHuntAward } from '@/components/landing/social-proof/LandingProductHuntAward';\nimport { LandingSocialProof } from '@/components/landing/social-proof/LandingSocialProof';\nimport { LandingSocialProofBand } from '@/components/landing/social-proof/LandingSocialProofBand';\nimport { LandingSocialProofBandItem } from '@/components/landing/social-proof/LandingSocialProofBandItem';\n\n<LandingSocialProofBand invert>\n  <LandingSocialProofBandItem>\n    100% encrypted and secure\n  </LandingSocialProofBandItem>\n\n  <LandingSocialProofBandItem>24/7 customer support</LandingSocialProofBandItem>\n\n  <LandingSocialProofBandItem>\n    99% customer satisfaction\n  </LandingSocialProofBandItem>\n</LandingSocialProofBand>\n\n<LandingPrimaryImageCtaSection\n  title=\"Landing page in minutes\"\n  description=\"Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back.\"\n  imageSrc=\"/static/images/shipixen/product/1.webp\"\n  imageAlt=\"Sample image\"\n  imagePosition=\"center\"\n  textPosition=\"center\"\n  withBackground\n  withBackgroundGlow\n  variant=\"secondary\"\n  backgroundGlowVariant=\"secondary\"\n  leadingComponent={<LandingProductHuntAward />}\n>\n  <Button size=\"xl\" variant=\"secondary\" asChild>\n    <a href=\"#\">\n      Buy now\n    </a>\n  </Button>\n\n  <Button size=\"xl\" variant=\"outlineSecondary\">\n    <a href=\"#\">Read more</a>\n  </Button>\n\n  <LandingDiscount\n    className=\"w-full flex justify-center\"\n    discountValueText=\"$350 off\"\n    discountDescriptionText=\"for the first 10 customers (2 left)\"\n  />\n\n  <LandingSocialProof\n    className=\"mt-12 w-full flex justify-center\"\n    showRating\n    numberOfUsers={99}\n    avatarItems={[\n      {\n        imageSrc: '/static/images/people/1.webp',\n        name: 'John Doe',\n      },\n      {\n        imageSrc: '/static/images/people/2.webp',\n        name: 'Jane Doe',\n      },\n      {\n        imageSrc: '/static/images/people/3.webp',\n        name: 'Alice Doe',\n      },\n    ]}\n  />\n</LandingPrimaryImageCtaSection>\n```\n\n\n\n### With Newsletter Form\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryImageCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\nimport { LandingNewsletterInput } from '@/components/landing/newsletter/LandingNewsletterInput';\n\n<LandingPrimaryImageCtaSection\n  title=\"Sign up now\"\n  description=\"Never miss an update! Subscribe today for the latest announcements.\"\n  imageSrc=\"/static/images/shipixen/product/1.webp\"\n  imageAlt=\"Sample image\"\n>\n  <LandingNewsletterInput />\n</LandingPrimaryImageCtaSection>\n```\n\n\n\n\n### With Newsletter and Social Proof\n\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryImageCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\nimport { LandingSocialProof } from '@/components/landing/social-proof/LandingSocialProof';\nimport { LandingNewsletterInput } from '@/components/landing/newsletter/LandingNewsletterInput';\n\n<LandingPrimaryImageCtaSection\n  title=\"Landing page in minutes\"\n  description=\"Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back.\"\n  imageSrc=\"/static/images/shipixen/product/1.webp\"\n  imageAlt=\"Sample image\"\n  imagePosition=\"center\"\n  textPosition=\"center\"\n>\n  <LandingNewsletterInput className=\"max-w-xs\" />\n\n  <LandingSocialProof\n    className=\"mt-6 w-full flex justify-center\"\n    showRating\n    numberOfUsers={99}\n    avatarItems={[\n      {\n        imageSrc: '/static/images/people/1.webp',\n        name: 'John Doe',\n      },\n      {\n        imageSrc: '/static/images/people/2.webp',\n        name: 'Jane Doe',\n      },\n      {\n        imageSrc: '/static/images/people/3.webp',\n        name: 'Alice Doe',\n      },\n    ]}\n  />\n</LandingPrimaryImageCtaSection>;\n```\n\n\n\n### With Background Effects\n\nMore effects are available in the <a href=\"/boilerplate-documentation/landing-page-components/primary-cta-effects\" className=\"fancy-link\">Primary CTA Background Effects</a> section.\n\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryImageCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\nimport { LandingDotParticleCtaBg } from '@/components/landing/cta-backgrounds/LandingDotParticleCtaBg';\nimport { LandingSocialProof } from '@/components/landing/social-proof/LandingSocialProof';\n\n<LandingPrimaryImageCtaSection\n  title=\"Revolutionary Product Launch\"\n  description=\"Experience the future with our cutting-edge solution. Built for modern teams who demand excellence and reliability.\"\n  imageSrc=\"/static/images/shipixen/product/1.webp\"\n  imageAlt=\"Product showcase\"\n  textPosition=\"center\"\n  imagePosition=\"center\"\n  effectComponent={<LandingDotParticleCtaBg />}\n>\n  <Button size=\"xl\" asChild>\n    <a href=\"#\">Start Free Trial</a>\n  </Button>\n\n  <Button size=\"xl\" variant=\"outlineSecondary\">\n    <a href=\"#\">Watch Demo</a>\n  </Button>\n\n  <LandingSocialProof\n    className=\"mt-8 w-full flex justify-center\"\n    showRating\n    numberOfUsers={1000}\n    avatarItems={[\n      {\n        imageSrc: '/static/images/people/1.webp',\n        name: 'John Doe',\n      },\n      {\n        imageSrc: '/static/images/people/2.webp',\n        name: 'Jane Smith',\n      },\n      {\n        imageSrc: '/static/images/people/3.webp',\n        name: 'Alex Johnson',\n      },\n    ]}\n  />\n</LandingPrimaryImageCtaSection>\n```\n\n\n\n### With Leading Pill\n\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryImageCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\nimport { LandingLeadingPill } from '@/components/landing/leading/LandingLeadingPill';\n\n<LandingPrimaryImageCtaSection\n  title=\"Landing page in minutes\"\n  description=\"Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back.\"\n  imageSrc=\"/static/images/shipixen/product/1.webp\"\n  imageAlt=\"Sample image\"\n  imagePosition=\"center\"\n  leadingComponent={<LandingLeadingPill\n    text=\"Best generator\"\n    borderVariant=\"primary\"\n  />}\n>\n  <Button size=\"xl\" asChild>\n    <a href=\"#\">Buy now</a>\n  </Button>\n\n  <Button size=\"xl\" variant=\"outlinePrimary\">\n    <a href=\"#\">Read more</a>\n  </Button>\n</LandingPrimaryImageCtaSection>;\n```\n\n\n</Examples>\n\n<PropsReference>\n| Prop Name                                                                                                                             | Prop Type                                                                | Required | Default     |\n| ------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------ | -------- | ----------- |\n| **children** <Tippy>React nodes to be rendered within the component (below the text on smaller screen, right side on larger).</Tippy> | `React.ReactNode` ǀ `string`                                             | Yes      | -           |\n| **title** <Tippy>The title to be displayed in the component.</Tippy>                                                                  | `string` ǀ `React.ReactNode`                                             | Yes      | -           |\n| **description** <Tippy>The description to be displayed in the component.</Tippy>                                                      | `string` ǀ `React.ReactNode`                                             | Yes      | -           |\n| **innerClassName** <Tippy>Additional CSS class to be applied to the inner container of the component.</Tippy>                         | `string`                                                                 | No       | -           |\n| **titleComponent** <Tippy>Custom component for rendering the title.</Tippy>                                                           | `React.ReactNode`                                                        | No       | -           |\n| **descriptionComponent** <Tippy>Custom component for rendering the description.</Tippy>                                               | `React.ReactNode`                                                        | No       | -           |\n| **leadingComponent** <Tippy>Custom component to be rendered before the title and description.</Tippy>                                 | `React.ReactNode`                                                        | No       | -           |\n| **footerComponent** <Tippy>Custom component to be rendered as the footer of the section.</Tippy>                                      | `React.ReactNode`                                                        | No       | -           |\n| **textPosition** <Tippy>The position of text content within the component.</Tippy>                                                    | `'center'` ǀ `'left'`                                                    | No       | `'left'`    |\n| **imageSrc** <Tippy>The source URL for the image to be displayed in the component.</Tippy>                                            | `string`                                                                 | No       | -           |\n| **imageAlt** <Tippy>The alt text for the image.</Tippy>                                                                               | `string`                                                                 | No       | `''`        |\n| **imagePosition** <Tippy>The position of the image within the component.</Tippy>                                                      | `'left'` ǀ `'right'` ǀ `'center'`                                        | No       | `'right'`   |\n| **imagePerspective** <Tippy>The perspective effect applied to the image.</Tippy>                                                      | `'none'` ǀ `'left'` ǀ `'right'` ǀ `'bottom'` ǀ `'bottom-lg'` ǀ `'paper'` | No       | `'none'`    |\n| **imageShadow** <Tippy>The type of shadow applied to the image.</Tippy>                                                               | `'none'` ǀ `'soft'` ǀ `'hard'`                                           | No       | `'hard'`    |\n| **minHeight** <Tippy>The minimum height of the section containing the component.</Tippy>                                              | `number`                                                                 | No       | `350`       |\n| **withBackground** <Tippy>Whether to display a background for the component.</Tippy>                                                  | `boolean`                                                                | No       | `false`     |\n| **withBackgroundGlow** <Tippy>Whether to display a glowing background for the component.</Tippy>                                      | `boolean`                                                                | No       | `false`     |\n| **variant** <Tippy>The variant style for the component.</Tippy>                                                                       | `'primary'` ǀ `'secondary'`                                              | No       | `'primary'` |\n| **backgroundGlowVariant** <Tippy>The variant style for the glowing background.</Tippy>                                                | `'primary'` ǀ `'secondary'`                                              | No       | `'primary'` |\n| **effectComponent** <Tippy>Custom React component to be displayed as the background effect.</Tippy>                                    | `React.ReactNode`                                                        | No       | -           |\n| **effectClassName** <Tippy>Additional CSS classes to apply to the background effect.</Tippy>                                        | `string`                                                                 | No       | -           |\n</PropsReference>"}, "PrimaryTextCta": {"description": "Use this component on landing page as the primary Call to Action section. A section that shows a title & description. Optionally, it can have actions (children) and a background. This is the most important section of your landing page. Use it to grab the attention of your visitors and encourage them to take action.", "api": "<Usage>\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryTextCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\n```\n\n```jsx\n<LandingPrimaryTextCtaSection\n  title=\"Landing page in minutes\"\n  description=\"Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back.\"\n>\n  <Button size=\"xl\" asChild>\n    <a href=\"#\">Buy now</a>\n  </Button>\n\n  <Button size=\"xl\" variant=\"outlinePrimary\">\n    <a href=\"#\">Read more</a>\n  </Button>\n</LandingPrimaryTextCtaSection>\n```\n</Usage>\n\n<Examples>\n### With Social Proof\n\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryTextCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\nimport { LandingSocialProof } from '@/components/landing/social-proof/LandingSocialProof';\n\n<LandingPrimaryTextCtaSection\n  title=\"Landing page in minutes\"\n  description=\"Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back.\"\n>\n  <Button size=\"xl\" asChild>\n    <a href=\"#\">Buy now</a>\n  </Button>\n\n  <Button size=\"xl\" variant=\"outlinePrimary\">\n    <a href=\"#\">Read more</a>\n  </Button>\n\n  <LandingSocialProof\n    className=\"mt-6 w-full flex justify-center\"\n    showRating\n    numberOfUsers={99}\n    avatarItems={[\n      {\n        imageSrc: '/static/images/people/1.webp',\n        name: 'John Doe',\n      },\n      {\n        imageSrc: '/static/images/people/2.webp',\n        name: 'Jane Doe',\n      },\n      {\n        imageSrc: '/static/images/people/3.webp',\n        name: 'Alice Doe',\n      },\n    ]}\n  />\n</LandingPrimaryTextCtaSection>;\n```\n\n\n\n### With Discount/Offer\n\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryTextCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\nimport { LandingDiscount } from '@/components/landing/discount/LandingDiscount';\n\n<LandingPrimaryTextCtaSection\n  title=\"Landing page in minutes\"\n  description=\"Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back.\"\n>\n  <Button size=\"xl\" asChild>\n    <a href=\"#\">Buy now</a>\n  </Button>\n\n  <Button size=\"xl\" variant=\"outlinePrimary\">\n    <a href=\"#\">Read more</a>\n  </Button>\n\n  <LandingDiscount\n    className=\"w-full flex justify-center\"\n    discountValueText=\"$350 off\"\n    discountDescriptionText=\"for the first 10 customers (2 left)\"\n  />\n</LandingPrimaryTextCtaSection>;\n```\n\n\n\n### With Discount & Left Alignment\n\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryTextCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\nimport { LandingDiscount } from '@/components/landing/discount/LandingDiscount';\n\n<LandingPrimaryTextCtaSection\n  title=\"Landing page in minutes\"\n  description=\"Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back.\"\n  textPosition=\"left\"\n>\n  <Button size=\"xl\" asChild>\n    <a href=\"#\">Buy now</a>\n  </Button>\n\n  <Button size=\"xl\" variant=\"outlinePrimary\">\n    <a href=\"#\">Read more</a>\n  </Button>\n\n  <LandingDiscount\n    className=\"w-full\"\n    discountValueText=\"$350 off\"\n    discountDescriptionText=\"for the first 10 customers (2 left)\"\n  />\n</LandingPrimaryTextCtaSection>;\n```\n\n\n\n### With Bullet Points\n\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryTextCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\nimport { LandingDiscount } from '@/components/landing/discount/LandingDiscount';\nimport { LandingProductFeatureKeyPoints } from '@/components/landing/LandingProductFeatureKeyPoints';\n\n<LandingPrimaryTextCtaSection\n  title=\"Landing page in minutes\"\n  descriptionComponent={\n    <LandingProductFeatureKeyPoints\n      keyPoints={[\n        {\n          title: 'Intelligent Assistance',\n          description:\n            'Receive personalized recommendations and insights tailored to your workflow.',\n        },\n        {\n          title: 'Seamless Collaboration',\n          description:\n            'Easily collaborate with team members and clients in real-time.',\n        },\n        {\n          title: 'Advanced Customization',\n          description:\n            'Tailor your app to fit your unique requirements with extensive customization.',\n        },\n      ]}\n    />\n  }\n  textPosition=\"left\"\n>\n  <Button size=\"xl\" asChild>\n    <a href=\"#\">Buy now</a>\n  </Button>\n\n  <Button size=\"xl\" variant=\"outlinePrimary\">\n    <a href=\"#\">Read more</a>\n  </Button>\n\n  <LandingDiscount\n    className=\"w-full\"\n    discountValueText=\"$350 off\"\n    discountDescriptionText=\"for the first 10 customers (2 left)\"\n  />\n</LandingPrimaryTextCtaSection>;\n```\n\n\n\n### With Product Hunt Award\n\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryTextCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\nimport { LandingDiscount } from '@/components/landing/discount/LandingDiscount';\nimport { LandingProductHuntAward } from '@/components/landing/social-proof/LandingProductHuntAward';\n\n<LandingPrimaryTextCtaSection\n  title=\"Landing page in minutes\"\n  description=\"Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back.\"\n  textPosition=\"left\"\n  leadingComponent={<LandingProductHuntAward />}\n>\n  <Button size=\"xl\" asChild>\n    <a href=\"#\">Buy now</a>\n  </Button>\n\n  <Button size=\"xl\" variant=\"outlinePrimary\">\n    <a href=\"#\">Read more</a>\n  </Button>\n\n  <LandingDiscount\n    className=\"w-full\"\n    discountValueText=\"$350 off\"\n    discountDescriptionText=\"for the first 10 customers (2 left)\"\n  />\n</LandingPrimaryTextCtaSection>;\n```\n\n\n\n### With Social Proof Band\n\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryTextCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\nimport { LandingDiscount } from '@/components/landing/discount/LandingDiscount';\nimport { LandingProductHuntAward } from '@/components/landing/social-proof/LandingProductHuntAward';\nimport { LandingSocialProofBand } from '@/components/landing/social-proof/LandingSocialProofBand';\nimport { LandingSocialProofBandItem } from '@/components/landing/social-proof/LandingSocialProofBandItem';\n\n<LandingSocialProofBand>\n  <LandingSocialProofBandItem>\n    100% encrypted and secure\n  </LandingSocialProofBandItem>\n\n  <LandingSocialProofBandItem>24/7 customer support</LandingSocialProofBandItem>\n\n  <LandingSocialProofBandItem>\n    99% customer satisfaction\n  </LandingSocialProofBandItem>\n</LandingSocialProofBand>\n\n<LandingPrimaryTextCtaSection\n  title=\"Landing page in minutes\"\n  description=\"Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back.\"\n  textPosition=\"left\"\n  leadingComponent={<LandingProductHuntAward />}\n>\n  <Button size=\"xl\" asChild>\n    <a href=\"#\">\n      Buy now\n    </a>\n  </Button>\n\n  <Button size=\"xl\" variant=\"outlinePrimary\">\n    <a href=\"#\">Read more</a>\n  </Button>\n\n  <LandingDiscount\n    className=\"w-full\"\n    discountValueText=\"$350 off\"\n    discountDescriptionText=\"for the first 10 customers (2 left)\"\n  />\n</LandingPrimaryTextCtaSection>\n```\n\n\n\n### With Background\n\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryTextCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\nimport { LandingDiscount } from '@/components/landing/discount/LandingDiscount';\nimport { LandingProductHuntAward } from '@/components/landing/social-proof/LandingProductHuntAward';\n\n<LandingPrimaryTextCtaSection\n  title=\"Landing page in minutes\"\n  description=\"Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back.\"\n  textPosition=\"left\"\n  withBackground\n  variant=\"secondary\"\n  leadingComponent={<LandingProductHuntAward />}\n>\n  <Button size=\"xl\" variant=\"secondary\" asChild>\n    <a href=\"#\">Buy now</a>\n  </Button>\n\n  <Button size=\"xl\" variant=\"outlineSecondary\">\n    <a href=\"#\">Read more</a>\n  </Button>\n\n  <LandingDiscount\n    className=\"w-full\"\n    discountValueText=\"$350 off\"\n    discountDescriptionText=\"for the first 10 customers (2 left)\"\n  />\n</LandingPrimaryTextCtaSection>;\n```\n\n\n\n### Left-aligned full example\n\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryTextCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\nimport { LandingDiscount } from '@/components/landing/discount/LandingDiscount';\nimport { LandingProductHuntAward } from '@/components/landing/social-proof/LandingProductHuntAward';\nimport { LandingSocialProof } from '@/components/landing/social-proof/LandingSocialProof';\nimport { LandingSocialProofBand } from '@/components/landing/social-proof/LandingSocialProofBand';\nimport { LandingSocialProofBandItem } from '@/components/landing/social-proof/LandingSocialProofBandItem';\n\n<LandingSocialProofBand>\n  <LandingSocialProofBandItem>\n    100% encrypted and secure\n  </LandingSocialProofBandItem>\n\n  <LandingSocialProofBandItem>24/7 customer support</LandingSocialProofBandItem>\n\n  <LandingSocialProofBandItem>\n    99% customer satisfaction\n  </LandingSocialProofBandItem>\n</LandingSocialProofBand>\n\n<LandingPrimaryTextCtaSection\n  title=\"Landing page in minutes\"\n  description=\"Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back.\"\n  textPosition=\"left\"\n  withBackground\n  withBackgroundGlow\n  leadingComponent={<LandingProductHuntAward />}\n>\n  <Button size=\"xl\" asChild>\n    <a href=\"#\">\n      Buy now\n    </a>\n  </Button>\n\n  <Button size=\"xl\" variant=\"outlinePrimary\">\n    <a href=\"#\">Read more</a>\n  </Button>\n\n  <LandingSocialProof\n    className=\"w-full mt-12\"\n    showRating\n    numberOfUsers={99}\n    avatarItems={[\n      {\n        imageSrc: '/static/images/people/1.webp',\n        name: 'John Doe',\n      },\n      {\n        imageSrc: '/static/images/people/2.webp',\n        name: 'Jane Doe',\n      },\n      {\n        imageSrc: '/static/images/people/3.webp',\n        name: 'Alice Doe',\n      },\n    ]}\n  />\n</LandingPrimaryTextCtaSection>\n```\n\n\n\n### Centered full example\n\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryTextCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\nimport { LandingDiscount } from '@/components/landing/discount/LandingDiscount';\nimport { LandingProductHuntAward } from '@/components/landing/social-proof/LandingProductHuntAward';\nimport { LandingSocialProof } from '@/components/landing/social-proof/LandingSocialProof';\nimport { LandingSocialProofBand } from '@/components/landing/social-proof/LandingSocialProofBand';\nimport { LandingSocialProofBandItem } from '@/components/landing/social-proof/LandingSocialProofBandItem';\n\n<LandingSocialProofBand invert>\n  <LandingSocialProofBandItem>\n    100% encrypted and secure\n  </LandingSocialProofBandItem>\n\n  <LandingSocialProofBandItem>24/7 customer support</LandingSocialProofBandItem>\n\n  <LandingSocialProofBandItem>\n    99% customer satisfaction\n  </LandingSocialProofBandItem>\n</LandingSocialProofBand>\n\n<LandingPrimaryTextCtaSection\n  title=\"Landing page in minutes\"\n  description=\"Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back.\"\n  withBackground\n  withBackgroundGlow\n  variant=\"secondary\"\n  backgroundGlowVariant=\"secondary\"\n  leadingComponent={<LandingProductHuntAward />}\n>\n  <Button size=\"xl\" variant=\"secondary\" asChild>\n    <a href=\"#\">\n      Buy now\n    </a>\n  </Button>\n\n  <Button size=\"xl\" variant=\"outlineSecondary\">\n    <a href=\"#\">Read more</a>\n  </Button>\n\n  <LandingDiscount\n    className=\"w-full flex justify-center\"\n    discountValueText=\"$350 off\"\n    discountDescriptionText=\"for the first 10 customers (2 left)\"\n  />\n\n  <LandingSocialProof\n    className=\"mt-12 w-full flex justify-center\"\n    showRating\n    numberOfUsers={99}\n    avatarItems={[\n      {\n        imageSrc: '/static/images/people/1.webp',\n        name: 'John Doe',\n      },\n      {\n        imageSrc: '/static/images/people/2.webp',\n        name: 'Jane Doe',\n      },\n      {\n        imageSrc: '/static/images/people/3.webp',\n        name: 'Alice Doe',\n      },\n    ]}\n  />\n</LandingPrimaryTextCtaSection>\n```\n\n\n\n### With Newsletter Form\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryTextCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\nimport { LandingNewsletterInput } from '@/components/landing/newsletter/LandingNewsletterInput';\n\n<LandingPrimaryTextCtaSection\n  title=\"Sign up today\"\n  description=\"Never miss an update! Subscribe to our newsletter to get the latest announcements, news and exclusive offers.\"\n>\n  <LandingNewsletterInput />\n</LandingPrimaryTextCtaSection>\n```\n\n\n\n### With Background Effects\n\nMore effects are available in the <a href=\"/boilerplate-documentation/landing-page-components/primary-cta-effects\" className=\"fancy-link\">Primary CTA Background Effects</a> section.\n\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryTextCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\nimport { LandingDotParticleCtaBg } from '@/components/landing/cta-backgrounds/LandingDotParticleCtaBg';\nimport { LandingSocialProof } from '@/components/landing/social-proof/LandingSocialProof';\n\n<LandingPrimaryTextCtaSection\n  title=\"Revolutionary Product Launch\"\n  description=\"Experience the future with our cutting-edge solution. Built for modern teams who demand excellence and reliability.\"\n  textPosition=\"center\"\n  effectComponent={<LandingDotParticleCtaBg />}\n>\n  <Button size=\"xl\" asChild>\n    <a href=\"#\">Start Free Trial</a>\n  </Button>\n\n  <Button size=\"xl\" variant=\"outlineSecondary\">\n    <a href=\"#\">Watch Demo</a>\n  </Button>\n\n  <LandingSocialProof\n    className=\"mt-8 w-full flex justify-center\"\n    showRating\n    numberOfUsers={1000}\n    avatarItems={[\n      {\n        imageSrc: '/static/images/people/1.webp',\n        name: 'John Doe',\n      },\n      {\n        imageSrc: '/static/images/people/2.webp',\n        name: 'Jane Smith',\n      },\n      {\n        imageSrc: '/static/images/people/3.webp',\n        name: 'Alex Johnson',\n      },\n    ]}\n  />\n</LandingPrimaryTextCtaSection>\n```\n\n\n</Examples>\n\n<PropsReference>\n| Prop Name                                                                                                                             | Prop Type                    | Required | Default     |\n| ------------------------------------------------------------------------------------------------------------------------------------- | ---------------------------- | -------- | ----------- |\n| **children** <Tippy>React nodes to be rendered within the component (below the text on smaller screen, right side on larger).</Tippy> | `React.ReactNode` ǀ `string` | No       | -           |\n| **className** <Tippy>Additional CSS classes to apply to the component.</Tippy>                                                        | `string`                     | No       | -           |\n| **innerClassName** <Tippy>Additional CSS classes to apply to the inner container of the component.</Tippy>                            | `string`                     | No       | -           |\n| **title** <Tippy>The title text or element to be displayed.</Tippy>                                                                   | `string` ǀ `React.ReactNode` | No       | -           |\n| **titleComponent** <Tippy>Custom React component to be used as the title.</Tippy>                                                     | `React.ReactNode`            | No       | -           |\n| **description** <Tippy>The description text or element to be displayed.</Tippy>                                                       | `string` ǀ `React.ReactNode` | No       | -           |\n| **descriptionComponent** <Tippy>Custom React component to be used as the description.</Tippy>                                         | `React.ReactNode`            | No       | -           |\n| **leadingComponent** <Tippy>Custom React component to be displayed before the title and description.</Tippy>                          | `React.ReactNode`            | No       | -           |\n| **footerComponent** <Tippy>Custom React component to be displayed as the footer of the section.</Tippy>                               | `React.ReactNode`            | No       | -           |\n| **textPosition** <Tippy>The position of the text content within the section. Possible values: `'center'`, `'left'`.</Tippy>           | `'center'` ǀ `'left'`        | No       | `'center'`  |\n| **withBackground** <Tippy>Determines whether to show the section with a background or not.</Tippy>                                    | `boolean`                    | No       | `false`     |\n| **variant** <Tippy>The variant style of the section. Possible values: `'primary'`, `'secondary'`.</Tippy>                             | `'primary'` ǀ `'secondary'`  | No       | `'primary'` |\n| **effectComponent** <Tippy>Custom React component to be displayed as the background effect.</Tippy>                                    | `React.ReactNode`            | No       | -           |\n| **effectClassName** <Tippy>Additional CSS classes to apply to the background effect.</Tippy>                                        | `string`                     | No       | -           |\n</PropsReference>"}, "PrimaryVideoCta": {"description": "A high-impact component designed for landing pages that combines a compelling video with a call-to-action. This section helps draw attention to key messages while using video content to enhance engagement and demonstrate features or products.", "api": "<Usage>\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryVideoCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\n```\n\n```jsx\n<LandingPrimaryVideoCtaSection\n  title=\"Your Headline Here\"\n  description=\"Your description text goes here.\"\n  videoSrc=\"path/to/your/video.mp4\"\n  videoPoster=\"path/to/your/poster-image.jpg\"\n>\n  <Button size=\"xl\" asChild>\n    <a href=\"#\">Get Started</a>\n  </Button>\n</LandingPrimaryVideoCtaSection>\n```\n</Usage>\n\n<Examples>\n### Centered Text Layout\n\nWhen you need to emphasize both text and video equally, use the centered text layout with the video positioned below.\n\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryVideoCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\n\n<LandingPrimaryVideoCtaSection\n  title=\"Streamline Your Workflow\"\n  description=\"Our intuitive tools help teams work more efficiently with less effort.\"\n  videoSrc=\"https://cache.shipixen.com/features/8-customize-pages.mp4\"\n  textPosition=\"center\"\n  videoPosition=\"center\"\n  withBackground={true}\n  autoPlay={true}\n  loop={true}\n  muted={true}\n>\n  <Button size=\"xl\" asChild>\n    <a href=\"#\">Try It Free</a>\n  </Button>\n  <Button size=\"xl\" variant=\"outlinePrimary\">\n    <a href=\"#\">Learn More</a>\n  </Button>\n</LandingPrimaryVideoCtaSection>\n```\n\n\n\n### With Video Controls\n\nDisplay video controls to give users more control over the playback experience.\n\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryVideoCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\n\n<LandingPrimaryVideoCtaSection\n  title=\"See How It Works\"\n  description=\"Watch our detailed tutorial showing the key features in action.\"\n  videoSrc=\"https://cache.shipixen.com/features/8-customize-pages.mp4\"\n  videoPoster=\"/static/images/backdrop-3.webp\"\n  videoPosition=\"right\"\n  controls={true}\n  autoPlay={false}\n  muted={true}\n>\n  <Button size=\"xl\" asChild>\n    <a href=\"#\">Start Now</a>\n  </Button>\n  <Button size=\"xl\" variant=\"outlinePrimary\">\n    <a href=\"#\">Contact Sales</a>\n  </Button>\n</LandingPrimaryVideoCtaSection>\n```\n\n\n\n### With Background Glow\n\nAdd a subtle background glow to enhance the visual appeal and draw attention to your content.\n\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryVideoCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\n\n<LandingPrimaryVideoCtaSection\n  title=\"Powerful Visual Storytelling\"\n  description=\"Engage your audience with stunning visuals and compelling narratives.\"\n  videoSrc=\"https://cache.shipixen.com/features/8-customize-pages.mp4\"\n  videoPosition=\"left\"\n  withBackgroundGlow={true}\n  backgroundGlowVariant=\"secondary\"\n  autoPlay={true}\n  loop={true}\n  muted={true}\n>\n  <Button size=\"xl\" variant=\"secondary\" asChild>\n    <a href=\"#\">Get Started</a>\n  </Button>\n  <Button size=\"xl\" variant=\"outlineSecondary\">\n    <a href=\"#\">View Demo</a>\n  </Button>\n</LandingPrimaryVideoCtaSection>\n```\n\n\n\n### With Social Proof\n\nAdd social proof to increase credibility and show popularity of your product.\n\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryVideoCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\nimport { LandingSocialProof } from '@/components/landing/social-proof/LandingSocialProof';\n\n<LandingPrimaryVideoCtaSection\n  title=\"Trusted by Thousands\"\n  description=\"Join the community of professionals leveraging our platform for increased productivity.\"\n  videoSrc=\"https://cache.shipixen.com/features/8-customize-pages.mp4\"\n  muted={true}\n  autoPlay={true}\n  loop={true}\n>\n  <Button size=\"xl\" asChild>\n    <a href=\"#\">Start Free Trial</a>\n  </Button>\n  <Button size=\"xl\" variant=\"outlinePrimary\">\n    <a href=\"#\">Learn More</a>\n  </Button>\n  <LandingSocialProof\n    className=\"mt-6 w-full\"\n    showRating\n    numberOfUsers={99}\n    avatarItems={[\n      {\n        imageSrc: '/static/images/people/1.webp',\n        name: 'John Doe',\n      },\n      {\n        imageSrc: '/static/images/people/2.webp',\n        name: 'Jane Doe',\n      },\n      {\n        imageSrc: '/static/images/people/3.webp',\n        name: 'Alice Doe',\n      },\n    ]}\n  />\n</LandingPrimaryVideoCtaSection>\n```\n\n\n\n### With Discount/Offer\n\nHighlight special offers or discounts to encourage conversion.\n\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryVideoCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\nimport { LandingDiscount } from '@/components/landing/discount/LandingDiscount';\n\n<LandingPrimaryVideoCtaSection\n  title=\"Limited Time Offer\"\n  description=\"Get exclusive access to premium features and save with our special pricing.\"\n  videoSrc=\"https://cache.shipixen.com/features/8-customize-pages.mp4\"\n  textPosition=\"center\"\n  videoPosition=\"center\"\n  autoPlay={true}\n  loop={true}\n  muted={true}\n>\n  <Button size=\"xl\" asChild>\n    <a href=\"#\">Claim Discount</a>\n  </Button>\n  <Button size=\"xl\" variant=\"outlinePrimary\">\n    <a href=\"#\">Learn More</a>\n  </Button>\n  <LandingDiscount\n    className=\"w-full flex justify-center\"\n    discountValueText=\"$350 off\"\n    discountDescriptionText=\"for the first 10 customers (2 left)\"\n  />\n</LandingPrimaryVideoCtaSection>\n```\n\n\n\n### With Discount and Left Alignment\n\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryVideoCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\nimport { LandingDiscount } from '@/components/landing/discount/LandingDiscount';\n\n<LandingPrimaryVideoCtaSection\n  title=\"Early Bird Pricing\"\n  description=\"Sign up now and save with our early adopter discount program.\"\n  videoSrc=\"https://cache.shipixen.com/features/8-customize-pages.mp4\"\n  autoPlay={true}\n  loop={true}\n  muted={true}\n>\n  <Button size=\"xl\" asChild>\n    <a href=\"#\">Get Started</a>\n  </Button>\n  <Button size=\"xl\" variant=\"outlinePrimary\">\n    <a href=\"#\">View Plans</a>\n  </Button>\n  <LandingDiscount\n    className=\"w-full\"\n    discountValueText=\"$350 off\"\n    discountDescriptionText=\"for the first 10 customers (2 left)\"\n  />\n</LandingPrimaryVideoCtaSection>\n```\n\n\n\n### With Bullet Points\n\nUse bullet points to highlight key features or benefits of your product.\n\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryVideoCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\nimport { LandingProductFeatureKeyPoints } from '@/components/landing/LandingProductFeatureKeyPoints';\n\n<LandingPrimaryVideoCtaSection\n  title=\"Why Choose Our Platform\"\n  descriptionComponent={\n    <LandingProductFeatureKeyPoints\n      keyPoints={[\n        {\n          title: 'Intelligent Assistance',\n          description:\n            'Receive personalized recommendations and insights tailored to your workflow.',\n        },\n        {\n          title: 'Seamless Collaboration',\n          description:\n            'Easily collaborate with team members and clients in real-time.',\n        },\n        {\n          title: 'Advanced Customization',\n          description:\n            'Tailor your app to fit your unique requirements with extensive customization options.',\n        },\n      ]}\n    />\n  }\n  videoSrc=\"https://cache.shipixen.com/features/8-customize-pages.mp4\"\n  autoPlay={true}\n  loop={true}\n  muted={true}\n>\n  <Button size=\"xl\" asChild>\n    <a href=\"#\">Get Started</a>\n  </Button>\n  <Button size=\"xl\" variant=\"outlinePrimary\">\n    <a href=\"#\">Learn More</a>\n  </Button>\n</LandingPrimaryVideoCtaSection>\n```\n\n\n\n### With Product Hunt Award\n\nShowcase awards and recognition to build credibility.\n\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryVideoCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\nimport { LandingProductHuntAward } from '@/components/landing/social-proof/LandingProductHuntAward';\n\n<LandingPrimaryVideoCtaSection\n  title=\"Award-Winning Platform\"\n  description=\"Join thousands of satisfied users who rely on our platform daily.\"\n  videoSrc=\"https://cache.shipixen.com/features/8-customize-pages.mp4\"\n  leadingComponent={<LandingProductHuntAward />}\n  autoPlay={true}\n  loop={true}\n  muted={true}\n>\n  <Button size=\"xl\" asChild>\n    <a href=\"#\">Get Started</a>\n  </Button>\n  <Button size=\"xl\" variant=\"outlinePrimary\">\n    <a href=\"#\">Learn More</a>\n  </Button>\n</LandingPrimaryVideoCtaSection>\n```\n\n\n\n### With Social Proof Band\n\nAdd a social proof band to showcase key benefits or testimonials.\n\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryVideoCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\nimport { LandingSocialProofBand } from '@/components/landing/social-proof/LandingSocialProofBand';\nimport { LandingSocialProofBandItem } from '@/components/landing/social-proof/LandingSocialProofBandItem';\n\n<>\n  <LandingSocialProofBand>\n    <LandingSocialProofBandItem>\n      100% encrypted and secure\n    </LandingSocialProofBandItem>\n    <LandingSocialProofBandItem>24/7 customer support</LandingSocialProofBandItem>\n    <LandingSocialProofBandItem>\n      99% customer satisfaction\n    </LandingSocialProofBandItem>\n  </LandingSocialProofBand>\n  <LandingPrimaryVideoCtaSection\n    title=\"Enterprise-Grade Solution\"\n    description=\"Powerful tools designed for businesses of all sizes, with the security and reliability you need.\"\n    videoSrc=\"https://cache.shipixen.com/features/8-customize-pages.mp4\"\n    autoPlay={true}\n    loop={true}\n    muted={true}\n  >\n    <Button size=\"xl\" asChild>\n      <a href=\"#\">Schedule Demo</a>\n    </Button>\n    <Button size=\"xl\" variant=\"outlinePrimary\">\n      <a href=\"#\">Contact Sales</a>\n    </Button>\n  </LandingPrimaryVideoCtaSection>\n</>\n```\n\n\n\n### Full Example with Combined Elements\n\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryVideoCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\nimport { LandingDiscount } from '@/components/landing/discount/LandingDiscount';\nimport { LandingProductHuntAward } from '@/components/landing/social-proof/LandingProductHuntAward';\nimport { LandingSocialProof } from '@/components/landing/social-proof/LandingSocialProof';\nimport { LandingSocialProofBand } from '@/components/landing/social-proof/LandingSocialProofBand';\nimport { LandingSocialProofBandItem } from '@/components/landing/social-proof/LandingSocialProofBandItem';\n\n<>\n  <LandingSocialProofBand invert>\n    <LandingSocialProofBandItem>\n      100% encrypted and secure\n    </LandingSocialProofBandItem>\n    <LandingSocialProofBandItem>24/7 customer support</LandingSocialProofBandItem>\n    <LandingSocialProofBandItem>\n      99% customer satisfaction\n    </LandingSocialProofBandItem>\n  </LandingSocialProofBand>\n  <LandingPrimaryVideoCtaSection\n    title=\"Transform Your Business\"\n    description=\"Get everything you need to grow your business with our comprehensive platform.\"\n    videoSrc=\"https://cache.shipixen.com/features/8-customize-pages.mp4\"\n    textPosition=\"center\"\n    videoPosition=\"center\"\n    withBackground={true}\n    withBackgroundGlow={true}\n    variant=\"secondary\"\n    leadingComponent={<LandingProductHuntAward />}\n    autoPlay={true}\n    loop={true}\n    muted={true}\n  >\n    <Button size=\"xl\" variant=\"secondary\" asChild>\n      <a href=\"#\">Get Started</a>\n    </Button>\n    <Button size=\"xl\" variant=\"outlineSecondary\">\n      <a href=\"#\">View Pricing</a>\n    </Button>\n    <LandingDiscount\n      className=\"w-full flex justify-center\"\n      discountValueText=\"$350 off\"\n      discountDescriptionText=\"for the first 10 customers (2 left)\"\n    />\n    <LandingSocialProof\n      className=\"mt-12 w-full flex justify-center\"\n      showRating\n      numberOfUsers={99}\n      avatarItems={[\n        {\n          imageSrc: '/static/images/people/1.webp',\n          name: 'John Doe',\n        },\n        {\n          imageSrc: '/static/images/people/2.webp',\n          name: 'Jane Doe',\n        },\n        {\n          imageSrc: '/static/images/people/3.webp',\n          name: 'Alice Doe',\n        },\n      ]}\n    />\n  </LandingPrimaryVideoCtaSection>\n</>\n```\n\n\n\n### With Background Effects\n\nMore effects are available in the <a href=\"/boilerplate-documentation/landing-page-components/primary-cta-effects\" className=\"fancy-link\">Primary CTA Background Effects</a> section.\n\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryVideoCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\nimport { LandingDotParticleCtaBg } from '@/components/landing/cta-backgrounds/LandingDotParticleCtaBg';\nimport { LandingSocialProof } from '@/components/landing/social-proof/LandingSocialProof';\n\n<LandingPrimaryVideoCtaSection\n  title=\"Revolutionary Product Launch\"\n  description=\"Experience the future with our cutting-edge solution. Built for modern teams who demand excellence and reliability.\"\n  textPosition=\"center\"\n  videoSrc=\"https://cache.shipixen.com/features/8-customize-pages.mp4\"\n  videoPoster=\"/static/images/shipixen/product/1.webp\"\n  videoPosition=\"center\"\n  effectComponent={<LandingDotParticleCtaBg />}\n>\n  <Button size=\"xl\" asChild>\n    <a href=\"#\">Start Free Trial</a>\n  </Button>\n\n  <Button size=\"xl\" variant=\"outlineSecondary\">\n    <a href=\"#\">Watch Demo</a>\n  </Button>\n\n  <LandingSocialProof\n    className=\"mt-8 w-full flex justify-center\"\n    showRating\n    numberOfUsers={1000}\n    avatarItems={[\n      {\n        imageSrc: '/static/images/people/1.webp',\n        name: 'John Doe',\n      },\n      {\n        imageSrc: '/static/images/people/2.webp',\n        name: 'Jane Smith',\n      },\n      {\n        imageSrc: '/static/images/people/3.webp',\n        name: 'Alex Johnson',\n      },\n    ]}\n  />\n</LandingPrimaryVideoCtaSection>\n```\n\n\n\n\n### With Leading Pill\n\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryVideoCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\nimport { LandingLeadingPill } from '@/components/landing/leading/LandingLeadingPill';\n\n<LandingPrimaryVideoCtaSection\n  title=\"Landing page in minutes\"\n  description=\"Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back.\"\n  imageSrc=\"/static/images/shipixen/product/1.webp\"\n  imageAlt=\"Sample image\"\n  imagePosition=\"center\"\n  leadingComponent={<LandingLeadingPill\n    text=\"Best generator\"\n    borderVariant=\"primary\"\n  />}\n>\n  <Button size=\"xl\" asChild>\n    <a href=\"#\">Buy now</a>\n  </Button>\n\n  <Button size=\"xl\" variant=\"outlinePrimary\">\n    <a href=\"#\">Read more</a>\n  </Button>\n</LandingPrimaryVideoCtaSection>;\n```\n\n\n</Examples>\n\n<PropsReference>\n| Prop Name | Prop Type | Required | Default |\n| --- | --- | ----- | ---- |\n| **title** <Tippy>The main headline for the CTA section</Tippy> | `string \\| React.ReactNode` | No | - |\n| **titleComponent** <Tippy>Custom component for the title if more control is needed</Tippy> | `React.ReactNode` | No | - |\n| **description** <Tippy>Supporting text that explains the value proposition</Tippy> | `string \\| React.ReactNode` | No | - |\n| **descriptionComponent** <Tippy>Custom component for the description</Tippy> | `React.ReactNode` | No | - |\n| **videoSrc** <Tippy>URL to the video file</Tippy> | `string` | No | - |\n| **videoPoster** <Tippy>URL to the poster image shown before video plays</Tippy> | `string` | No | - |\n| **videoPosition** <Tippy>Position of the video relative to text content</Tippy> | `'left' \\| 'right' \\| 'center'` | No | `'right'` |\n| **videoMaxWidth** <Tippy>Maximum width constraint for the video</Tippy> | `string` | No | `'none'` |\n| **videoShadow** <Tippy>Shadow style for the video container</Tippy> | `'none' \\| 'soft' \\| 'hard'` | No | `'hard'` |\n| **textPosition** <Tippy>Text alignment within the section</Tippy> | `'center' \\| 'left'` | No | `'left'` |\n| **muted** <Tippy>Whether the video should be muted</Tippy> | `boolean` | No | `true` |\n| **autoPlay** <Tippy>Whether the video should auto-play</Tippy> | `boolean` | No | `false` |\n| **controls** <Tippy>Whether to show video controls</Tippy> | `boolean` | No | `false` |\n| **loop** <Tippy>Whether the video should loop</Tippy> | `boolean` | No | `false` |\n| **minHeight** <Tippy>Minimum height of the section</Tippy> | `number` | No | `350` |\n| **withBackground** <Tippy>Whether to show a background color</Tippy> | `boolean` | No | `false` |\n| **withBackgroundGlow** <Tippy>Whether to add a glowing background effect</Tippy> | `boolean` | No | `false` |\n| **variant** <Tippy>Color variant for the component</Tippy> | `'primary' \\| 'secondary'` | No | `'primary'` |\n| **backgroundGlowVariant** <Tippy>Color variant for the background glow</Tippy> | `'primary' \\| 'secondary'` | No | `'primary'` |\n| **className** <Tippy>Additional CSS classes for the outer container</Tippy> | `string` | No | - |\n| **innerClassName** <Tippy>Additional CSS classes for the inner container</Tippy> | `string` | No | - |\n| **leadingComponent** <Tippy>Component to display above the title</Tippy> | `React.ReactNode` | No | - |\n| **footerComponent** <Tippy>Component to display at the bottom of the section</Tippy> | `React.ReactNode` | No | - |\n| **children** <Tippy>Call-to-action elements like buttons</Tippy> | `React.ReactNode` | No | - |\n| **effectComponent** <Tippy>Component to display as a background effect</Tippy> | `React.ReactNode` | No | - |\n| **effectClassName** <Tippy>Additional CSS classes for the effect component</Tippy> | `string` | No | - |\n</PropsReference>"}, "ProblemAgitator": {"description": "The Problem Agitator component creates a visually engaging layout for highlighting common pain points or problems your users face. It arranges 3-4 problem statements in a grid pattern connected by directional arrows, creating a flow that leads to a cliffhanger or solution teaser.", "api": "<Usage>\nImport the components from their respective paths:\n\n```jsx\nimport { LandingProblemAgitator } from '@/components/landing/problem-agitator/LandingProblemAgitator';\nimport { LandingProblemAgitatorItem } from '@/components/landing/problem-agitator/LandingProblemAgitatorItem';\nimport { LandingProblemAgitatorComment } from '@/components/landing/problem-agitator/LandingProblemAgitatorComment';\n```\n\nBasic implementation:\n\n```jsx\n<LandingProblemAgitator\n  title=\"Common User Challenges\"\n  description=\"These are the problems our users face every day\"\n  cliffhanger=\"Our solution makes these problems disappear\"\n>\n  <LandingProblemAgitatorItem>\n    <p>Problem 1</p>\n  </LandingProblemAgitatorItem>\n\n  <LandingProblemAgitatorItem>\n    <p>Problem 2</p>\n    <LandingProblemAgitatorComment className=\"-right-8 top-0\">\n      Pain point 1\n    </LandingProblemAgitatorComment>\n  </LandingProblemAgitatorItem>\n\n  <LandingProblemAgitatorItem>\n    <p>Problem 3</p>\n  </LandingProblemAgitatorItem>\n</LandingProblemAgitator>\n```\n</Usage>\n\n<Examples>\n### Four-Item Grid with Custom Container\n\n\n\n```jsx\n<LandingProblemAgitator\n  titleComponent={\n    <h2 className=\"text-3xl font-bold text-gradient-primary\">\n      Common Website Development Challenges\n    </h2>\n  }\n  containerType=\"narrow\"\n  withBackground={false}\n>\n  <LandingProblemAgitatorItem>\n    <p>Unclear requirements</p>\n  </LandingProblemAgitatorItem>\n\n  <LandingProblemAgitatorItem>\n    <p>Lack of expertise</p>\n  </LandingProblemAgitatorItem>\n\n  <LandingProblemAgitatorItem>\n    <p>Budget constraints</p>\n  </LandingProblemAgitatorItem>\n\n  <LandingProblemAgitatorItem>\n    <p>Time pressure</p>\n  </LandingProblemAgitatorItem>\n</LandingProblemAgitator>\n```\n\n\n\n### Left-aligned Text with Secondary Color Scheme\n\n\n\n```jsx\n<LandingProblemAgitator\n  title=\"Building websites is challenging\"\n  description=\"Traditional approaches leave teams frustrated and projects delayed\"\n  cliffhanger=\"There's a better approach\"\n  textPosition=\"left\"\n  variant=\"secondary\"\n  withBackground\n>\n  <LandingProblemAgitatorItem>\n    <p>Complex requirements</p>\n  </LandingProblemAgitatorItem>\n\n  <LandingProblemAgitatorItem>\n    <p>Limited resources</p>\n    <LandingProblemAgitatorComment className=\"-right-9 top-1 rotate-6\">\n      tight budget\n    </LandingProblemAgitatorComment>\n  </LandingProblemAgitatorItem>\n\n  <LandingProblemAgitatorItem>\n    <p>Technical constraints</p>\n  </LandingProblemAgitatorItem>\n</LandingProblemAgitator>\n```\n\n\n\n### With Background Glow\n\n\n\n```jsx\n<LandingProblemAgitator\n  title=\"Building websites is challenging\"\n  description=\"Traditional approaches leave teams frustrated and projects delayed\"\n  cliffhanger=\"There's a better approach\"\n  withBackgroundGlow\n  withBackground={false}\n>\n  <LandingProblemAgitatorItem>\n    <p>Complex requirements</p>\n  </LandingProblemAgitatorItem>\n\n  <LandingProblemAgitatorItem>\n    <p>Limited resources</p>\n  </LandingProblemAgitatorItem>\n\n  <LandingProblemAgitatorItem>\n    <p>Technical constraints</p>\n  </LandingProblemAgitatorItem>\n</LandingProblemAgitator>\n```\n\n\n</Examples>\n\n<PropsReference>\n### LandingProblemAgitator\n\n| Prop Name | Prop Type | Required | Default |\n| --- | --- | ----- | ---- |\n| **className** <Tippy>Additional CSS classes to apply to the section</Tippy> | `string` | No | `-` |\n| **children** <Tippy>LandingProblemAgitatorItem components (3 or 4 recommended)</Tippy> | `React.ReactNode` | Yes | - |\n| **title** <Tippy>Main heading text for the section</Tippy> | `string \\| React.ReactNode` | No | `-` |\n| **titleComponent** <Tippy>Custom React component for the title (used instead of title prop)</Tippy> | `React.ReactNode` | No | `-` |\n| **description** <Tippy>Descriptive text displayed below the title</Tippy> | `string \\| React.ReactNode` | No | `-` |\n| **descriptionComponent** <Tippy>Custom React component for the description (used instead of description prop)</Tippy> | `React.ReactNode` | No | `-` |\n| **cliffhanger** <Tippy>Conclusion text displayed after the problem items</Tippy> | `string \\| React.ReactNode` | No | `-` |\n| **cliffhangerComponent** <Tippy>Custom React component for the cliffhanger (used instead of cliffhanger prop)</Tippy> | `React.ReactNode` | No | `-` |\n| **textPosition** <Tippy>Alignment of text within the component</Tippy> | `'center' \\| 'left'` | No | `'center'` |\n| **withBackground** <Tippy>Whether to display a background color</Tippy> | `boolean` | No | `true` |\n| **withBackgroundGlow** <Tippy>Whether to include a glow effect in the background</Tippy> | `boolean` | No | `false` |\n| **variant** <Tippy>Color scheme variant</Tippy> | `'primary' \\| 'secondary'` | No | `'primary'` |\n| **backgroundGlowVariant** <Tippy>Color scheme for the background glow effect</Tippy> | `'primary' \\| 'secondary'` | No | `'primary'` |\n| **containerType** <Tippy>Width constraint for the container</Tippy> | `'narrow' \\| 'wide' \\| 'ultrawide'` | No | `'ultrawide'` |\n\n### LandingProblemAgitatorItem\n\n| Prop Name | Prop Type | Required | Default |\n| --- | --- | ----- | ---- |\n| **className** <Tippy>Additional CSS classes to apply to the item</Tippy> | `string` | No | `-` |\n| **children** <Tippy>Content of the problem item (text and optional LandingProblemAgitatorComment components)</Tippy> | `React.ReactNode` | Yes | - |\n\n### LandingProblemAgitatorComment\n\n| Prop Name | Prop Type | Required | Default |\n| --- | --- | ----- | ---- |\n| **className** <Tippy>Additional CSS classes to apply to the comment (use for positioning)</Tippy> | `string` | No | `-` |\n| **children** <Tippy>Text content of the comment</Tippy> | `React.ReactNode` | Yes | - |\n</PropsReference>"}, "ProblemSolution": {"description": "The Problem / Solution component presents a clear comparison between problems and their solutions. This pattern is effective for highlighting how your product addresses specific pain points and provides value to users.", "api": "<Usage>\nImport the component:\n\n```jsx\nimport { LandingProductProblemSolution } from '@/components/landing/LandingProductProblemSolution';\n```\n\nBasic implementation:\n\n```jsx\n<LandingProductProblemSolution\n  title=\"Problems and Solutions\"\n  description=\"Addressing key customer pain points with innovative solutions\"\n  problems={[\n    { title: 'Problem title' },\n  ]}\n  solutions={[\n    { title: 'Solution title' },\n  ]}\n/>\n```\n</Usage>\n\n<Examples>\n### No titles\n\n\n\n```jsx\nimport { LandingProductProblemSolution } from '@/components/landing/LandingProductProblemSolution';\n<LandingProductProblemSolution\n  solutionTitle=''\n  problemTitle=''\n  problems={[\n    { title: \"Too many steps\" },\n    { title: \"Too many clicks\" },\n  ]}\n  solutions={[\n    { title: \"One-click installation\" },\n    { title: \"Optimized performance\" },\n  ]}\n/>\n```\n\n\n\n### With Left Alignment\n\n\n\n```jsx\nimport { LandingProductProblemSolution } from '@/components/landing/LandingProductProblemSolution';\n\n<LandingProductProblemSolution\n  title=\"Problems and Solutions\"\n  description=\"Addressing key customer pain points with innovative solutions\"\n  textPosition=\"left\"\n  problems={[\n    { title: 'Complex setup process' },\n    { title: 'Poor performance' }\n  ]}\n  solutions={[\n    { title: 'One-click installation' },\n    { title: 'Optimized performance' }\n  ]}\n/>\n```\n\n\n\n### Custom Titles\n\n\n\n```jsx\nimport { LandingProductProblemSolution } from '@/components/landing/LandingProductProblemSolution';\n\n<LandingProductProblemSolution\n  title=\"Before vs After\"\n  description=\"See the difference our product makes\"\n  problemTitle=\"Before\"\n  solutionTitle=\"After\"\n  problems={[\n    { title: 'Manual process' },\n  ]}\n  solutions={[\n    { title: 'Automated workflow' },\n  ]}\n/>\n```\n\n\n\n### With descriptions\n\n\n\n```jsx\nimport { LandingProductProblemSolution } from '@/components/landing/LandingProductProblemSolution';\n\n<LandingProductProblemSolution\n  title=\"Before vs After\"\n  description=\"See the difference our product makes\"\n  problemTitle=\"Before\"\n  solutionTitle=\"After\"\n  problems={[\n    { title: 'Manual process', description: 'Time-consuming and error-prone' },\n    { title: 'Manual process', description: 'Time-consuming and error-prone' },\n  ]}\n  solutions={[\n    { title: 'Automated workflow', description: 'Fast, accurate, and reliable' },\n    { title: 'Automated workflow', description: 'Fast, accurate, and reliable' },\n  ]}\n/>\n\n```\n\n\n\n### With Background\n\n\n\n```jsx\nimport { LandingProductProblemSolution } from '@/components/landing/LandingProductProblemSolution';\n\n<LandingProductProblemSolution\n  title=\"Problems and Solutions\"\n  description=\"Addressing key customer pain points with innovative solutions\"\n  withBackground={true}\n  problems={[\n    { title: 'Complex setup process' },\n    { title: 'Poor performance' }\n  ]}\n  solutions={[\n    { title: 'One-click installation' },\n    { title: 'Optimized performance' }\n  ]}\n/>\n```\n\n\n\n### With Background Glow\n\n\n\n```jsx\nimport { LandingProductProblemSolution } from '@/components/landing/LandingProductProblemSolution';\n\n<LandingProductProblemSolution\n  title=\"Problems and Solutions\"\n  withBackgroundGlow={true}\n  problems={[\n    { title: 'Complex setup process' },\n    { title: 'Poor performance' }\n  ]}\n  solutions={[\n    { title: 'One-click installation' },\n    { title: 'Optimized performance' }\n  ]}\n/>\n```\n\n\n</Examples>\n\n<PropsReference>\n### LandingProductProblemSolution Props\n\n| Prop Name | Prop Type | Required | Default |\n| --- | --- | ----- | ---- |\n| **className** <Tippy>Additional CSS classes to apply to the section container</Tippy> | `string` | No | - |\n| **problems** <Tippy>Array of problems to display</Tippy> | `KeyPoint[]` | Yes | - |\n| **solutions** <Tippy>Array of solutions to display</Tippy> | `KeyPoint[]` | Yes | - |\n| **title** <Tippy>Main heading text for the section</Tippy> | `string` | No | - |\n| **titleComponent** <Tippy>Custom React component to replace the default title</Tippy> | `ReactNode` | No | - |\n| **description** <Tippy>Supporting text that appears under the title</Tippy> | `string` | No | - |\n| **descriptionComponent** <Tippy>Custom React component to replace the default description</Tippy> | `ReactNode` | No | - |\n| **solutionTitle** <Tippy>Heading text for the solution column</Tippy> | `string` | No | `'Solution'` |\n| **solutionTitleComponent** <Tippy>Custom React component to replace the default solution title</Tippy> | `ReactNode` | No | - |\n| **problemTitle** <Tippy>Heading text for the problem column</Tippy> | `string` | No | `'Problem'` |\n| **problemTitleComponent** <Tippy>Custom React component to replace the default problem title</Tippy> | `ReactNode` | No | - |\n| **variant** <Tippy>Color theme variant</Tippy> | `'primary' \\| 'secondary'` | No | `'primary'` |\n| **withBackground** <Tippy>Apply a subtle background color based on the variant</Tippy> | `boolean` | No | `false` |\n| **withBackgroundGlow** <Tippy>Add a background glow effect</Tippy> | `boolean` | No | `false` |\n| **backgroundGlowVariant** <Tippy>Color theme for the background glow</Tippy> | `'primary' \\| 'secondary'` | No | `'primary'` |\n| **textPosition** <Tippy>Alignment of the section title and description</Tippy> | `'center' \\| 'left'` | No | `'center'` |\n\n### Interfaces\n\n```ts\nexport interface KeyPoint {\n  title: string;\n  description?: string;\n}\n```\n\nThe `KeyPoint` interface is used both for problems and solutions to provide consistent formatting.\n</PropsReference>"}, "ProductCard": {"description": "The Product Card Section component is designed to showcase products in a responsive grid layout with customizable columns. It's perfect for e-commerce sections, featured products, or any collection of items that need to be displayed in an organized grid format. The component supports both programmatic usage with an array of products and declarative usage with children components.", "api": "<Usage>\n```jsx\nimport { LandingProductCardSection } from '@/components/landing/card/LandingProductCardSection';\nimport { LandingProductCard } from '@/components/landing/card/LandingProductCard';\n```\n\n```jsx\n<LandingProductCardSection\n  title=\"Our Products\"\n  description=\"Explore our collection\"\n  products={[\n    {\n      title: \"Product 1\",\n      description: \"Description of product 1\",\n      imageSrc: \"/static/images/backdrop-1.webp\",\n    },\n    // ... more products\n  ]}\n/>\n```\n</Usage>\n\n<Examples>\n### Four Column Grid\n\nDisplay products in a 4-column grid for a more compact layout.\n\n\n\n```jsx\nimport { LandingProductCardSection } from '@/components/landing/card/LandingProductCardSection';\nimport { Button } from '@/components/shared/ui/button';\n\n<LandingProductCardSection\n  title=\"Featured Collection\"\n  description=\"A selection of our most popular items\"\n  gridColumns={4}\n  variant=\"secondary\"\n  withBackground\n  textPosition=\"left\"\n  products={[\n    {\n      title: \"Side Table\",\n      description: \"Compact side table with marble top.\",\n      imageSrc: \"/static/images/backdrop-12.webp\",\n      actionComponent: <Button>View</Button>\n    },\n    {\n      title: \"Floor Lamp\",\n      description: \"Adjustable floor lamp with linen shade.\",\n      imageSrc: \"/static/images/backdrop-13.webp\",\n      actionComponent: <Button>Details</Button>\n    },\n    {\n      title: \"Bookshelf\",\n      description: \"Modular bookshelf with adjustable shelves.\",\n      imageSrc: \"/static/images/backdrop-14.webp\",\n      actionComponent: <Button>View</Button>\n    },\n    {\n      title: \"Desk Chair\",\n      description: \"Ergonomic desk chair with breathable mesh.\",\n      imageSrc: \"/static/images/backdrop-15.webp\",\n      actionComponent: <Button>Details</Button>\n    }\n  ]}\n/>\n```\n\n\n\n\n### Component-Based Usage\n\nYou can use the component-based approach by providing child `LandingProductCard` components directly. This allows for more customization and flexibility.\n\n\n\n```jsx\nimport { LandingProductCardSection } from '@/components/landing/card/LandingProductCardSection';\nimport { LandingProductCard } from '@/components/landing/card/LandingProductCard';\nimport { Button } from '@/components/shared/ui/button';\n\n<LandingProductCardSection\n  title=\"Product Collection\"\n  description=\"Handcrafted with attention to detail\"\n>\n  <LandingProductCard\n    title=\"Wooden Table\"\n    description=\"Handcrafted oak dining table with natural finish.\"\n    imageSrc=\"/static/images/backdrop-6.webp\"\n    actionComponent={<Button>Add to Cart</Button>}\n  />\n  <LandingProductCard\n    title=\"Ceramic Vase\"\n    description=\"Hand-thrown ceramic vase with unique glaze pattern.\"\n    imageSrc=\"/static/images/backdrop-7.webp\"\n    actionComponent={<Button variant=\"secondary\">View Details</Button>}\n    variant=\"secondary\"\n    featured\n  />\n  <LandingProductCard\n    title=\"Wall Sconce\"\n    description=\"Modern wall sconce with adjustable arm and warm lighting.\"\n    imageSrc=\"/static/images/backdrop-8.webp\",\n    actionComponent={<Button>Purchase</Button>}\n  />\n</LandingProductCardSection>\n```\n\n\n\n### With Background\n\n\n\n```jsx\nimport { LandingProductCardSection } from '@/components/landing/card/LandingProductCardSection';\nimport { Button } from '@/components/shared/ui/button';\n\n<LandingProductCardSection\n  title=\"Premium Collection\"\n  description=\"Our finest selection of premium products\"\n  withBackground\n  variant=\"primary\"\n  gridColumns={3}\n  products={[\n    {\n      title: \"Leather Armchair\",\n      description: \"Premium leather armchair with walnut frame.\",\n      imageSrc: \"/static/images/backdrop-9.webp\",\n      actionComponent: <Button>View Details</Button>\n    },\n    {\n      title: \"Pendant Light\",\n      description: \"Handblown glass pendant light with brass accents.\",\n      imageSrc: \"/static/images/backdrop-10.webp\",\n      actionComponent: <Button>Purchase</Button>\n    },\n    {\n      title: \"Area Rug\",\n      description: \"Hand-knotted wool area rug in natural colors.\",\n      imageSrc: \"/static/images/backdrop-11.webp\",\n      actionComponent: <Button>View Details</Button>\n    }\n  ]}\n/>\n```\n\n\n\n\n### Custom Card Components\n\nDemonstrate using the `topComponent` and `bottomComponent` props to add custom content to the product cards.\n\n\n\n```jsx\nimport { LandingProductCardSection } from '@/components/landing/card/LandingProductCardSection';\nimport { LandingProductCard } from '@/components/landing/card/LandingProductCard';\nimport { Button } from '@/components/shared/ui/button';\nimport { Badge } from '@/components/shared/ui/badge';\n\n<LandingProductCardSection\n  title=\"Custom Product Cards\"\n  description=\"Featuring custom component slots for flexible layouts\"\n>\n  <LandingProductCard\n    title=\"Premium Chair\"\n    description=\"Ergonomic office chair with adjustable height and lumbar support.\"\n    imageSrc=\"/static/images/backdrop-16.webp\"\n    topComponent={<Badge>New Arrival</Badge>}\n    actionComponent={<Button className=\"w-full\">Add to Cart</Button>}\n  />\n  <LandingProductCard\n    title=\"Standing Desk\"\n    description=\"Height-adjustable standing desk with memory presets.\"\n    imageSrc=\"/static/images/backdrop-17.webp\"\n    topComponent={<Badge variant=\"destructive\">Bestseller</Badge>}\n    bottomComponent={<div className=\"flex justify-center\"><LandingRating /></div>}\n    actionComponent={<Button className=\"w-full\">View Details</Button>}\n  />\n  <LandingProductCard\n    title=\"Desk Lamp\"\n    description=\"Adjustable desk lamp with wireless charging base.\"\n    imageSrc=\"/static/images/backdrop-18.webp\"\n    topComponent={<Badge variant=\"secondary\">Limited Edition</Badge>}\n    bottomComponent={<div className=\"text-sm font-medium text-green-600\">In stock - ships in 24 hours</div>}\n    actionComponent={<Button className=\"w-full\">Buy Now</Button>}\n  />\n</LandingProductCardSection>\n```\n\n\n</Examples>\n\n<PropsReference>\n### LandingProductCardSection Props\n\n| Prop Name | Prop Type | Required | Default |\n| --- | --- | ----- | ---- |\n| **title** <Tippy>Main title of the product card section</Tippy> | `string` | No | - |\n| **titleComponent** <Tippy>Custom component for the title</Tippy> | `React.ReactNode` | No | - |\n| **description** <Tippy>Text description displayed below the title</Tippy> | `string` | No | - |\n| **descriptionComponent** <Tippy>Custom component for the description</Tippy> | `React.ReactNode` | No | - |\n| **products** <Tippy>Array of product objects to be displayed</Tippy> | `ProductCardProps[]` | No | `[]` |\n| **children** <Tippy>Child components to render instead of products array</Tippy> | `React.ReactNode` | No | - |\n| **className** <Tippy>Additional CSS classes for the section</Tippy> | `string` | No | `''` |\n| **innerClassName** <Tippy>Additional CSS classes for the inner container</Tippy> | `string` | No | `''` |\n| **gridClassName** <Tippy>Additional CSS classes for the grid</Tippy> | `string` | No | `''` |\n| **gridColumns** <Tippy>Number of columns to display on desktop</Tippy> | `3 \\| 4` | No | `3` |\n| **withBackground** <Tippy>Whether to display a background color</Tippy> | `boolean` | No | `false` |\n| **withBackgroundGlow** <Tippy>Whether to display a background glow effect</Tippy> | `boolean` | No | `false` |\n| **backgroundGlowVariant** <Tippy>Color variant for the background glow</Tippy> | `'primary' \\| 'secondary'` | No | `'primary'` |\n| **variant** <Tippy>Color variant for the section</Tippy> | `'primary' \\| 'secondary'` | No | `'primary'` |\n| **textPosition** <Tippy>Alignment of the text content</Tippy> | `'center' \\| 'left'` | No | `'center'` |\n\n### LandingProductCard Props\n\n| Prop Name | Prop Type | Required | Default |\n| --- | --- | ----- | ---- |\n| **title** <Tippy>Title of the product</Tippy> | `string` | Yes | - |\n| **titleComponent** <Tippy>Custom component for the title</Tippy> | `React.ReactNode` | No | - |\n| **description** <Tippy>Description of the product</Tippy> | `string` | No | - |\n| **descriptionComponent** <Tippy>Custom component for the description</Tippy> | `React.ReactNode` | No | - |\n| **imageSrc** <Tippy>Source URL for the product image</Tippy> | `string` | No | - |\n| **imageAlt** <Tippy>Alt text for the product image</Tippy> | `string` | No | `''` |\n| **actionComponent** <Tippy>Component for the call-to-action (e.g., button)</Tippy> | `React.ReactNode` | No | - |\n| **topComponent** <Tippy>Custom component to display at the top of the card content</Tippy> | `React.ReactNode` | No | - |\n| **bottomComponent** <Tippy>Custom component to display at the bottom of the card content</Tippy> | `React.ReactNode` | No | - |\n| **featured** <Tippy>Whether to highlight this card as featured</Tippy> | `boolean` | No | `false` |\n| **className** <Tippy>Additional CSS classes for the card</Tippy> | `string` | No | - |\n| **variant** <Tippy>Color variant for the card</Tippy> | `'primary' \\| 'secondary' \\| 'default'` | No | `'default'` |\n| **href** <Tippy>URL to navigate to when the card is clicked</Tippy> | `string` | No | - |\n\n### Type Definitions\n\n```typescript\nexport interface ProductCardProps {\n  title: string;\n  titleComponent?: React.ReactNode;\n  description?: string;\n  descriptionComponent?: React.ReactNode;\n  imageSrc?: string;\n  imageAlt?: string;\n  actionComponent?: React.ReactNode;\n  topComponent?: React.ReactNode;\n  bottomComponent?: React.ReactNode;\n  featured?: boolean;\n  className?: string;\n  variant?: 'primary' | 'secondary' | 'default';\n  href?: string;\n}\n```\n\n```typescript\nexport interface LandingProductCardSectionProps {\n  title?: string;\n  titleComponent?: React.ReactNode;\n  description?: string;\n  descriptionComponent?: React.ReactNode;\n  products?: ProductCardProps[];\n  children?: React.ReactNode;\n  className?: string;\n  innerClassName?: string;\n  gridClassName?: string;\n  gridColumns?: 3 | 4;\n  withBackground?: boolean;\n  withBackgroundGlow?: boolean;\n  backgroundGlowVariant?: 'primary' | 'secondary';\n  variant?: 'primary' | 'secondary';\n  textPosition?: 'center' | 'left';\n}\n```\n</PropsReference>"}, "ProductFeature": {"description": "This component can display a product feature e.g. on your landing page, features page or elsewhere. It can show an image on the left, right or center; either in perspective or flat and has many customization options. Use this to highlight a feature or key aspect of your product with text and an optional image.", "api": "<Usage>\n```jsx\nimport { LandingProductFeature } from '@/components/landing/LandingProductFeature';\n```\n\n```jsx\n<LandingProductFeature\n  title=\"The wait is over\"\n  description=\"Give your project the home it deserves. Your users will love you for it.\"\n  imageSrc=\"/static/images/backdrop-5.webp\"\n  imageAlt=\"Sample image\"\n/>\n```\n</Usage>\n\n<Examples>\n### Image position\n\n\n\n```jsx\nimport { LandingProductFeature } from '@/components/landing/LandingProductFeature';\n\n<LandingProductFeature\n  imagePosition=\"left\"\n  title=\"The wait is over\"\n  description=\"Give your project the home it deserves. Your users will love you for it.\"\n  imageSrc=\"/static/images/backdrop-5.webp\"\n  imageAlt=\"Sample image\"\n/>;\n```\n\n\n\n### Image perspective\n\n\n\n```jsx\nimport { LandingProductFeature } from '@/components/landing/LandingProductFeature';\n\n<LandingProductFeature\n  imagePerspective=\"paper\"\n  title=\"The wait is over\"\n  description=\"Give your project the home it deserves. Your users will love you for it.\"\n  imageSrc=\"/static/images/backdrop-5.webp\"\n  imageAlt=\"Sample image\"\n/>;\n```\n\n\n\n### Customization\n\nIt is also possible to customize the background color, change text\nposition or disable zooming on hover.\n\n\n\n```jsx\nimport { LandingProductFeature } from '@/components/landing/LandingProductFeature';\n\n<LandingProductFeature\n  withBackground\n  variant=\"secondary\"\n  zoomOnHover={false}\n  imagePosition=\"left\"\n  imagePerspective=\"right\"\n  title=\"The wait is over\"\n  description=\"Give your project the home it deserves. Your users will love you for it.\"\n  imageSrc=\"/static/images/backdrop-5.webp\"\n  imageAlt=\"Sample image\"\n/>;\n```\n\n\n\n### With Background Glow\n\n\n\n```jsx\nimport { LandingProductFeature } from '@/components/landing/LandingProductFeature';\n\n<LandingProductFeature\n  withBackgroundGlow\n  backgroundGlowVariant=\"primary\"\n  title=\"The wait is over\"\n  description=\"Give your project the home it deserves. Your users will love you for it.\"\n  imageSrc=\"/static/images/backdrop-5.webp\"\n  imageAlt=\"Sample image\"\n/>;\n```\n\n\n\n### With Bullet Points\n\n\n\n```jsx\nimport { LandingProductFeature } from '@/components/landing/LandingProductFeature';\nimport { LandingProductFeatureKeyPoints } from '@/components/landing/LandingProductFeatureKeyPoints';\n\n<LandingProductFeature\n  title=\"The wait is over\"\n  descriptionComponent={\n    <>\n      <LandingProductFeatureKeyPoints\n        keyPoints={[\n          {\n            title: 'Intelligent Assistance',\n            description:\n              'Receive personalized recommendations and insights tailored to your workflow.',\n          },\n          {\n            title: 'Seamless Collaboration',\n            description:\n              'Easily collaborate with team members and clients in real-time.',\n          },\n          {\n            title: 'Advanced Customization',\n            description:\n              'Tailor your app to fit your unique requirements with extensive customization options.',\n          },\n        ]}\n      />\n    </>\n  }\n  imageSrc=\"/static/images/backdrop-5.webp\"\n  imageAlt=\"Sample image\"\n/>;\n```\n\n\n\n### With Call to Action (CTA)\n\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingProductFeature } from '@/components/landing/LandingProductFeature';\n\n<LandingProductFeature\n  title=\"The wait is over\"\n  descriptionComponent={\n    <>\n      <p>\n        Receive personalized recommendations and insights tailored to your\n        workflow.\n      </p>\n\n      <Button className=\"mt-8\" asChild>\n        <a href=\"#\">Try now for free</a>\n      </Button>\n\n      <p className=\"text-sm\">7 day free trial, no credit card required.</p>\n    </>\n  }\n  imageSrc=\"/static/images/backdrop-5.webp\"\n  imageAlt=\"Sample image\"\n/>;\n```\n\n\n\n### With Features Grid\n\n\n\n```jsx\nimport { LandingProductFeaturesGrid } from '@/components/landing/LandingProductFeaturesGrid';\nimport { LandingProductFeature } from '@/components/landing/LandingProductFeature';\n\n<LandingProductFeaturesGrid\n  title=\"Get the job done in no time\"\n  description=\"You'll save days of work and the only question you'll have is 'What do I do with all this free time?'\"\n>\n  <LandingProductFeature\n    title=\"Deploy\"\n    description=\"Give your project the home it deserves.\"\n    imageSrc=\"/static/images/shipixen/product/14.webp\"\n    imageAlt=\"Sample image\"\n  />\n\n  <LandingProductFeature\n    title=\"No config\"\n    description=\"No configuration needed. We take care of it.\"\n    imageSrc=\"/static/images/shipixen/product/4.webp\"\n    imageAlt=\"Sample image\"\n  />\n\n  <LandingProductFeature\n    title=\"Theme\"\n    description=\"Choose from more than 30+ themes or create your own.\"\n    imageSrc=\"/static/images/shipixen/product/2.webp\"\n    imageAlt=\"Sample image\"\n  />\n</LandingProductFeaturesGrid>;\n```\n\n\n\n### With Product Steps\n\n\n\n```jsx\nimport { LandingProductSteps } from '@/components/landing/LandingProductSteps';\nimport { LandingProductFeature } from '@/components/landing/LandingProductFeature';\n\n<LandingProductSteps\n  title=\"How it works\"\n  description=\"Follow these simple steps to get started with our product.\"\n>\n  <LandingProductFeature\n    title=\"Create your account\"\n    description=\"Sign up in seconds and get instant access to our platform.\"\n    imageSrc=\"/static/images/backdrop-4.webp\"\n  />\n  <LandingProductFeature\n    title=\"Customize your workflow\"\n    description=\"Set up your preferences and configure your workspace.\"\n    imageSrc=\"/static/images/backdrop-8.webp\"\n  />\n  <LandingProductFeature\n    title=\"Start collaborating\"\n    description=\"Invite your team and begin working together seamlessly.\"\n    imageSrc=\"/static/images/backdrop-9.webp\"\n  />\n</LandingProductSteps>\n```\n\n\n\n### With Background Effect\n\n\n\n```jsx\nimport { LandingProductFeature } from '@/components/landing/LandingProductFeature';\nimport { LandingDotParticleCtaBg } from '@/components/landing/cta-backgrounds/LandingDotParticleCtaBg';\n\n<LandingProductFeature\n  title=\"The wait is over\"\n  description=\"Give your project the home it deserves. Your users will love you for it.\"\n  imageSrc=\"/static/images/backdrop-5.webp\"\n  imageAlt=\"Sample image\"\n  effectComponent={<LandingDotParticleCtaBg />}\n/>;\n```\n\n\n\n\n\n### With Leading Pill\n\n\n\n```jsx\nimport { LandingProductFeature } from '@/components/landing/LandingProductFeature';\n\n<LandingProductFeature\n  title=\"The wait is over\"\n  description=\"Give your project the home it deserves. Your users will love you for it.\"\n  imageSrc=\"/static/images/backdrop-5.webp\"\n  imageAlt=\"Sample image\"\n  leadingComponent={\n    <LandingLeadingPill\n      withBorder={false}\n      withBackground={true}\n      backgroundVariant=\"primary\"\n      leftComponent={<SparklesIcon className=\"w-4 h-4\" />}\n    >\n      Join today\n    </LandingLeadingPill>\n  }\n/>;\n```\n\n\n</Examples>\n\n<PropsReference>\n| Prop Name                                                                                                                              | Prop Type                                                                | Required | Default     |\n| -------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------ | -------- | ----------- |\n| **children** <Tippy>React nodes to be rendered within the component (below the text on smaller screen, right side on larger).</Tippy>  | `React.ReactNode` ǀ `string`                                             | No       | -           |\n| **className** <Tippy>Additional CSS classes to apply to the component.</Tippy>                                                         | `string`                                                                 | No       | -           |\n| **innerClassName** <Tippy>Additional CSS classes to apply to the inner container of the component.</Tippy>                             | `string`                                                                 | No       | -           |\n| **title** <Tippy>The title to be displayed in the component.</Tippy>                                                                   | `string` ǀ `React.ReactNode`                                             | No       | -           |\n| **titleComponent** <Tippy>Custom React component to render as the title.</Tippy>                                                       | `React.ReactNode`                                                        | No       | -           |\n| **description** <Tippy>The description to be displayed in the component.</Tippy>                                                       | `string` ǀ `React.ReactNode`                                             | No       | -           |\n| **descriptionComponent** <Tippy>Custom React component to render as the description.</Tippy>                                           | `React.ReactNode`                                                        | No       | -           |\n| **leadingComponent** <Tippy>Custom React component to render as the leading component.</Tippy>                                         | `React.ReactNode`                                                        | No       | -           |\n| **textPosition** <Tippy>The position of the text content ('center' or 'left').</Tippy>                                                 | `'center'` ǀ `'left'`                                                    | No       | `'left'`    |\n| **imageSrc** <Tippy>The URL of the image to be displayed.</Tippy>                                                                      | `string`                                                                 | No       | -           |\n| **imageAlt** <Tippy>The alternative text for the image.</Tippy>                                                                        | `string`                                                                 | No       | ''          |\n| **imagePosition** <Tippy>The position of the image ('left', 'right', or 'center').</Tippy>                                             | `'left'` ǀ `'right'` ǀ `'center'`                                        | No       | `'right'`   |\n| **imagePerspective** <Tippy>The perspective effect for the image ('none', 'left', 'right', 'bottom', 'bottom-lg', or 'paper').</Tippy> | `'none'` ǀ `'left'` ǀ `'right'` ǀ `'bottom'` ǀ `'bottom-lg'` ǀ `'paper'` | No       | `'paper'`   |\n| **imageShadow** <Tippy>The shadow effect for the image ('none', 'soft', or 'hard').</Tippy>                                            | `'none'` ǀ `'soft'` ǀ `'hard'`                                           | No       | `'hard'`    |\n| **zoomOnHover** <Tippy>Whether to enable zoom effect on hover for the image.</Tippy>                                                   | `boolean`                                                                | No       | `true`      |\n| **minHeight** <Tippy>The minimum height of the component.</Tippy>                                                                      | `number`                                                                 | No       | `350`       |\n| **withBackground** <Tippy>Whether to include a background for the component.</Tippy>                                                   | `boolean`                                                                | No       | `false`     |\n| **withBackgroundGlow** <Tippy>Whether to include a glowing background effect.</Tippy>                                                  | `boolean`                                                                | No       | `false`     |\n| **variant** <Tippy>The variant of the component ('primary' or 'secondary').</Tippy>                                                    | `'primary'` ǀ `'secondary'`                                              | No       | `'primary'` |\n| **backgroundGlowVariant** <Tippy>The variant of the glowing background effect ('primary' or 'secondary').</Tippy>                      | `'primary'` ǀ `'secondary'`                                              | No       | `'primary'` |\n| **effectComponent** <Tippy>Custom React component to be displayed as the background effect.</Tippy>                                    | `React.ReactNode`                                                        | No       | -           |\n| **effectClassName** <Tippy>Additional CSS classes to apply to the background effect.</Tippy>                                        | `string`                                                                 | No       | -           |\n</PropsReference>"}, "ProductFeaturesGrid": {"description": "This component displays a title, description and a grid of [Product Feature](/boilerplate-documentation/landing-page-components/product-feature) and/or [Product Video Feature](/boilerplate-documentation/landing-page-components/product-video-feature) (in any combination, passed as children). The component is responsive and will display a different number of columns depending on the number of features passed. The number of columns can be specified using the `numberOfColumns` prop.", "api": "<Usage>\n```jsx\nimport { LandingProductFeaturesGrid } from '@/components/landing/LandingProductFeaturesGrid';\nimport { LandingProductFeature } from '@/components/landing/LandingProductFeature';\n```\n\n```jsx\n<LandingProductFeaturesGrid\n  title=\"Get the job done in no time\"\n  description=\"You'll save days of work and the only question you'll have is 'What do I do with all this free time?'\"\n  numberOfColumns={2}\n>\n  <LandingProductFeature\n    title=\"The wait is over\"\n    description=\"Give your project the home it deserves. Your users will love you for it.\"\n    imageSrc=\"/static/images/shipixen/product/14.webp\"\n    imageAlt=\"Sample image\"\n  />\n\n  <LandingProductFeature\n    title=\"Branding\"\n    description=\"No configuration needed. We take care of everything for you, just press a button.\"\n    imageSrc=\"/static/images/shipixen/product/4.webp\"\n    imageAlt=\"Sample image\"\n  />\n\n  <LandingProductFeature\n    title=\"39+ themes\"\n    description=\"Choose from more than 30+ themes or create your own. Upload your logo, set the size and we take care of the rest.\"\n    imageSrc=\"/static/images/shipixen/product/2.webp\"\n    imageAlt=\"Sample image\"\n  />\n</LandingProductFeaturesGrid>\n```\n</Usage>\n\n<Examples>\n### Usage with video features\n\n\n\n```jsx\nimport { LandingProductFeaturesGrid } from '@/components/landing/LandingProductFeaturesGrid';\nimport { LandingProductVideoFeature } from '@/components/landing/LandingProductVideoFeature';\n\n<LandingProductFeaturesGrid\n  title=\"Get the job done in no time\"\n  description=\"You'll save days of work and the only question you'll have is 'What do I do with all this free time?'\"\n  numberOfColumns={2}\n>\n  <LandingProductVideoFeature\n    title=\"Generate\"\n    description=\"Save time by generating features, sales copy, FAQs and even example testimonials with AI. All beautifully designed.\"\n    autoPlay={false}\n    videoSrc=\"https://cache.shipixen.com/features/2-generate-content-with-ai.mp4\"\n  />\n\n  <LandingProductVideoFeature\n    title=\"Design\"\n    description=\"Choose from more than 30+ themes or create your own. Upload your logo, set the size and we take care of the rest.\"\n    autoPlay={false}\n    videoSrc=\"https://cache.shipixen.com/features/3-theme-and-logo.mp4\"\n  />\n\n  <LandingProductVideoFeature\n    title=\"Build\"\n    description=\"Use our pricing page builder to create a beautiful pricing page. Choose from different layouts and monthly/yearly pricing options. It's as easy as it looks.\"\n    autoPlay={false}\n    videoSrc=\"https://cache.shipixen.com/features/11-pricing-page-builder.mp4\"\n  />\n</LandingProductFeaturesGrid>;\n```\n\n\n\n### Mixing features\n\n\n\n```jsx\nimport { LandingProductFeaturesGrid } from '@/components/landing/LandingProductFeaturesGrid';\nimport { LandingProductFeature } from '@/components/landing/LandingProductFeature';\nimport { LandingProductVideoFeature } from '@/components/landing/LandingProductVideoFeature';\n\n<LandingProductFeaturesGrid\n  title=\"Get the job done in no time\"\n  description=\"You'll save days of work and the only question you'll have is 'What do I do with all this free time?'\"\n>\n  <LandingProductFeature\n    title=\"Design\"\n    description=\"No configuration needed. We take care of everything for you, just press a button.\"\n    imageSrc=\"/static/images/shipixen/product/4.webp\"\n    imageAlt=\"Sample image\"\n  />\n\n  <LandingProductVideoFeature\n    title=\"Build\"\n    description=\"Use our pricing page builder to create a beautiful pricing page. Choose from different layouts and monthly/yearly pricing options. It's as easy as it looks.\"\n    autoPlay={true}\n    videoSrc=\"https://cache.shipixen.com/features/11-pricing-page-builder.mp4\"\n  />\n</LandingProductFeaturesGrid>;\n```\n\n\n\n### Customization\n\n\n\n```jsx\nimport { LandingProductFeaturesGrid } from '@/components/landing/LandingProductFeaturesGrid';\nimport { LandingProductFeature } from '@/components/landing/LandingProductFeature';\nimport { LandingProductVideoFeature } from '@/components/landing/LandingProductVideoFeature';\n\n<LandingProductFeaturesGrid\n  title=\"Get the job done in no time\"\n  description=\"You'll save days of work and the only question you'll have is 'What do I do with all this free time?'\"\n>\n  <LandingProductFeature\n    title=\"Design\"\n    description=\"No configuration needed. We take care of everything for you, just press a button.\"\n    imageSrc=\"/static/images/shipixen/product/4.webp\"\n    imageAlt=\"Sample image\"\n  />\n\n  <LandingProductVideoFeature\n    title=\"Build\"\n    description=\"Use our pricing page builder to create a beautiful pricing page. Choose from different layouts and monthly/yearly pricing options. It's as easy as it looks.\"\n    autoPlay={true}\n    videoSrc=\"https://cache.shipixen.com/features/11-pricing-page-builder.mp4\"\n  />\n</LandingProductFeaturesGrid>;\n```\n\n\n\n### With a 3 column layout\n\n\n\n```jsx\nimport { LandingProductFeaturesGrid } from '@/components/landing/LandingProductFeaturesGrid';\nimport { LandingProductFeature } from '@/components/landing/LandingProductFeature';\n\n<LandingProductFeaturesGrid\n  title=\"Get the job done in no time\"\n  description=\"You'll save days of work and the only question you'll have is 'What do I do with all this free time?'\"\n  numberOfColumns={3}\n>\n  <LandingProductFeature\n    title=\"Deploy\"\n    description=\"Give your project the home it deserves.\"\n    imageSrc=\"/static/images/shipixen/product/14.webp\"\n    imageAlt=\"Sample image\"\n  />\n\n  <LandingProductFeature\n    title=\"No config\"\n    description=\"No configuration needed. We take care of it.\"\n    imageSrc=\"/static/images/shipixen/product/4.webp\"\n    imageAlt=\"Sample image\"\n  />\n\n  <LandingProductFeature\n    title=\"Theme\"\n    description=\"Choose from more than 30+ themes or create your own.\"\n    imageSrc=\"/static/images/shipixen/product/2.webp\"\n    imageAlt=\"Sample image\"\n  />\n</LandingProductFeaturesGrid>;\n```\n\n\n</Examples>\n\n<PropsReference>\n| Prop Name                                                                                                                                                              | Prop Type                             | Required | Default       |\n| ---------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------- | -------- | ------------- |\n| **children** <Tippy>React nodes to be rendered within the component, supporting a mix of `LandingProductFeature` and/or `LandingProductVideoFeature` elements.</Tippy> | `React.ReactNode`                     | No       | -             |\n| **title** <Tippy>The title text or a React node to be displayed at the top of the component.</Tippy>                                                                   | `string` ǀ `React.ReactNode`          | No       | -             |\n| **titleComponent** <Tippy>A React node used to render a custom title component.</Tippy>                                                                                | `React.ReactNode`                     | No       | -             |\n| **description** <Tippy>The description text or a React node to be displayed below the title.</Tippy>                                                                   | `string` ǀ `React.ReactNode`          | No       | -             |\n| **descriptionComponent** <Tippy>A React node used to render a custom description component.</Tippy>                                                                    | `React.ReactNode`                     | No       | -             |\n| **withBackground** <Tippy>Determines whether the background styling based on the variant is applied to the component.</Tippy>                                          | `boolean`                             | No       | `true`        |\n| **variant** <Tippy>Defines the color scheme variant for the component, affecting both background and child components.</Tippy>                                         | `'primary'` ǀ `'secondary'`           | No       | `'primary'`   |\n| **containerType** <Tippy>Specifies the width of the container, affecting its max-width on larger screens.</Tippy>                                                      | `'narrow'` ǀ `'wide'` ǀ `'ultrawide'` | No       | `'ultrawide'` |\n| **numberOfColumns** <Tippy>Specifies the number of columns to display in the grid.</Tippy>                                                                            | `number`                              | No       | -             |\n</PropsReference>"}, "ProductHuntAward": {"description": "Use this component to show a Product Hunt award, if applicable, to increase trust.", "api": "<Usage>\n```jsx\nimport { LandingProductHuntAward } from '@/components/landing/social-proof/LandingProductHuntAward';\n```\n\n```jsx\n<LandingProductHuntAward place={3} />\n```\n</Usage>\n\n<Examples>\n### Customization\n\nThe award can either be grayscale or colored and it comes in two sizes: small and default.\n\n\n```jsx\nimport { LandingProductHuntAward } from '@/components/landing/social-proof/LandingProductHuntAward';\n\n<LandingProductHuntAward size=\"small\" grayscale={false} />\n```\n\n\n### With custom text\n\n\n```jsx\nimport { LandingProductHuntAward } from '@/components/landing/social-proof/LandingProductHuntAward';\n\n<LandingProductHuntAward\n  title=\"#1 Product of the week\"\n  subtitle=\"Marketing\"\n/>\n```\n\n</Examples>\n\n<PropsReference>\n| Prop Name                                                                                                               | Prop Type               | Required | Default                |\n| ----------------------------------------------------------------------------------------------------------------------- | ----------------------- | -------- | ---------------------- |\n| **place** <Tippy>The place or rank to display.</Tippy>                                                                  | `number` ǀ `string`     | Yes      | -                      |\n| **title** <Tippy>The title text displayed above the place/rank.</Tippy>                                                 | `string`                | No       | `'Product of the Day'` |\n| **size** <Tippy>Defines the size of the component, affecting its height and the size of text.</Tippy>                   | `'default'` ǀ `'small'` | No       | `'default'`            |\n| **grayscale** <Tippy>Whether the component should be displayed in grayscale colors.</Tippy>                             | `boolean`               | No       | `true`                 |\n| **textContainerClassName** <Tippy>Additional class names for styling the container of the title and place text.</Tippy> | `string`                | No       | -                      |\n| **titleClassName** <Tippy>Additional class names for styling the title text.</Tippy>                                    | `string`                | No       | -                      |\n| **placeClassName** <Tippy>Additional class names for styling the place/rank text.</Tippy>                               | `string`                | No       | -                      |\n</PropsReference>"}, "ProductSteps": {"description": "The `LandingProductSteps` component displays a title, description, and a list of steps with alternating media positions. It's designed for showcasing product features, workflows, or processes on landing pages. This component is ideal when you need to present more than three features in a visually engaging format.", "api": "<Usage>\n```jsx\nimport { LandingProductSteps } from '@/components/landing/LandingProductSteps';\nimport { LandingProductFeature } from '@/components/landing/LandingProductFeature';\nimport { LandingProductVideoFeature } from '@/components/landing/LandingProductVideoFeature';\n```\n\n```jsx\n<LandingProductSteps\n  title=\"Product Features\"\n  description=\"Discover what makes our product stand out.\"\n>\n  <LandingProductFeature\n    title=\"Feature One\"\n    description=\"Description of feature one\"\n    imageSrc=\"/path/to/image.webp\"\n  />\n  <LandingProductFeature\n    title=\"Feature Two\"\n    description=\"Description of feature two\"\n    imageSrc=\"/path/to/image.webp\"\n  />\n</LandingProductSteps>\n```\n</Usage>\n\n<Examples>\n### Grid Display\n\n\n\n```jsx\nimport { LandingProductSteps } from '@/components/landing/LandingProductSteps';\nimport { LandingProductFeature } from '@/components/landing/LandingProductFeature';\n\n<LandingProductSteps\n  title=\"Our Process\"\n  description=\"How we deliver quality results every time.\"\n  display=\"grid\"\n>\n  <LandingProductFeature\n    title=\"Research\"\n    description=\"We start by understanding your needs and goals.\"\n    imageSrc=\"/static/images/backdrop-1.webp\"\n  />\n  <LandingProductFeature\n    title=\"Design\"\n    description=\"Our team creates tailored solutions for your business.\"\n    imageSrc=\"/static/images/backdrop-5.webp\"\n  />\n  <LandingProductFeature\n    title=\"Code\"\n    description=\"We build your project to perfection.\"\n    imageSrc=\"/static/images/backdrop-6.webp\"\n  />\n</LandingProductSteps>\n```\n\n\n### Secondary Variant\n\n\n\n```jsx\nimport { LandingProductSteps } from '@/components/landing/LandingProductSteps';\nimport { LandingProductFeature } from '@/components/landing/LandingProductFeature';\n\n<LandingProductSteps\n  title=\"Our Process\"\n  description=\"How we deliver quality results every time.\"\n  variant=\"secondary\"\n>\n  <LandingProductFeature\n    title=\"Research\"\n    description=\"We start by understanding your needs and goals.\"\n    imageSrc=\"/static/images/backdrop-4.webp\"\n  />\n  <LandingProductFeature\n    title=\"Design\"\n    description=\"Our team creates tailored solutions for your business.\"\n    imageSrc=\"/static/images/backdrop-5.webp\"\n  />\n</LandingProductSteps>\n```\n\n\n\n### With Background Glow\n\n\n\n```jsx\nimport { LandingProductSteps } from '@/components/landing/LandingProductSteps';\nimport { LandingProductFeature } from '@/components/landing/LandingProductFeature';\n\n<LandingProductSteps\n  title=\"Platform Features\"\n  description=\"Discover the powerful capabilities of our platform.\"\n  withBackgroundGlow={true}\n>\n  <LandingProductFeature\n    title=\"Analytics Dashboard\"\n    description=\"Track key metrics and performance indicators in real-time.\"\n    imageSrc=\"/static/images/backdrop-6.webp\"\n  />\n  <LandingProductFeature\n    title=\"Team Collaboration\"\n    description=\"Work together efficiently with integrated tools.\"\n    imageSrc=\"/static/images/backdrop-7.webp\"\n  />\n</LandingProductSteps>\n```\n\n\n\n### With Video Features\n\n\n\n```jsx\nimport { LandingProductSteps } from '@/components/landing/LandingProductSteps';\nimport { LandingProductFeature } from '@/components/landing/LandingProductFeature';\nimport { LandingProductVideoFeature } from '@/components/landing/LandingProductVideoFeature';\n\n<LandingProductSteps\n  title=\"See it in action\"\n  description=\"Watch how our product solves real-world problems.\"\n>\n  <LandingProductVideoFeature\n    title=\"Easy Setup\"\n    description=\"Get started in minutes with our guided setup process.\"\n    videoSrc=\"https://cache.shipixen.com/features/8-customize-pages.mp4\"\n    videoPoster=\"/static/images/backdrop-8.webp\"\n  />\n  <LandingProductFeature\n    title=\"Intuitive Interface\"\n    description=\"Navigate with ease through our user-friendly platform.\"\n    imageSrc=\"/static/images/backdrop-9.webp\"\n  />\n</LandingProductSteps>\n```\n\n\n</Examples>\n\n<PropsReference>\n| Prop Name | Prop Type | Required | Default |\n| --- | --- | ----- | ---- |\n| **className** <Tippy>Additional CSS classes for the section container.</Tippy> | `string` | No | `undefined` |\n| **children** <Tippy>LandingProductFeature and/or LandingProductVideoFeature components to display as steps.</Tippy> | `React.ReactNode` | No | `undefined` |\n| **title** <Tippy>The main heading text for the section.</Tippy> | `string \\| React.ReactNode` | No | `undefined` |\n| **titleComponent** <Tippy>Custom React component to use instead of the default title.</Tippy> | `React.ReactNode` | No | `undefined` |\n| **description** <Tippy>Descriptive text that appears below the title.</Tippy> | `string \\| React.ReactNode` | No | `undefined` |\n| **descriptionComponent** <Tippy>Custom React component to use instead of the default description.</Tippy> | `React.ReactNode` | No | `undefined` |\n| **withBackground** <Tippy>Determines if the component has a semi-transparent background color.</Tippy> | `boolean` | No | `true` |\n| **withBackgroundGlow** <Tippy>Adds a decorative glow effect to the background.</Tippy> | `boolean` | No | `false` |\n| **variant** <Tippy>Color theme for the component, affecting background colors.</Tippy> | `'primary' \\| 'secondary'` | No | `'primary'` |\n| **backgroundGlowVariant** <Tippy>Color theme for the background glow effect.</Tippy> | `'primary' \\| 'secondary'` | No | `'primary'` |\n| **containerType** <Tippy>Controls the maximum width of the content container.</Tippy> | `'narrow' \\| 'wide' \\| 'ultrawide'` | No | `'ultrawide'` |\n| **display** <Tippy>Controls the display of the component.</Tippy> | `'list' \\| 'grid'` | No | `'list'` |\n</PropsReference>"}, "ProductTour": {"description": "This component displays a list of features and content corresponding to each, creating a product tour. It is useful to showcase many features in a compact way and guide the user through the product.", "api": "<Usage>\n```jsx\nimport { LandingProductTourSection, LandingProductTourList, LandingProductTourTrigger, LandingProductTourContent } from '@/components/landing/LandingProductTour';\nimport { VideoPlayer } from '@/components/shared/ui/VideoPlayer';\n```\n\n```jsx\n<LandingProductTourSection\n  title='Landing page in minutes'\n  description=\"Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back.\"\n  defaultValue=\"feature-1\"\n>\n  <LandingProductTourList>\n    <LandingProductTourTrigger value=\"feature-1\">\n      <p className=\"text-xl font-bold\">\n        Automatic deployment to Vercel\n      </p>\n      <p>\n        Deploying the generated template to Vercel is as easy as clicking a button.\n      </p>\n    </LandingProductTourTrigger>\n\n    <LandingProductTourTrigger value=\"feature-2\">\n      <p className=\"text-xl font-bold\">\n        MDX blog, no server required\n      </p>\n      <p>\n        Shipixen comes with a fully featured MDX blog.\n      </p>\n    </LandingProductTourTrigger>\n\n    <LandingProductTourTrigger value=\"feature-3\">\n      <p className=\"text-xl font-bold\">\n        Customizable themes\n      </p>\n      <p>\n        Choose from more than 30+ beautifully designed themes or create your own.\n      </p>\n    </LandingProductTourTrigger>\n  </LandingProductTourList>\n  <LandingProductTourContent value=\"feature-1\">\n    <VideoPlayer\n      className={'w-full rounded-md'}\n      src={'https://cache.shipixen.com/features/20-mobile-optimized.mp4'}\n      autoPlay={true}\n      controls={false}\n      loop={true}\n    />\n  </LandingProductTourContent>\n  <LandingProductTourContent value=\"feature-2\">\n    <VideoPlayer\n      className={'w-full rounded-md'}\n      src={\n        'https://cache.shipixen.com/features/11-pricing-page-builder.mp4'\n      }\n      autoPlay={true}\n      controls={false}\n      loop={true}\n    />\n  </LandingProductTourContent>\n  <LandingProductTourContent value=\"feature-3\">\n    <VideoPlayer\n      className={'w-full rounded-md'}\n      src={'https://cache.shipixen.com/features/21-run-locally.mp4'}\n      autoPlay={true}\n      controls={false}\n      loop={true}\n    />\n  </LandingProductTourContent>\n</LandingProductTourSection>\n```\n</Usage>\n\n<Examples>\n### With images\n\n\n```jsx\nimport { LandingProductTourSection, LandingProductTourList, LandingProductTourTrigger, LandingProductTourContent } from '@/components/landing/LandingProductTour';\nimport Image from '@/components/shared/Image';\n\n<LandingProductTourSection\n  title='Landing page in minutes'\n  description=\"Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back.\"\n  defaultValue=\"feature-1\"\n>\n  <LandingProductTourList>\n    <LandingProductTourTrigger value=\"feature-1\">\n      <p className=\"text-xl font-bold\">\n        Automatic deployment to Vercel\n      </p>\n      <p>\n        Deploying the generated template to Vercel is as easy as clicking a button.\n      </p>\n    </LandingProductTourTrigger>\n\n    <LandingProductTourTrigger value=\"feature-2\">\n      <p className=\"text-xl font-bold\">\n        MDX blog, no server required\n      </p>\n      <p>\n        Shipixen comes with a fully featured MDX blog.\n      </p>\n    </LandingProductTourTrigger>\n\n    <LandingProductTourTrigger value=\"feature-3\">\n      <p className=\"text-xl font-bold\">\n        Customizable themes\n      </p>\n      <p>\n        Choose from more than 30+ beautifully designed themes or create your own.\n      </p>\n    </LandingProductTourTrigger>\n  </LandingProductTourList>\n  <LandingProductTourContent value=\"feature-1\">\n    <Image src=\"https://picsum.photos/id/206/800/800\" width={800} height={800} />\n  </LandingProductTourContent>\n  <LandingProductTourContent value=\"feature-2\">\n    <Image src=\"https://picsum.photos/id/33/800/800\" width={800} height={800} />\n  </LandingProductTourContent>\n  <LandingProductTourContent value=\"feature-3\">\n    <Image src=\"https://picsum.photos/id/59/800/800\" width={800} height={800} />\n  </LandingProductTourContent>\n</LandingProductTourSection>\n```\n\n\n\n### With background\n\n\n```jsx\nimport { LandingProductTourSection, LandingProductTourList, LandingProductTourTrigger, LandingProductTourContent } from '@/components/landing/LandingProductTour';\nimport { VideoPlayer } from '@/components/shared/ui/VideoPlayer';\n\n<LandingProductTourSection\n  title='Landing page in minutes'\n  description=\"Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back.\"\n  defaultValue=\"feature-1\"\n  withBackground\n  variant=\"secondary\"\n>\n  <LandingProductTourList>\n    <LandingProductTourTrigger value=\"feature-1\">\n      <p className=\"text-xl font-bold\">\n        Automatic deployment to Vercel\n      </p>\n      <p>\n        Deploying the generated template to Vercel is as easy as clicking a button.\n      </p>\n    </LandingProductTourTrigger>\n\n    <LandingProductTourTrigger value=\"feature-2\">\n      <p className=\"text-xl font-bold\">\n        MDX blog, no server required\n      </p>\n      <p>\n        Shipixen comes with a fully featured MDX blog.\n      </p>\n    </LandingProductTourTrigger>\n\n    <LandingProductTourTrigger value=\"feature-3\">\n      <p className=\"text-xl font-bold\">\n        Customizable themes\n      </p>\n      <p>\n        Choose from more than 30+ beautifully designed themes or create your own.\n      </p>\n    </LandingProductTourTrigger>\n  </LandingProductTourList>\n  <LandingProductTourContent value=\"feature-1\">\n    <VideoPlayer\n      className={'w-full rounded-md'}\n      src={'https://cache.shipixen.com/features/20-mobile-optimized.mp4'}\n      autoPlay={true}\n      controls={false}\n      loop={true}\n    />\n  </LandingProductTourContent>\n  <LandingProductTourContent value=\"feature-2\">\n    <VideoPlayer\n      className={'w-full rounded-md'}\n      src={\n        'https://cache.shipixen.com/features/11-pricing-page-builder.mp4'\n      }\n      autoPlay={true}\n      controls={false}\n      loop={true}\n    />\n  </LandingProductTourContent>\n  <LandingProductTourContent value=\"feature-3\">\n    <VideoPlayer\n      className={'w-full rounded-md'}\n      src={'https://cache.shipixen.com/features/21-run-locally.mp4'}\n      autoPlay={true}\n      controls={false}\n      loop={true}\n    />\n  </LandingProductTourContent>\n</LandingProductTourSection>\n```\n\n\n\n### With background glow\n\n\n```jsx\nimport { LandingProductTourSection, LandingProductTourList, LandingProductTourTrigger, LandingProductTourContent } from '@/components/landing/LandingProductTour';\nimport { VideoPlayer } from '@/components/shared/ui/VideoPlayer';\n\n<LandingProductTourSection\n  title='Landing page in minutes'\n  description=\"Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back.\"\n  defaultValue=\"feature-1\"\n  withBackgroundGlow\n>\n  <LandingProductTourList>\n    <LandingProductTourTrigger value=\"feature-1\">\n      <p className=\"text-xl font-bold\">\n        Automatic deployment to Vercel\n      </p>\n      <p>\n        Deploying the generated template to Vercel is as easy as clicking a button.\n      </p>\n    </LandingProductTourTrigger>\n\n    <LandingProductTourTrigger value=\"feature-2\">\n      <p className=\"text-xl font-bold\">\n        MDX blog, no server required\n      </p>\n      <p>\n        Shipixen comes with a fully featured MDX blog.\n      </p>\n    </LandingProductTourTrigger>\n\n    <LandingProductTourTrigger value=\"feature-3\">\n      <p className=\"text-xl font-bold\">\n        Customizable themes\n      </p>\n      <p>\n        Choose from more than 30+ beautifully designed themes or create your own.\n      </p>\n    </LandingProductTourTrigger>\n  </LandingProductTourList>\n  <LandingProductTourContent value=\"feature-1\">\n    <VideoPlayer\n      className={'w-full rounded-md'}\n      src={'https://cache.shipixen.com/features/20-mobile-optimized.mp4'}\n      autoPlay={true}\n      controls={false}\n      loop={true}\n    />\n  </LandingProductTourContent>\n  <LandingProductTourContent value=\"feature-2\">\n    <VideoPlayer\n      className={'w-full rounded-md'}\n      src={\n        'https://cache.shipixen.com/features/11-pricing-page-builder.mp4'\n      }\n      autoPlay={true}\n      controls={false}\n      loop={true}\n    />\n  </LandingProductTourContent>\n  <LandingProductTourContent value=\"feature-3\">\n    <VideoPlayer\n      className={'w-full rounded-md'}\n      src={'https://cache.shipixen.com/features/21-run-locally.mp4'}\n      autoPlay={true}\n      controls={false}\n      loop={true}\n    />\n  </LandingProductTourContent>\n</LandingProductTourSection>\n```\n\n\n</Examples>\n\n<PropsReference>\n| Prop Name | Prop Type | Required | Default |\n| --------- | --------- | -------- | ------- |\n| **title** <Tippy>Text content for the section's title.</Tippy> | `string` ǀ `React.ReactNode` | No | - |\n| **titleComponent** <Tippy>React node to render a custom title component.</Tippy> | `React.ReactNode` | No | - |\n| **description** <Tippy>Text content for the section's description.</Tippy> | `string` ǀ `React.ReactNode` | No | - |\n| **descriptionComponent** <Tippy>React node to render a custom description component.</Tippy> | `React.ReactNode` | No | - |\n| **withBackground** <Tippy>Determines whether the section has a background.</Tippy> | `boolean` | No | `false` |\n| **withBackgroundGlow** <Tippy>Determines whether the section has a glowing background effect.</Tippy> | `boolean` | No | `false` |\n| **variant** <Tippy>Specifies the variant of the background.</Tippy> | `'primary'` ǀ `'secondary'` | No | `'primary'` |\n| **backgroundGlowVariant** <Tippy>Specifies the variant of the glowing background effect.</Tippy> | `'primary'` ǀ `'secondary'` | No | `'primary'` |\n</PropsReference>"}, "ProductVideoFeature": {"description": "This component is used to display a product video feature in the landing page. The video could either be left, right or center (larger). The section can have a background or not. It displays a title, description and video of a product's feature. Use this to highlight a feature or key aspect of your product with a video. Can be used with multiple features in a [Product Features Grid](/boilerplate-documentation/landing-page-components/product-features-grid).", "api": "<Usage>\n```jsx\nimport { LandingProductVideoFeature } from '@/components/landing/LandingProductVideoFeature';\n```\n\n```jsx\n<LandingProductVideoFeature\n  title=\"Sites in minutes\"\n  description=\"Choose from more than 30+ themes or create your own.\"\n  videoSrc=\"https://cache.shipixen.com/features/3-theme-and-logo.mp4\"\n/>\n```\n</Usage>\n\n<Examples>\n### Video Position\n\n\n\n```jsx\nimport { LandingProductVideoFeature } from '@/components/landing/LandingProductVideoFeature';\n\n<LandingProductVideoFeature\n  textPosition=\"center\"\n  videoPosition=\"center\"\n  withBackground\n  variant=\"primary\"\n  title=\"Sites in minutes\"\n  description=\"Choose from more than 30+ themes or create your own.\"\n  autoPlay={false}\n  controls={false}\n  videoSrc=\"https://cache.shipixen.com/features/3-theme-and-logo.mp4\"\n/>;\n```\n\n\n\n### Customization\n\nTo better separate sections, you can alternate between primary, secondary\nand no background.<br />\nHere we set <b>variant</b> to <b>secondary</b> and the <b>videoPosition</b> to <b>right</b>.\n\n\n\n```jsx\nimport { LandingProductVideoFeature } from '@/components/landing/LandingProductVideoFeature';\n\n<LandingProductVideoFeature\n  videoPosition=\"right\"\n  withBackground\n  variant=\"secondary\"\n  title=\"Sites in minutes\"\n  description=\"Choose from more than 30+ themes or create your own.\"\n  autoPlay={false}\n  controls={false}\n  videoSrc=\"https://cache.shipixen.com/features/3-theme-and-logo.mp4\"\n/>;\n```\n\n\n\n### With Background Glow\n\n\n\n```jsx\nimport { LandingProductVideoFeature } from '@/components/landing/LandingProductVideoFeature';\n\n<LandingProductVideoFeature\n  videoPosition=\"left\"\n  withBackgroundGlow\n  backgroundGlowVariant=\"primary\"\n  title=\"Easy Branding\"\n  description=\"Choose from more than 30+ themes or create your own. Upload your logo and we take care of the rest.\"\n  autoPlay={false}\n  controls={false}\n  videoSrc=\"https://cache.shipixen.com/features/3-theme-and-logo.mp4\"\n/>;\n```\n\n\n\n### With Bullet Points\n\n\n\n```jsx\nimport { LandingProductFeature } from '@/components/landing/LandingProductFeature';\nimport { LandingProductFeatureKeyPoints } from '@/components/landing/LandingProductFeatureKeyPoints';\n\n<LandingProductVideoFeature\n  withBackgroundGlow\n  backgroundGlowVariant=\"primary\"\n  title=\"Easy Branding\"\n  autoPlay={false}\n  controls={false}\n  videoSrc=\"https://cache.shipixen.com/features/3-theme-and-logo.mp4\"\n  descriptionComponent={\n    <>\n      <LandingProductFeatureKeyPoints\n        keyPoints={[\n          {\n            title: 'Intelligent Assistance',\n            description: 'Receive personalized recommendations.',\n          },\n          {\n            title: 'Seamless Collaboration',\n            description: 'Easily collaborate with team members.',\n          },\n          {\n            title: 'Advanced Customization',\n            description: 'Tailor your app to fit your style.',\n          },\n        ]}\n      />\n    </>\n  }\n/>;\n```\n\n\n\n### With Call to Action (CTA)\n\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingProductFeature } from '@/components/landing/LandingProductFeature';\n\n<LandingProductVideoFeature\n  withBackgroundGlow\n  backgroundGlowVariant=\"primary\"\n  title=\"Easy Branding\"\n  autoPlay={false}\n  controls={false}\n  videoSrc=\"https://cache.shipixen.com/features/3-theme-and-logo.mp4\"\n  descriptionComponent={\n    <>\n      <p>\n        Receive personalized recommendations and insights tailored to your\n        workflow and easily collaborate with team members and clients in\n        real-time.\n      </p>\n\n      <Button className=\"mt-8\" asChild>\n        <a href=\"#\">Try now for free</a>\n      </Button>\n\n      <p className=\"text-sm opacity-70\">\n        7 day free trial, no credit card required.\n      </p>\n    </>\n  }\n/>;\n```\n\n\n\n### With Features Grid\n\n\n\n```jsx\nimport { LandingProductFeaturesGrid } from '@/components/landing/LandingProductFeaturesGrid';\nimport { LandingProductVideoFeature } from '@/components/landing/LandingProductVideoFeature';\n\n<LandingProductFeaturesGrid\n  title=\"Get the job done in no time\"\n  description=\"You'll save days of work and the only question you'll have is 'What do I do with all this free time?'\"\n>\n  <LandingProductVideoFeature\n    title=\"Generate\"\n    description=\"Save time by generating features, sales copy and more.\"\n    autoPlay={false}\n    videoSrc=\"https://cache.shipixen.com/features/2-generate-content-with-ai.mp4\"\n  />\n\n  <LandingProductVideoFeature\n    title=\"Design\"\n    description=\"Choose from more than 30+ themes or create your own.\"\n    autoPlay={false}\n    videoSrc=\"https://cache.shipixen.com/features/3-theme-and-logo.mp4\"\n  />\n\n  <LandingProductVideoFeature\n    title=\"Build\"\n    description=\"Use our pricing page builder to create a beautiful pricing page.\"\n    autoPlay={false}\n    videoSrc=\"https://cache.shipixen.com/features/11-pricing-page-builder.mp4\"\n  />\n</LandingProductFeaturesGrid>;\n```\n\n\n\n### With Product Steps\n\n\n\n```jsx\nimport { LandingProductSteps } from '@/components/landing/LandingProductSteps';\nimport { LandingProductVideoFeature } from '@/components/landing/LandingProductVideoFeature';\n\n<LandingProductSteps\n  title=\"See it in action\"\n  description=\"Watch how our product solves real-world problems.\"\n>\n  <LandingProductVideoFeature\n    title=\"Easy Setup\"\n    description=\"Get started in minutes with our guided setup process.\"\n    videoSrc=\"https://cache.shipixen.com/features/8-customize-pages.mp4\"\n    videoPoster=\"/static/images/backdrop-8.webp\"\n  />\n  <LandingProductVideoFeature\n    title=\"Intuitive Interface\"\n    description=\"Navigate with ease through our user-friendly platform.\"\n    videoSrc=\"https://cache.shipixen.com/features/11-pricing-page-builder.mp4\"\n    videoPoster=\"/static/images/backdrop-8.webp\"\n  />\n</LandingProductSteps>\n```\n\n\n\n### With Background Effect\n\n\n\n```jsx\nimport { LandingProductVideoFeature } from '@/components/landing/LandingProductVideoFeature';\nimport { LandingDotParticleCtaBg } from '@/components/landing/cta-backgrounds/LandingDotParticleCtaBg';\n\n<LandingProductVideoFeature\n  title=\"The wait is over\"\n  description=\"Give your project the home it deserves. Your users will love you for it.\"\n  videoSrc=\"https://cache.shipixen.com/features/3-theme-and-logo.mp4\"\n  videoPoster=\"/static/images/backdrop-5.webp\"\n  effectComponent={<LandingDotParticleCtaBg />}\n/>;\n```\n\n\n\n\n### With Leading Pill\n\n\n\n```jsx\nimport { LandingProductVideoFeature } from '@/components/landing/LandingProductVideoFeature';\n\n<LandingProductVideoFeature\n  title=\"The wait is over\"\n  description=\"Give your project the home it deserves. Your users will love you for it.\"\n  videoSrc=\"https://cache.shipixen.com/features/3-theme-and-logo.mp4\"\n  videoPoster=\"/static/images/backdrop-5.webp\"\n  leadingComponent={\n    <LandingLeadingPill\n      withBorder={false}\n      withBackground={true}\n      backgroundVariant=\"primary\"\n      leftComponent={<SparklesIcon className=\"w-4 h-4\" />}\n    >\n      Join today\n    </LandingLeadingPill>\n  }\n/>;\n```\n\n\n</Examples>\n\n<PropsReference>\n| Prop Name                                                                                                                             | Prop Type                         | Required | Default     |\n| ------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------- | -------- | ----------- |\n| **children** <Tippy>React nodes to be rendered within the component (below the text on smaller screen, right side on larger).</Tippy> | `React.ReactNode` ǀ `string`      | No       | -           |\n| **className** <Tippy>Additional CSS classes to apply to the component.</Tippy>                                                        | `string`                          | No       | -           |\n| **innerClassName** <Tippy>Additional CSS classes to apply to the inner container of the component.</Tippy>                            | `string`                          | No       | -           |\n| **title** <Tippy>Title of the section.</Tippy>                                                                                        | `string` ǀ `React.ReactNode`      | No       | -           |\n| **titleComponent** <Tippy>Custom React component for the title.</Tippy>                                                               | `React.ReactNode`                 | No       | -           |\n| **description** <Tippy>Description of the section.</Tippy>                                                                            | `string` ǀ `React.ReactNode`      | No       | -           |\n| **descriptionComponent** <Tippy>Custom React component for the description.</Tippy>                                                   | `React.ReactNode`                 | No       | -           |\n| **leadingComponent** <Tippy>Custom React component to render as the leading component.</Tippy>                                         | `React.ReactNode`                                                        | No       | -           |\n| **textPosition** <Tippy>Position of the text content. Can be `'center'` or `'left'`.</Tippy>                                          | `'center'` ǀ `'left'`             | No       | `'left'`    |\n| **videoSrc** <Tippy>Source URL for the video.</Tippy>                                                                                 | `string`                          | No       | -           |\n| **videoPoster** <Tippy>URL for the poster image of the video.</Tippy>                                                                 | `string`                          | No       | -           |\n| **videoPosition** <Tippy>Position of the video. Can be `'left'`, `'right'`, or `'center'`.</Tippy>                                    | `'left'` ǀ `'right'` ǀ `'center'` | No       | `'right'`   |\n| **videoMaxWidth** <Tippy>Maximum width of the video.</Tippy>                                                                          | `string`                          | No       | `'none'`    |\n| **autoPlay** <Tippy>Specifies whether the video should automatically start playing.</Tippy>                                           | `boolean`                         | No       | -           |\n| **controls** <Tippy>Specifies whether the video player should display controls.</Tippy>                                               | `boolean`                         | No       | `false`     |\n| **zoomOnHover** <Tippy>Specifies whether the video should zoom on hover.</Tippy>                                                      | `boolean`                         | No       | `false`     |\n| **minHeight** <Tippy>Minimum height of the section.</Tippy>                                                                           | `number`                          | No       | `350`       |\n| **withBackground** <Tippy>Specifies whether the section should have a background.</Tippy>                                             | `boolean`                         | No       | `false`     |\n| **withBackgroundGlow** <Tippy>Specifies whether the section should have a glowing background.</Tippy>                                 | `boolean`                         | No       | `false`     |\n| **variant** <Tippy>Variation style of the section. Can be `'primary'` or `'secondary'`.</Tippy>                                       | `'primary'` ǀ `'secondary'`       | No       | `'primary'` |\n| **backgroundGlowVariant** <Tippy>Variation style of the glowing background.</Tippy>                                                   | `'primary'` ǀ `'secondary'`       | No       | -           |\n| **effectComponent** <Tippy>Custom React component to be displayed as the background effect.</Tippy>                                    | `React.ReactNode`                                                        | No       | -           |\n| **effectClassName** <Tippy>Additional CSS classes to apply to the background effect.</Tippy>                                        | `string`                                                                 | No       | -           |\n</PropsReference>"}, "Rating": {"description": "Use this component to show a rating with stars.", "api": "<Usage>\n```jsx\nimport { LandingRating } from '@/components/landing/rating/LandingRating';\n```\n\n```jsx\n<LandingRating />\n```\n</Usage>\n\n<Examples>\n### With Custom Rating\n\n\n\n```jsx\nimport { LandingRating } from '@/components/landing/rating/LandingRating';\n\n<LandingRating rating={4} />;\n```\n\n\n\n### With Partial Rating\n\n\n\n```jsx\nimport { LandingRating } from '@/components/landing/rating/LandingRating';\n\n<LandingRating rating={4.3} />;\n```\n\n\n\n### With Custom Size\n\n\n\n```jsx\nimport { LandingRating } from '@/components/landing/rating/LandingRating';\n\n<LandingRating rating={4.3} size=\"large\" />;\n```\n\n\n\n### With Custom Max Rating\n\n\n\n```jsx\nimport { LandingRating } from '@/components/landing/rating/LandingRating';\n\n<LandingRating rating={4.3} maxRating={6} />;\n```\n\n\n</Examples>\n\n<PropsReference>\n| Prop Name                                                            | Prop Type                         | Required | Default    |\n| -------------------------------------------------------------------- | --------------------------------- | -------- | ---------- |\n| **rating** <Tippy>Number representing the current rating.</Tippy>    | `number`                          | No       | 5          |\n| **maxRating** <Tippy>Number representing the maximum rating.</Tippy> | `number`                          | No       | 5          |\n| **size** <Tippy>Size of the rating stars.</Tippy>                    | `'small'   ǀ 'medium'  ǀ 'large'` | No       | `'medium'` |\n</PropsReference>"}, "SaleCta": {"description": "Use this to prompt users to take action, such as signing up for a trial or buying a product. <br /> This can be used to break up longer pages and increase conversion as users scroll down and get past your primary CTA.", "api": "<Usage>\n```jsx\nimport { LandingSaleCtaSection } from '@/components/landing/cta/LandingSaleCta';\n```\n\n```\n<LandingSaleCtaSection\n  title=\"Ready to get started?\"\n  description={\n    'Pre-order today and get a 50% discount on the final price for the first 3 months. No credit card required.'\n  }\n  ctaHref=\"https://gum.co/product\"\n  ctaLabel=\"Pre-order for $49\"\n/>\n```\n</Usage>\n\n<Examples>\n### Background Customization/Variant\n\n\n\n```jsx\nimport { LandingSaleCtaSection } from '@/components/landing/cta/LandingSaleCta';\n\n<LandingSaleCtaSection\n  withBackground\n  variant=\"secondary\"\n  title=\"Ready to get started?\"\n  description=\"Pre-order today and get a 50% discount on the final price for the first 3 months. No credit card required.\"\n  ctaHref=\"https://gum.co/product\"\n  ctaLabel=\"Pre-order for $49\"\n/>;\n```\n\n\n\n### Secondary Call to Action\n\n\n\n```jsx\nimport { LandingSaleCtaSection } from '@/components/landing/cta/LandingSaleCta';\n\n<LandingSaleCtaSection\n  withBackground\n  title=\"Ready to get started?\"\n  description='Pre-order today and get a 50% discount on the final price for the first 3 months. No credit card required.'\n  ctaHref='https://gum.co/product'\n  ctaLabel='Pre-order for $49'\n  secondaryCtaHref='https://gum.co/learn-more'\n  secondaryCtaLabel='Learn more'\n/>\n```\n\n\n\n### Custom CTAs\n\n\n\n```jsx\nimport { LandingSaleCtaSection } from '@/components/landing/cta/LandingSaleCta';\nimport { Button } from '@/components/ui/button';\nimport { LandingDiscount } from '@/components/landing/discount/LandingDiscount';\n\n<LandingSaleCtaSection\n  withBackground\n  title=\"Ready to get started?\"\n  description='Pre-order today and get a 50% discount on the final price for the first 3 months. No credit card required.'\n  variant=\"secondary\"\n>\n  <Button size=\"xl\" variant=\"secondary\" asChild>\n    <a href=\"#\">Buy Now</a>\n  </Button>\n\n  <Button size=\"xl\" variant=\"outlineSecondary\">\n    <a href=\"#\">Learn More</a>\n  </Button>\n\n  <LandingDiscount\n    className=\"w-full\"\n    discountValueText=\"$350 off\"\n    discountDescriptionText=\"for the first 10 customers (2 left)\"\n  />\n</LandingSaleCtaSection>\n```\n\n\n\n### Background Glow\n\n\n\n```jsx\nimport { LandingSaleCtaSection } from '@/components/landing/cta/LandingSaleCta';\n\n<LandingSaleCtaSection\n  withBackgroundGlow\n  variant=\"secondary\"\n  backgroundGlowVariant=\"secondary\"\n  title=\"Ready to get started?\"\n  description=\"Pre-order today and get a 50% discount on the final price for the first 3 months. No credit card required.\"\n  ctaHref=\"https://gum.co/product\"\n  ctaLabel=\"Pre-order for $49\"\n/>;\n```\n\n\n</Examples>\n\n<PropsReference>\n| Prop Name                                                                                                            | Prop Type                    | Required | Default     |\n| -------------------------------------------------------------------------------------------------------------------- | ---------------------------- | -------- | ----------- |\n| **children** <Tippy>React nodes to be rendered within the component.</Tippy>                                         | `React.ReactNode`            | No       | -           |\n| **className**                                                                                                        | `string`                     | No       | -           |\n| **title** <Tippy>A string or React nodes representing the title of the section.</Tippy>                              | `string` ǀ `React.ReactNode` | No       | -           |\n| **titleComponent** <Tippy>A React node representing the title of the section.</Tippy>                                | `React.ReactNode`            | No       | -           |\n| **description** <Tippy>A string or React nodes representing the description of the section.</Tippy>                  | `string` ǀ `React.ReactNode` | No       | -           |\n| **descriptionComponent** <Tippy>A React node representing the description of the section.</Tippy>                    | `React.ReactNode`            | No       | -           |\n| **footerComponent** <Tippy>A React node to be rendered as the footer of the section.</Tippy>                         | `React.ReactNode`            | No       | -           |\n| **ctaHref**                                                                                                          | `string`                     | No       | `'#'`       |\n| **ctaLabel**                                                                                                         | `string`                     | No       | -           |\n| **secondaryCtaHref**                                                                                                 | `string`                     | No       | `'#'`       |\n| **secondaryCtaLabel**                                                                                                | `string`                     | No       | -           |\n| **withBackground** <Tippy>Indicates whether the section should have a background or not.</Tippy>                     | `boolean`                    | No       | `false`     |\n| **withBackgroundGlow** <Tippy>Indicates whether the section should have a glowing background or not.</Tippy>         | `boolean`                    | No       | `false`     |\n| **variant** <Tippy>The variant of the section. Can be `'primary'` or `'secondary'`.</Tippy>                          | `'primary'` ǀ `'secondary'`  | No       | `'primary'` |\n| **backgroundGlowVariant** <Tippy>The variant of the glowing background. Can be `'primary'` or `'secondary'`.</Tippy> | `'primary'` ǀ `'secondary'`  | No       | `'primary'` |\n</PropsReference>"}, "Showcase": {"description": "This component displays a grid of logos or images, showcasing the companies that use the product, integrations, etc.", "api": "<Usage>\n```jsx\nimport {\n  FigmaIcon,\n  TwitchIcon,\n  ChromeIcon,\n  InstagramIcon,\n  TwitterIcon,\n  FramerIcon,\n  GithubIcon,\n  SlackIcon,\n} from 'lucide-react';\nimport { LandingShowcase } from '@/components/landing/showcase/LandingShowcase';\nimport { LandingShowcaseItem } from '@/components/landing/showcase/LandingShowcaseItem';\n```\n\n```jsx\n<LandingShowcase\n  title=\"Import with ease\"\n  description=\"All your video assets in one platform. Import your existing footage from any device with a click.\"\n>\n  <LandingShowcaseItem>\n    <FigmaIcon className=\"w-10 h-10\" />\n  </LandingShowcaseItem>\n\n  <LandingShowcaseItem>\n    <TwitchIcon className=\"w-10 h-10\" />\n  </LandingShowcaseItem>\n\n  <LandingShowcaseItem>\n    <ChromeIcon className=\"w-10 h-10\" />\n  </LandingShowcaseItem>\n\n  <LandingShowcaseItem>\n    <InstagramIcon className=\"w-10 h-10\" />\n  </LandingShowcaseItem>\n\n  <LandingShowcaseItem>\n    <TwitterIcon className=\"w-10 h-10\" />\n  </LandingShowcaseItem>\n\n  <LandingShowcaseItem>\n    <FramerIcon className=\"w-10 h-10\" />\n  </LandingShowcaseItem>\n\n  <LandingShowcaseItem>\n    <GithubIcon className=\"w-10 h-10\" />\n  </LandingShowcaseItem>\n\n  <LandingShowcaseItem>\n    <SlackIcon className=\"w-10 h-10\" />\n  </LandingShowcaseItem>\n</LandingShowcase>\n```\n</Usage>\n\n<Examples>\n### Text Position\n\n\n\n```jsx\nimport {\n  FigmaIcon,\n  TwitchIcon,\n  ChromeIcon,\n  InstagramIcon,\n  TwitterIcon,\n  FramerIcon,\n  GithubIcon,\n  SlackIcon,\n} from 'lucide-react';\nimport { LandingShowcase } from '@/components/landing/showcase/LandingShowcase';\nimport { LandingShowcaseItem } from '@/components/landing/showcase/LandingShowcaseItem';\n\n<LandingShowcase\n  title=\"Import with ease\"\n  description=\"All your video assets in one platform. Import your existing footage from any device with a click.\"\n  textPosition=\"right\"\n>\n  <LandingShowcaseItem>\n    <FigmaIcon className=\"w-10 h-10\" />\n  </LandingShowcaseItem>\n\n  <LandingShowcaseItem>\n    <TwitchIcon className=\"w-10 h-10\" />\n  </LandingShowcaseItem>\n\n  <LandingShowcaseItem>\n    <ChromeIcon className=\"w-10 h-10\" />\n  </LandingShowcaseItem>\n\n  <LandingShowcaseItem>\n    <InstagramIcon className=\"w-10 h-10\" />\n  </LandingShowcaseItem>\n\n  <LandingShowcaseItem>\n    <TwitterIcon className=\"w-10 h-10\" />\n  </LandingShowcaseItem>\n\n  <LandingShowcaseItem>\n    <FramerIcon className=\"w-10 h-10\" />\n  </LandingShowcaseItem>\n\n  <LandingShowcaseItem>\n    <GithubIcon className=\"w-10 h-10\" />\n  </LandingShowcaseItem>\n\n  <LandingShowcaseItem>\n    <SlackIcon className=\"w-10 h-10\" />\n  </LandingShowcaseItem>\n</LandingShowcase>;\n```\n\n\n\n### Center Text Position\n\n\n\n```jsx\nimport {\n  FigmaIcon,\n  TwitchIcon,\n  ChromeIcon,\n  InstagramIcon,\n  TwitterIcon,\n  FramerIcon,\n  GithubIcon,\n  SlackIcon,\n} from 'lucide-react';\nimport { LandingShowcase } from '@/components/landing/showcase/LandingShowcase';\nimport { LandingShowcaseItem } from '@/components/landing/showcase/LandingShowcaseItem';\n\n<LandingShowcase\n  title=\"Import with ease\"\n  description=\"All your video assets in one platform. Import your existing footage from any device with a click.\"\n  textPosition=\"center\"\n>\n  <LandingShowcaseItem>\n    <FigmaIcon className=\"w-10 h-10\" />\n  </LandingShowcaseItem>\n\n  <LandingShowcaseItem>\n    <TwitchIcon className=\"w-10 h-10\" />\n  </LandingShowcaseItem>\n\n  <LandingShowcaseItem>\n    <ChromeIcon className=\"w-10 h-10\" />\n  </LandingShowcaseItem>\n\n  <LandingShowcaseItem>\n    <InstagramIcon className=\"w-10 h-10\" />\n  </LandingShowcaseItem>\n\n  <LandingShowcaseItem>\n    <TwitterIcon className=\"w-10 h-10\" />\n  </LandingShowcaseItem>\n\n  <LandingShowcaseItem>\n    <FramerIcon className=\"w-10 h-10\" />\n  </LandingShowcaseItem>\n\n  <LandingShowcaseItem>\n    <GithubIcon className=\"w-10 h-10\" />\n  </LandingShowcaseItem>\n\n  <LandingShowcaseItem>\n    <SlackIcon className=\"w-10 h-10\" />\n  </LandingShowcaseItem>\n</LandingShowcase>\n```\n\n\n\n### With Background\n\n\n\n```jsx\nimport {\n  FigmaIcon,\n  TwitchIcon,\n  ChromeIcon,\n  InstagramIcon,\n  TwitterIcon,\n  FramerIcon,\n  GithubIcon,\n  SlackIcon,\n} from 'lucide-react';\nimport { LandingShowcase } from '@/components/landing/showcase/LandingShowcase';\nimport { LandingShowcaseItem } from '@/components/landing/showcase/LandingShowcaseItem';\n\n<LandingShowcase\n  title=\"Import with ease\"\n  description=\"All your video assets in one platform. Import your existing footage from any device with a click.\"\n  withBackground\n  variant=\"secondary\"\n>\n  <LandingShowcaseItem>\n    <FigmaIcon className=\"w-10 h-10\" />\n  </LandingShowcaseItem>\n\n  <LandingShowcaseItem>\n    <TwitchIcon className=\"w-10 h-10\" />\n  </LandingShowcaseItem>\n\n  <LandingShowcaseItem>\n    <ChromeIcon className=\"w-10 h-10\" />\n  </LandingShowcaseItem>\n\n  <LandingShowcaseItem>\n    <InstagramIcon className=\"w-10 h-10\" />\n  </LandingShowcaseItem>\n\n  <LandingShowcaseItem>\n    <TwitterIcon className=\"w-10 h-10\" />\n  </LandingShowcaseItem>\n\n  <LandingShowcaseItem>\n    <FramerIcon className=\"w-10 h-10\" />\n  </LandingShowcaseItem>\n\n  <LandingShowcaseItem>\n    <GithubIcon className=\"w-10 h-10\" />\n  </LandingShowcaseItem>\n\n  <LandingShowcaseItem>\n    <SlackIcon className=\"w-10 h-10\" />\n  </LandingShowcaseItem>\n</LandingShowcase>;\n```\n\n\n\n### Background Glow\n\n\n\n```jsx\nimport {\n  FigmaIcon,\n  TwitchIcon,\n  ChromeIcon,\n  InstagramIcon,\n  TwitterIcon,\n  FramerIcon,\n  GithubIcon,\n  SlackIcon,\n} from 'lucide-react';\nimport { LandingShowcase } from '@/components/landing/showcase/LandingShowcase';\nimport { LandingShowcaseItem } from '@/components/landing/showcase/LandingShowcaseItem';\n\n<LandingShowcase\n  title=\"Import with ease\"\n  description=\"All your video assets in one platform. Import your existing footage from any device with a click.\"\n  withBackground\n  variant=\"secondary\"\n  withBackgroundGlow\n  backgroundGlowVariant=\"secondary\"\n>\n  <LandingShowcaseItem>\n    <FigmaIcon className=\"w-10 h-10\" />\n  </LandingShowcaseItem>\n\n  <LandingShowcaseItem>\n    <TwitchIcon className=\"w-10 h-10\" />\n  </LandingShowcaseItem>\n\n  <LandingShowcaseItem>\n    <ChromeIcon className=\"w-10 h-10\" />\n  </LandingShowcaseItem>\n\n  <LandingShowcaseItem>\n    <InstagramIcon className=\"w-10 h-10\" />\n  </LandingShowcaseItem>\n\n  <LandingShowcaseItem>\n    <TwitterIcon className=\"w-10 h-10\" />\n  </LandingShowcaseItem>\n\n  <LandingShowcaseItem>\n    <FramerIcon className=\"w-10 h-10\" />\n  </LandingShowcaseItem>\n\n  <LandingShowcaseItem>\n    <GithubIcon className=\"w-10 h-10\" />\n  </LandingShowcaseItem>\n\n  <LandingShowcaseItem>\n    <SlackIcon className=\"w-10 h-10\" />\n  </LandingShowcaseItem>\n</LandingShowcase>;\n```\n\n\n</Examples>\n\n<PropsReference>\n| Prop Name                                                                                                                             | Prop Type                    | Required | Default     |\n| ------------------------------------------------------------------------------------------------------------------------------------- | ---------------------------- | -------- | ----------- |\n| **children** <Tippy>React nodes to be rendered within the component (below the text on smaller screen, right side on larger).</Tippy> | `React.ReactNode`            | No       | -           |\n| **innerClassName** <Tippy>Additional class names for the inner div element.</Tippy>                                                   | `string`                     | No       | -           |\n| **title** <Tippy>The title text or React node to be displayed in the component.</Tippy>                                               | `string` ǀ `React.ReactNode` | No       | -           |\n| **titleComponent** <Tippy>React node to be rendered as the title, if `title` is not provided.</Tippy>                                 | `React.ReactNode`            | No       | -           |\n| **description** <Tippy>The description text or React node to be displayed in the component.</Tippy>                                   | `string` ǀ `React.ReactNode` | No       | -           |\n| **descriptionComponent** <Tippy>React node to be rendered as the description, if `description` is not provided.</Tippy>               | `React.ReactNode`            | No       | -           |\n| **textPosition** <Tippy>Position of the text within the component. Can be 'left' or 'right' or 'center'.</Tippy>                                  | `'left'` ǀ `'right'`         | No       | `'left'`    |\n| **withBackground** <Tippy>Flag indicating whether the background should be applied.</Tippy>                                           | `boolean`                    | No       | `false`     |\n| **withBackgroundGlow** <Tippy>Flag indicating whether the background glow should be applied.</Tippy>                                  | `boolean`                    | No       | `false`     |\n| **variant** <Tippy>Variant of the background style. Can be 'primary' or 'secondary'.</Tippy>                                          | `'primary'` ǀ `'secondary'`  | No       | `'primary'` |\n| **backgroundGlowVariant** <Tippy>Variant of the background glow style. Can be 'primary' or 'secondary'.</Tippy>                       | `'primary'` ǀ `'secondary'`  | No       | `'primary'` |\n</PropsReference>"}, "SocialProofBandItem": {"description": "A component meant to be used in the landing page, as a child of Social Proof Band. Shows a social proof/key feature/milestone item with an optional graphic.", "api": "<Usage>\n```jsx\nimport { LandingSocialProofBandItem } from '@/components/landing/social-proof/LandingSocialProofBandItem';\n```\n\n```jsx\n<LandingSocialProofBandItem>\n  100% encrypted and secure\n</LandingSocialProofBandItem>\n```\n</Usage>\n\n<Examples>\n### With custom graphics\n\n\n\n```jsx\nimport { LandingSocialProofBandItem } from '@/components/landing/social-proof/LandingSocialProofBandItem';\n\n<LandingSocialProofBandItem graphic=\"magic\">\n  Easy setup\n</LandingSocialProofBandItem>;\n```\n\n\n\n\n\n```jsx\nimport { LandingSocialProofBandItem } from '@/components/landing/social-proof/LandingSocialProofBandItem';\n\n<LandingSocialProofBandItem graphic=\"rating\">\n  Trusted by 1000+ customers\n</LandingSocialProofBandItem>;\n```\n\n\n\n\n\n```jsx\nimport { LandingSocialProofBandItem } from '@/components/landing/social-proof/LandingSocialProofBandItem';\n\n<LandingSocialProofBandItem graphic=\"gift\">\n  20% off today\n</LandingSocialProofBandItem>;\n```\n\n\n</Examples>\n\n<PropsReference>\n| Prop Name                                                                                                                             | Prop Type                                                                 | Required | Default       |\n| ------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------- | -------- | ------------- |\n| **children** <Tippy>React nodes to be rendered within the component (below the text on smaller screen, right side on larger).</Tippy> | `React.ReactNode` ǀ `string`                                              | Yes      | -             |\n| **graphic** <Tippy>Specifies the type of graphic to display alongside the content.</Tippy>                                            | `'none'` ǀ `'checkmark'` ǀ `'gift'` ǀ `'magic'` ǀ `'trophy'` ǀ `'rating'` ǀ `'zap'` ǀ `'rocket'` ǀ `'time'` | No       | `'checkmark'` |\n| **customGraphic** <Tippy>Custom SVG graphic to display alongside the content.</Tippy>                                                 | `React.ReactNode`                                                         | No       | -             |\n</PropsReference>"}, "SocialProofBand": {"description": "Use this to highlight key features or social proof. This is usually placed at the top of the page, but you can also use it in between sections or below your primary CTA.", "api": "<Usage>\n```jsx\nimport { LandingSocialProofBand } from '@/components/landing/social-proof/LandingSocialProofBand';\nimport { LandingSocialProofBandItem } from '@/components/landing/social-proof/LandingSocialProofBandItem';\n```\n\n```jsx\n<LandingSocialProofBand>\n  <LandingSocialProofBandItem>\n    100% encrypted and secure\n  </LandingSocialProofBandItem>\n\n  <LandingSocialProofBandItem>24/7 customer support</LandingSocialProofBandItem>\n\n  <LandingSocialProofBandItem>\n    99% customer satisfaction\n  </LandingSocialProofBandItem>\n</LandingSocialProofBand>\n```\n</Usage>\n\n<Examples>\n### Inverted\n\n\n\n```jsx\nimport { LandingSocialProofBand } from '@/components/landing/social-proof/LandingSocialProofBand';\nimport { LandingSocialProofBandItem } from '@/components/landing/social-proof/LandingSocialProofBandItem';\n\n<LandingSocialProofBand invert={true}>\n  <LandingSocialProofBandItem>\n    100% encrypted and secure\n  </LandingSocialProofBandItem>\n\n  <LandingSocialProofBandItem>24/7 customer support</LandingSocialProofBandItem>\n\n  <LandingSocialProofBandItem>\n    99% customer satisfaction\n  </LandingSocialProofBandItem>\n</LandingSocialProofBand>;\n```\n\n\n\n### With customized icons\n\n\n\n```jsx\nimport { LandingSocialProofBand } from '@/components/landing/social-proof/LandingSocialProofBand';\nimport { LandingSocialProofBandItem } from '@/components/landing/social-proof/LandingSocialProofBandItem';\n\n<LandingSocialProofBand invert={true}>\n  <LandingSocialProofBandItem graphic=\"magic\">\n    AI powered\n  </LandingSocialProofBandItem>\n\n  <LandingSocialProofBandItem graphic=\"gift\">\n    30% off this week\n  </LandingSocialProofBandItem>\n\n  <LandingSocialProofBandItem graphic=\"rating\">\n    Trusted by 1000+ customers\n  </LandingSocialProofBandItem>\n\n  <LandingSocialProofBandItem graphic=\"trophy\">\n    Most popular app in US\n  </LandingSocialProofBandItem>\n</LandingSocialProofBand>;\n```\n\n\n\n### With Primary Image CTA\n\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryImageCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\nimport { LandingDiscount } from '@/components/landing/discount/LandingDiscount';\nimport { LandingProductHuntAward } from '@/components/landing/social-proof/LandingProductHuntAward';\nimport { LandingSocialProofBand } from '@/components/landing/social-proof/LandingSocialProofBand';\nimport { LandingSocialProofBandItem } from '@/components/landing/social-proof/LandingSocialProofBandItem';\n\n<LandingSocialProofBand>\n  <LandingSocialProofBandItem>\n    100% encrypted and secure\n  </LandingSocialProofBandItem>\n\n  <LandingSocialProofBandItem>24/7 customer support</LandingSocialProofBandItem>\n\n  <LandingSocialProofBandItem>\n    99% customer satisfaction\n  </LandingSocialProofBandItem>\n</LandingSocialProofBand>\n\n<LandingPrimaryImageCtaSection\n  title=\"Landing page in minutes\"\n  description=\"Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back.\"\n  imageSrc=\"/static/images/shipixen/product/1.webp\"\n  imageAlt=\"Sample image\"\n  leadingComponent={<LandingProductHuntAward />}\n  withBackground\n>\n  <Button size=\"xl\" asChild>\n    <a href=\"#\">\n      Buy now\n    </a>\n  </Button>\n\n  <Button size=\"xl\" variant=\"outlinePrimary\">\n    <a href=\"#\">Read more</a>\n  </Button>\n</LandingPrimaryImageCtaSection>\n```\n\n\n</Examples>\n\n<PropsReference>\n| Prop Name                                                                                                                             | Prop Type                    | Required | Default |\n| ------------------------------------------------------------------------------------------------------------------------------------- | ---------------------------- | -------- | ------- |\n| **children** <Tippy>React nodes to be rendered within the component (below the text on smaller screen, right side on larger).</Tippy> | `React.ReactNode` ǀ `string` | Yes      | -       |\n| **invert** <Tippy>Whether to invert the color scheme of the component.</Tippy>                                                        | `boolean`                    | No       | -       |\n| **variant** <Tippy>The variant of the component.</Tippy>                                                                              | `'default'` ǀ `'primary'` ǀ `'secondary'` | No       | `'default'` |\n</PropsReference>"}, "SocialProof": {"description": "Use this to show proof of existing, happy customers & increase trust. Shows social proof with avatars, number of users and an optional rating.", "api": "<Usage>\n```jsx\nimport { LandingSocialProof } from '@/components/landing/social-proof/LandingSocialProof';\n```\n\n```jsx\nconst avatarItems = [\n  {\n    imageSrc: '/static/images/people/1.webp',\n    name: '<PERSON>',\n  },\n  {\n    imageSrc: '/static/images/people/2.webp',\n    name: '<PERSON>',\n  },\n  {\n    imageSrc: '/static/images/people/3.webp',\n    name: '<PERSON>',\n  },\n  {\n    imageSrc: '/static/images/people/4.webp',\n    name: '<PERSON>',\n  },\n  {\n    imageSrc: '/static/images/people/5.webp',\n    name: '<PERSON>',\n  },\n];\n```\n\n```jsx\n<LandingSocialProof numberOfUsers={99} avatarItems={avatarItems} />\n```\n</Usage>\n\n<Examples>\n### With Rating\n\n\n\n```jsx\nimport { LandingSocialProof } from '@/components/landing/social-proof/LandingSocialProof';\n\nconst avatarItems = [\n  {\n    imageSrc: '/static/images/people/1.webp',\n    name: '<PERSON>',\n  },\n  {\n    imageSrc: '/static/images/people/2.webp',\n    name: '<PERSON>',\n  },\n  {\n    imageSrc: '/static/images/people/3.webp',\n    name: 'Alice Doe',\n  },\n]\n\n<LandingSocialProof\n  showRating\n  numberOfUsers={99}\n  avatarItems={avatarItems}\n/>\n```\n\n\n\n### With Custom Suffix Text\n\n\n\n```jsx\nimport { LandingSocialProof } from '@/components/landing/social-proof/LandingSocialProof';\n\nconst avatarItems = [\n  {\n    imageSrc: '/static/images/people/1.webp',\n    name: 'John Doe',\n  },\n  {\n    imageSrc: '/static/images/people/2.webp',\n    name: 'Jane Doe',\n  },\n  {\n    imageSrc: '/static/images/people/3.webp',\n    name: 'Alice Doe',\n  },\n]\n\n<LandingSocialProof\n  suffixText=\"experienced developers\"\n  showRating\n  numberOfUsers={99}\n  avatarItems={avatarItems}\n/>\n```\n\n\n\n### Without Hover Animation\n\n\n\n```jsx\nimport { LandingSocialProof } from '@/components/landing/social-proof/LandingSocialProof';\n\nconst avatarItems = [\n  {\n    imageSrc: '/static/images/people/1.webp',\n    name: 'John Doe',\n  },\n  {\n    imageSrc: '/static/images/people/2.webp',\n    name: 'Jane Doe',\n  },\n  {\n    imageSrc: '/static/images/people/3.webp',\n    name: 'Alice Doe',\n  },\n  {\n    imageSrc: '/static/images/people/4.webp',\n    name: 'Bob Doe',\n  },\n  {\n    imageSrc: '/static/images/people/5.webp',\n    name: 'Eve Doe',\n  },\n]\n\n<LandingSocialProof\n  disableAnimation\n  numberOfUsers={12000}\n  avatarItems={avatarItems}\n/>\n```\n\n\n\n### With Primary Image Cta\n\n\n\n```jsx\nimport { Button } from '@/components/shared/ui/button';\nimport { LandingPrimaryImageCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\nimport { LandingDiscount } from '@/components/landing/discount/LandingDiscount';\nimport { LandingProductHuntAward } from '@/components/landing/social-proof/LandingProductHuntAward';\nimport { LandingSocialProof } from '@/components/landing/social-proof/LandingSocialProof';\n\n<LandingPrimaryImageCtaSection\n  title=\"Landing page in minutes\"\n  description=\"Get 10x more done with Shadcn UI, React & Next.js, and say goodbye to repetitive tasks. You'll never go back.\"\n  imageSrc=\"/static/images/shipixen/product/1.webp\"\n  imageAlt=\"Sample image\"\n  withBackground\n  withBackgroundGlow\n  leadingComponent={<LandingProductHuntAward />}\n>\n  <Button size=\"xl\" asChild>\n    <a href=\"#\">Buy now</a>\n  </Button>\n\n  <Button size=\"xl\" variant=\"outlinePrimary\">\n    <a href=\"#\">Read more</a>\n  </Button>\n\n  <LandingSocialProof\n    className=\"w-full mt-12\"\n    showRating\n    numberOfUsers={99}\n    avatarItems={[\n      {\n        imageSrc: '/static/images/people/1.webp',\n        name: 'John Doe',\n      },\n      {\n        imageSrc: '/static/images/people/2.webp',\n        name: 'Jane Doe',\n      },\n      {\n        imageSrc: '/static/images/people/3.webp',\n        name: 'Alice Doe',\n      },\n    ]}\n  />\n</LandingPrimaryImageCtaSection>;\n```\n\n\n</Examples>\n\n<PropsReference>\n| Prop Name                                                                                                     | Prop Type           | Required | Default       |\n| ------------------------------------------------------------------------------------------------------------- | ------------------- | -------- | ------------- |\n| **avatarItems** <Tippy>Array of objects representing social proof items with avatar images and names.</Tippy> | `SocialProofItem[]` | Yes      | -             |\n| **numberOfUsers** <Tippy>Number of users displayed as social proof.</Tippy>                                   | `number`            | Yes      | -             |\n| **suffixText** <Tippy>Text to be appended after the number of users, e.g., 'happy users'.</Tippy>             | `string`            | No       | 'happy users' |\n| **showRating** <Tippy>Boolean to determine whether to display the rating.</Tippy>                             | `boolean`           | No       | -             |\n| **disableAnimation** <Tippy>Boolean to disable animation.</Tippy>                                             | `boolean`           | No       | -             |\n\n```ts\nexport interface SocialProofItem {\n  imageSrc: string;\n  name: string;\n}\n```\n</PropsReference>"}, "Stats": {"description": "The Stats Section is a versatile component for showcasing key metrics, accomplishments, or any numerical data in an elegant grid layout. It's designed to highlight important statistics with clear visual hierarchy, making them stand out on your landing page.", "api": "<Usage>\nImport the component:\n\n```jsx\nimport { LandingStatsSection } from '@/components/landing/stats/LandingStatsSection';\n```\n\nBasic implementation:\n\n```jsx\n<LandingStatsSection\n  stats={[\n    { value: '150+', label: 'apps', description: 'Over 150 apps successfully delivered.' },\n    { value: '9', label: 'members', description: 'Our core team consists of 9 experts.' },\n    { value: '300+', label: 'devs', description: 'We collaborate with 300+ creative devs.' }\n  ]}\n/>\n```\n</Usage>\n\n<Examples>\n### With Title and Description\n\n\n\n```jsx\nimport { LandingStatsSection } from '@/components/landing/stats/LandingStatsSection';\n\n<LandingStatsSection\n  title=\"Our Achievements\"\n  description=\"We take pride in our accomplishments and the impact we've made across various apps and industries.\"\n  columnsDesktop={3}\n  hasBorders={true}\n  stats={[\n    { value: '150+', label: 'apps', description: 'Over 150 apps successfully delivered.' },\n    { value: '9', label: 'members', description: 'Our core team consists of 9 experts.' },\n    { value: '300+', label: 'devs', description: 'We collaborate with 300+ creative devs.' }\n  ]}\n/>\n```\n\n\n\n### Without Borders\n\n\n\n```jsx\nimport { LandingStatsSection } from '@/components/landing/stats/LandingStatsSection';\n\n<LandingStatsSection\n  columnsDesktop={3}\n  hasBorders={false}\n  stats={[\n    { value: '150+', label: 'apps', description: 'Over 150 apps successfully delivered.' },\n    { value: '9', label: 'members', description: 'Our core team consists of 9 experts.' },\n    { value: '300+', label: 'devs', description: 'We collaborate with 300+ creative devs.' }\n  ]}\n/>\n```\n\n\n\n### With Background\n\n\n\n```jsx\nimport { LandingStatsSection } from '@/components/landing/stats/LandingStatsSection';\n\n<LandingStatsSection\n  columnsDesktop={2}\n  withBackground={true}\n  variant=\"secondary\"\n  stats={[\n    { value: '150+', label: 'apps', description: 'Over 150 apps successfully delivered.' },\n    { value: '9', label: 'members', description: 'Our core team consists of 9 experts.' },\n    { value: '300+', label: 'devs', description: 'We collaborate with 300+ creative devs.' },\n    { value: '5+', label: 'awards', description: 'Recognized with 5+ awards and featured in industry publications.' }\n  ]}\n/>\n```\n\n\n\n### With Background Glow\n\n\n\n```jsx\nimport { LandingStatsSection } from '@/components/landing/stats/LandingStatsSection';\n\n<LandingStatsSection\n  columnsDesktop={2}\n  withBackground={true}\n  withBackgroundGlow={true}\n  backgroundGlowVariant=\"secondary\"\n  stats={[\n    { value: '150+', label: 'apps', description: 'Over 150 apps successfully delivered.' },\n    { value: '9', label: 'members', description: 'Our core team consists of 9 experts.' },\n    { value: '300+', label: 'devs', description: 'We collaborate with 300+ creative devs.' },\n    { value: '5+', label: 'awards', description: 'Recognized with 5+ awards and featured in industry publications.' }\n  ]}\n/>\n```\n\n\n\n### Different Column Layouts\n\nIt's possible to use different column layouts for desktop and mobile screens.\nFor example, you can use 4 columns on desktop and 1 column on mobile.\n\n\n\n```jsx\nimport { LandingStatsSection } from '@/components/landing/stats/LandingStatsSection';\n\n<LandingStatsSection\n  columnsDesktop={4}\n  columnsMobile={1}\n  stats={[\n    { value: '15+', label: 'apps', description: 'Projects delivered in the last 12 months.' },\n    { value: '9', label: 'people', description: 'That are part of our team.' },\n    { value: '30+', label: 'devs', description: 'Developers that we work with.' },\n    { value: '5+', label: 'prizes', description: 'Industry prizes since 2010.' }\n  ]}\n/>\n```\n\n\n</Examples>\n\n<PropsReference>\n### LandingStatsSection Props\n\n| Prop Name | Prop Type | Required | Default |\n| --- | --- | ----- | ---- |\n| **stats** <Tippy>Array of statistic objects to display</Tippy> | `Array<{ value: string; label?: string; description: string }>` | Yes | `[]` |\n| **className** <Tippy>Additional classes to apply to the section wrapper</Tippy> | `string` | No | `''` |\n| **innerClassName** <Tippy>Additional classes to apply to the inner container</Tippy> | `string` | No | `''` |\n| **title** <Tippy>Section title text</Tippy> | `string` | No | `undefined` |\n| **titleComponent** <Tippy>Custom component to replace the default title</Tippy> | `React.ReactNode` | No | `undefined` |\n| **description** <Tippy>Section description text</Tippy> | `string \\| React.ReactNode` | No | `undefined` |\n| **descriptionComponent** <Tippy>Custom component to replace the default description</Tippy> | `React.ReactNode` | No | `undefined` |\n| **variant** <Tippy>Visual style variant of the section</Tippy> | `'primary' \\| 'secondary'` \\| `'default'` | No | `'default'` |\n| **withBackground** <Tippy>Whether to display a background color</Tippy> | `boolean` | No | `false` |\n| **withBackgroundGlow** <Tippy>Whether to display a background glow effect</Tippy> | `boolean` | No | `false` |\n| **backgroundGlowVariant** <Tippy>Visual style variant of the background glow</Tippy> | `'primary' \\| 'secondary'` | No | `'primary'` |\n| **columnsDesktop** <Tippy>Number of columns on desktop screens</Tippy> | `2 \\| 3 \\| 4` | No | `3` |\n| **columnsMobile** <Tippy>Number of columns on mobile screens</Tippy> | `1 \\| 2` | No | `1` |\n| **hasBorders** <Tippy>Whether to display borders between stats</Tippy> | `boolean` | No | `true` |\n| **textPosition** <Tippy>Alignment of section text</Tippy> | `'center' \\| 'left'` | No | `'center'` |\n| **children** <Tippy>Additional content to display below the stats grid</Tippy> | `React.ReactNode` | No | `undefined` |\n\n### LandingStatItem Props\n\n| Prop Name | Prop Type | Required | Default |\n| --- | --- | ----- | ---- |\n| **value** <Tippy>The main statistic value to display</Tippy> | `string` | Yes | - |\n| **description** <Tippy>Description of the statistic</Tippy> | `string` | Yes | - |\n| **label** <Tippy>Optional label displayed next to the value</Tippy> | `string` | No | `undefined` |\n| **className** <Tippy>Additional classes to apply to the stat item wrapper</Tippy> | `string` | No | `''` |\n| **variant** <Tippy>Visual style variant of the stat item</Tippy> | `'primary' \\| 'secondary'` | No | `'primary'` |\n| **hasBorder** <Tippy>Whether to display borders around the item</Tippy> | `boolean` | No | `true` |\n\n### Stats Object Interface\n\n```ts\ninterface StatItem {\n  value: string;\n  label?: string;\n  description: string;\n}\n```\n</PropsReference>"}, "Team": {"description": "The Team Section component provides a beautiful and customizable way to showcase your team members on a landing page. It supports both array-based and component-based approaches, giving you flexibility in how you structure your code.", "api": "<Usage>\n```jsx\nimport { LandingTeamSection } from '@/components/landing/team/LandingTeamSection';\n```\n\n```jsx\n<LandingTeamSection\n  title=\"Meet Our Team\"\n  description=\"Our team is a tight-knit family of developers and visionaries, all bound by the same passion and enthusiasm.\"\n  members={[\n    { name: \"<PERSON>\", role: \"CEO & Founder\", imageSrc: \"/static/images/people/11.webp\" },\n    { name: \"<PERSON>\", role: \"<PERSON><PERSON>\", imageSrc: \"/static/images/people/2.webp\" },\n    { name: \"<PERSON>\", role: \"Lead Designer\", imageSrc: \"/static/images/people/12.webp\" }\n  ]}\n/>\n```\n</Usage>\n\n<Examples>\n### Centered Text Layout\n\n\n\n```jsx\nimport { LandingTeamSection } from '@/components/landing/team/LandingTeamSection';\n\n<LandingTeamSection\n  title=\"Our Amazing Team\"\n  description=\"Meet the talented individuals behind our success.\"\n  textPosition=\"center\"\n  members={[\n    { name: \"<PERSON>\", role: \"CEO & Founder\", imageSrc: \"/static/images/people/11.webp\" },\n    { name: \"<PERSON>\", role: \"<PERSON><PERSON>\", imageSrc: \"/static/images/people/2.webp\" },\n    { name: \"<PERSON>\", role: \"Lead Designer\", imageSrc: \"/static/images/people/12.webp\" }\n  ]}\n/>\n```\n\n\n\n### Secondary Variant\n\n\n\n```jsx\nimport { LandingTeamSection } from '@/components/landing/team/LandingTeamSection';\n\n<LandingTeamSection\n  title=\"Development Team\"\n  description=\"The talented engineers that build our product.\"\n  variant=\"secondary\"\n  withBackground={true}\n  members={[\n    { name: \"Lee Rob\", role: \"CEO & Founder\", imageSrc: \"/static/images/people/11.webp\" },\n    { name: \"David Chen\", role: \"CTO\", imageSrc: \"/static/images/people/2.webp\" },\n    { name: \"Alex Rivera\", role: \"Lead Designer\", imageSrc: \"/static/images/people/12.webp\" }\n  ]}\n/>\n```\n\n\n\n### Custom Team Member Styling\n\n\n\n```jsx\nimport { LandingTeamSection } from '@/components/landing/team/LandingTeamSection';\nimport { LandingTeamMember } from '@/components/landing/team/LandingTeamMember';\n\n<LandingTeamSection\n  title=\"Our Team\"\n  description=\"Meet the people who make it all happen.\"\n>\n  <LandingTeamMember\n    imageClassName=\"border-4 border-gray-200 dark:border-gray-800\"\n    member={{ name: \"Lee Rob\", role: \"CEO & Founder\", imageSrc: \"/static/images/people/11.webp\" }}\n  />\n  <LandingTeamMember\n    imageClassName=\"border-4 border-gray-200 dark:border-gray-800\"\n    member={{ name: \"David Chen\", role: \"CTO\", imageSrc: \"/static/images/people/2.webp\" }}\n  />\n  <LandingTeamMember\n    imageClassName=\"border-4 border-gray-200 dark:border-gray-800\"\n    member={{ name: \"Alex Rivera\", role: \"Lead Designer\", imageSrc: \"/static/images/people/12.webp\" }}\n  />\n</LandingTeamSection>\n```\n\n\n\n### With Background\n\n\n\n```jsx\nimport { LandingTeamSection } from '@/components/landing/team/LandingTeamSection';\n\n<LandingTeamSection\n  title=\"Meet Our Experts\"\n  description=\"The talented professionals who make everything possible.\"\n  withBackground={true}\n  members={[\n    { name: \"Lee Rob\", role: \"CEO & Founder\", imageSrc: \"/static/images/people/11.webp\" },\n    { name: \"David Chen\", role: \"CTO\", imageSrc: \"/static/images/people/2.webp\" },\n    { name: \"Alex Rivera\", role: \"Lead Designer\", imageSrc: \"/static/images/people/12.webp\" }\n  ]}\n/>\n```\n\n\n\n### With Background Glow\n\n\n\n```jsx\nimport { LandingTeamSection } from '@/components/landing/team/LandingTeamSection';\n\n<LandingTeamSection\n  title=\"Meet Our Experts\"\n  description=\"The talented professionals who make everything possible.\"\n  withBackgroundGlow={true}\n  members={[\n    { name: \"Lee Rob\", role: \"CEO & Founder\", imageSrc: \"/static/images/people/11.webp\" },\n    { name: \"David Chen\", role: \"CTO\", imageSrc: \"/static/images/people/2.webp\" },\n    { name: \"Alex Rivera\", role: \"Lead Designer\", imageSrc: \"/static/images/people/12.webp\" }\n  ]}\n/>\n```\n\n\n\n### Component-based Usage\n\nAlternatively, you can use individual `LandingTeamMember` components as children:\n\n\n\n```jsx\nimport { LandingTeamSection } from '@/components/landing/team/LandingTeamSection';\nimport { LandingTeamMember } from '@/components/landing/team/LandingTeamMember';\n\n<LandingTeamSection\n  title=\"Leadership Team\"\n  description=\"Meet the people driving our vision forward.\"\n>\n  <LandingTeamMember\n    member={{ name: \"Lee Rob\", role: \"CEO & Founder\", imageSrc: \"/static/images/people/11.webp\" }}\n  />\n  <LandingTeamMember\n    member={{ name: \"David Chen\", role: \"CTO\", imageSrc: \"/static/images/people/2.webp\" }}\n  />\n  <LandingTeamMember\n    member={{ name: \"Alex Rivera\", role: \"Lead Designer\", imageSrc: \"/static/images/people/12.webp\" }}\n  />\n</LandingTeamSection>\n```\n\n\n</Examples>\n\n<PropsReference>\n### LandingTeamSection\n\n| Prop Name | Prop Type | Required | Default |\n| --- | --- | ----- | ---- |\n| **className** <Tippy>Additional CSS classes for the section container</Tippy> | `string` | No | `-` |\n| **innerClassName** <Tippy>Additional CSS classes for the inner content container</Tippy> | `string` | No | `-` |\n| **title** <Tippy>Main title text for the team section</Tippy> | `string` | No | `'Our Members'` |\n| **titleComponent** <Tippy>Custom React component to replace the default title</Tippy> | `React.ReactNode` | No | `-` |\n| **description** <Tippy>Description text shown below the title</Tippy> | `string \\| React.ReactNode` | No | `'Our team is a tight-knit family of developers and visionaries, all bound by the same passion and enthusiasm.'` |\n| **descriptionComponent** <Tippy>Custom React component to replace the default description</Tippy> | `React.ReactNode` | No | `-` |\n| **members** <Tippy>Array of team members to display</Tippy> | `TeamMember[]` | No | `[]` |\n| **textPosition** <Tippy>Alignment of the title and description text</Tippy> | `'center' \\| 'left'` | No | `'left'` |\n| **withBackground** <Tippy>Whether to display a background color</Tippy> | `boolean` | No | `false` |\n| **withBackgroundGlow** <Tippy>Whether to display a glow effect in the background</Tippy> | `boolean` | No | `false` |\n| **variant** <Tippy>Color theme variant for the component</Tippy> | `'primary' \\| 'secondary'` | No | `'primary'` |\n| **backgroundGlowVariant** <Tippy>Color theme variant for the background glow effect</Tippy> | `'primary' \\| 'secondary'` | No | `'primary'` |\n| **children** <Tippy>Child components to render (used when not using the members array)</Tippy> | `React.ReactNode` | No | `-` |\n\n### LandingTeamMember\n\n| Prop Name | Prop Type | Required | Default |\n| --- | --- | ----- | ---- |\n| **className** <Tippy>Additional CSS classes for the team member container</Tippy> | `string` | No | `-` |\n| **member** <Tippy>Team member data object</Tippy> | `TeamMember` | Yes | `-` |\n| **imageClassName** <Tippy>Additional CSS classes for the team member's image</Tippy> | `string` | No | `-` |\n\n### TeamMember Interface\n\n```ts\nexport interface TeamMember {\n  name: string;\n  role: string;\n  imageSrc: string;\n}\n```\n</PropsReference>"}, "TestimonialGrid": {"description": "Use this component to display a grid of testimonials.<br/> This component accepts a title, description and a list of testimonials. They will be placed in a column layout on small screens, then a 2-column layout and finally a 3-column layout on large screens.<br/> Each testimonial can be featured or not. The featured testimonial will stand out with bigger & bolder text. Testimonials are a great way to show that other people have used your product and are happy with it. Consider adding it high up on your landing page.", "api": "<Usage>\n```jsx\nimport { LandingTestimonialGrid } from '@/components/landing/testimonial/LandingTestimonialGrid';\n```\n\n```jsx\nconst testimonialItems = [\n  {\n    name: '<PERSON><PERSON>',\n    text: 'After using this, I cannot imagine going back to the old way of doing things.',\n    handle: '@heymatt_oo',\n    imageSrc: 'https://picsum.photos/100/100.webp?random=2',\n  },\n  {\n    name: '<PERSON>',\n    text: 'Perfect for my use case',\n    handle: '@joshua',\n    imageSrc: 'https://picsum.photos/100/100.webp?random=3',\n  },\n  {\n    name: 'Parl <PERSON>ppa',\n    text: 'This is the best thing since sliced bread. I cannot believe I did not think of it myself.',\n    handle: '@coppalipse',\n    imageSrc: 'https://picsum.photos/100/100.webp?random=1',\n    featured: true, // Feature this testimonial\n  },\n  {\n    name: 'Mandy',\n    text: 'Excellent product!',\n    handle: '@mandy',\n    imageSrc: 'https://picsum.photos/100/100.webp?random=4',\n  },\n  {\n    name: '<PERSON>',\n    text: 'Can easily recommend!',\n    handle: '@alex',\n    imageSrc: 'https://picsum.photos/100/100.webp?random=5',\n  },\n  {\n    name: 'Sam',\n    text: 'I am very happy with the results.',\n    handle: '@sama',\n    imageSrc: 'https://picsum.photos/100/100.webp?random=6',\n  },\n];\n```\n\n```jsx\n<LandingTestimonialGrid\n  title=\"Don't take it from us\"\n  description=\"See what other people have to say.\"\n  testimonialItems={testimonialItems}\n/>\n```\n</Usage>\n\n<Examples>\n### Background, links and features\n\nThis component supports different background colors.\n\nHere we set <b>variant</b> to <b>secondary</b>.\nTestimonials can also be linked + be featured and you can mix and match to send\nthe desired message.\n\n\n\n```jsx\nimport { LandingTestimonialGrid } from '@/components/landing/testimonial/LandingTestimonialGrid';\n\nconst testimonialItems = [\n    {\n      name: 'Mathew',\n      text: 'After using this, I cannot imagine going back to the old way of doing things.',\n      handle: '@heymatt_oo',\n      imageSrc: 'https://picsum.photos/100/100.webp?random=2',\n      featured: true, // Feature this testimonial\n    },\n    {\n      name: 'Joshua',\n      text: 'Perfect for my use case',\n      handle: '@joshua',\n      imageSrc: 'https://picsum.photos/100/100.webp?random=3',\n    },\n    {\n      name: 'Parl Coppa',\n      text: 'This is the best thing since sliced bread. I cannot believe I did not think of it myself.',\n      handle: '@coppalipse',\n      imageSrc: 'https://picsum.photos/100/100.webp?random=1',\n    },\n    {\n      name: 'Mandy',\n      text: 'Excellent product!',\n      handle: '@mandy',\n      imageSrc: 'https://picsum.photos/100/100.webp?random=4',\n      featured: true, // Feature this testimonial\n    },\n    {\n      name: 'Alex',\n      text: 'Can easily recommend this product! I am very happy with the results.',\n      handle: '@alex',\n      imageSrc: 'https://picsum.photos/100/100.webp?random=5',\n      featured: true, // Feature this testimonial\n    },\n    {\n      name: 'Sam',\n      text: 'I am very happy with the results.',\n      handle: '@sama',\n      imageSrc: 'https://picsum.photos/100/100.webp?random=6',\n    },\n  ]\n\n<LandingTestimonialGrid\n  withBackground\n  variant=\"secondary\"\n  title=\"Don't take it from us\"\n  description=\"See what other people have to say.\"\n  testimonialItems={testimonialItems}\n/>\n```\n\n\n\n### Background Glow\n\n\n\n```jsx\nimport { LandingTestimonialGrid } from '@/components/landing/testimonial/LandingTestimonialGrid';\n\nconst testimonialItems = [\n    {\n      name: 'Mathew',\n      text: 'After using this, I cannot imagine going back to the old way of doing things.',\n      handle: '@heymatt_oo',\n      imageSrc: 'https://picsum.photos/100/100.webp?random=2',\n    },\n    {\n      name: 'Joshua',\n      text: 'Perfect for my use case',\n      handle: '@joshua',\n      imageSrc: 'https://picsum.photos/100/100.webp?random=3',\n    },\n    {\n      name: 'Parl Coppa',\n      text: 'This is the best thing since sliced bread. I cannot believe I did not think of it myself.',\n      handle: '@coppalipse',\n      imageSrc: 'https://picsum.photos/100/100.webp?random=1',\n      featured: true, // Feature this testimonial\n    },\n    {\n      name: 'Mandy',\n      text: 'Excellent product!',\n      handle: '@mandy',\n      imageSrc: 'https://picsum.photos/100/100.webp?random=4',\n    },\n    {\n      name: 'Alex',\n      text: 'Can easily recommend!',\n      handle: '@alex',\n      imageSrc: 'https://picsum.photos/100/100.webp?random=5',\n    },\n    {\n      name: 'Sam',\n      text: 'I am very happy with the results.',\n      handle: '@sama',\n      imageSrc: 'https://picsum.photos/100/100.webp?random=6',\n    },\n  ]\n\n<LandingTestimonialGrid\n  title=\"Don't take it from us\"\n  description=\"See what other people have to say.\"\n  testimonialItems={testimonialItems}\n/>\n```\n\n\n\n### Read more wrapper\n\nIf your testimonials exceed 2 rows, you can add a \"Read more\" wrapper to\nhide the rest of the content initially. <br />\nThis is usually a good idea to keep the page clean and focused.\n\n\n\n```jsx\nimport { LandingTestimonialGrid } from '@/components/landing/testimonial/LandingTestimonialGrid';\nimport { LandingTestimonialReadMoreWrapper } from '@/components/landing/testimonial/LandingTestimonialReadMoreWrapper';\n\nconst testimonialItems = [\n  {\n    name: 'Mathew',\n    text: 'After using this, I cannot imagine going back to the old way of doing things.',\n    handle: '@heymatt_oo',\n    imageSrc: 'https://picsum.photos/100/100.webp?random=2',\n  },\n  {\n    name: 'Joshua',\n    text: 'Perfect for my use case',\n    handle: '@joshua',\n    imageSrc: 'https://picsum.photos/100/100.webp?random=3',\n  },\n  {\n    name: 'Parl Coppa',\n    text: 'This is the best thing since sliced bread. I cannot believe I did not think of it myself.',\n    handle: '@coppalipse',\n    imageSrc: 'https://picsum.photos/100/100.webp?random=1',\n    featured: true, // Feature this testimonial\n  },\n  {\n    name: 'Mandy',\n    text: 'Excellent product!',\n    handle: '@mandy',\n    imageSrc: 'https://picsum.photos/100/100.webp?random=4',\n  },\n  {\n    name: 'Alex',\n    text: 'Can easily recommend!',\n    handle: '@alex',\n    imageSrc: 'https://picsum.photos/100/100.webp?random=5',\n  },\n  {\n    name: 'Sam',\n    text: 'I am very happy with the results.',\n    handle: '@sama',\n    imageSrc: 'https://picsum.photos/100/100.webp?random=6',\n  },\n]\n\n<LandingTestimonialReadMoreWrapper size=\"md\">\n  <LandingTestimonialGrid\n    title=\"Don't take it from us\"\n    description=\"See what other people have to say.\"\n    testimonialItems={testimonialItems}\n  />\n</LandingTestimonialReadMoreWrapper>\n```\n\n\n</Examples>\n\n<PropsReference>\n| Prop Name                                                                                                                                      | Prop Type                    | Required | Default     |\n| ---------------------------------------------------------------------------------------------------------------------------------------------- | ---------------------------- | -------- | ----------- |\n| **title** <Tippy>String or React nodes for the title of the testimonial grid.</Tippy>                                                          | `string` ǀ `React.ReactNode` | No       | -           |\n| **description** <Tippy>String or React nodes for the description of the testimonial grid.</Tippy>                                              | `string` ǀ `React.ReactNode` | No       | -           |\n| **testimonialItems** <Tippy>An array of `TestimonialItem` objects representing the testimonials to be displayed.</Tippy>                       | `Array<TestimonialItem>`     | Yes      | -           |\n| **featuredTestimonial** <Tippy>The featured testimonial to be displayed with special styling. It should be a `TestimonialItem` object.</Tippy> | `TestimonialItem`            | No       | -           |\n| **withBackground** <Tippy>A boolean indicating whether to display the testimonial grid with a background.</Tippy>                              | `boolean`                    | No       | -           |\n| **variant** <Tippy>The color variant of the background. It can be either `'primary'` or `'secondary'`.</Tippy>                                 | `'primary'` ǀ `'secondary'`  | No       | `'primary'` |\n| **withBackgroundGlow** <Tippy>A boolean indicating whether to add a glowing effect to the background of the testimonial grid.</Tippy>          | `boolean`                    | No       | `false`     |\n| **backgroundGlowVariant** <Tippy>The color variant of the background glow effect. It can be either `'primary'` or `'secondary'`.</Tippy>       | `'primary'` ǀ `'secondary'`  | No       | `'primary'` |\n\n```ts\nexport interface TestimonialItem {\n  className?: string;\n  url?: string;\n  text: string;\n  imageSrc: string;\n  name: string;\n  handle: string;\n  featured?: boolean;\n  verified?: boolean;\n  size?: 'full' | 'half' | 'third'; // NB: Only applies to testimonials in a list, not grid.\n}\n```\n</PropsReference>"}, "TestimonialInlineItem": {"description": "Use this component to display a single testimonial inline. Use this to highlight short customer testimonials or reviews. are meant as short validation and are usually support for a primary or secondary Call to action. Can be used with [Testimonial Inline](/boilerplate-documentation/landing-page-components/testimonial-inline).", "api": "<Usage>\n```jsx\nimport { LandingTestimonialInlineItem } from '@/components/landing/testimonial/LandingTestimonialInlineItem';\n```\n\n```jsx\n<LandingTestimonialInlineItem\n  imageSrc=\"https://picsum.photos/id/65/100/100\"\n  name=\"<PERSON>\"\n  text=\"Best app ever\"\n/>\n```\n</Usage>\n\n<Examples>\n### With Testimonial Inline Wrapper\n\n\n\n```jsx\nimport { LandingTestimonialInline } from '@/components/landing/testimonial/LandingTestimonialInline';\nimport { LandingTestimonialInlineItem } from '@/components/landing/testimonial/LandingTestimonialInlineItem';\n\n<LandingTestimonialInline withBackground variant=\"secondary\">\n  <LandingTestimonialInlineItem\n    imageSrc=\"https://picsum.photos/id/64/100/100\"\n    name=\"<PERSON>\"\n    text=\"I love this app\"\n  />\n\n  <LandingTestimonialInlineItem\n    imageSrc=\"https://picsum.photos/id/65/100/100\"\n    name=\"<PERSON>\"\n    text=\"Best app on the market\"\n  />\n\n  <LandingTestimonialInlineItem\n    imageSrc=\"https://picsum.photos/id/669/100/100\"\n    name=\"<PERSON>\"\n    text=\"Never seen anything like it\"\n    suffix=\"CEO of Instagram\"\n  />\n\n  <LandingTestimonialInlineItem\n    imageSrc=\"https://picsum.photos/id/829/100/100\"\n    name=\"Guido Ross\"\n    text=\"Nothing comes close to it\"\n    suffix=\"DevOps at Meta\"\n  />\n</LandingTestimonialInline>;\n```\n\n\n</Examples>\n\n<PropsReference>\n| Prop Name                                                             | Prop Type | Required | Default |\n| --------------------------------------------------------------------- | --------- | -------- | ------- |\n| **imageSrc** <Tippy>Image source URL for the avatar.</Tippy>          | `string`  | Yes      | -       |\n| **text** <Tippy>Text content of the testimonial.</Tippy>              | `string`  | Yes      | -       |\n| **name** <Tippy>Name of the person providing the testimonial.</Tippy> | `string`  | Yes      | -       |\n| **suffix** <Tippy>Optional suffix to append to the name.</Tippy>      | `string`  | No       | -       |\n</PropsReference>"}, "TestimonialInline": {"description": "Use this to highlight short customer testimonials or reviews. These are not meant for reviews, but short validation and are usually support for a primary or secondary Call to action. It displays an inline grid of short testimonials.", "api": "<Usage>\n```jsx\nimport { LandingTestimonialInline } from '@/components/landing/testimonial/LandingTestimonialInline';\nimport { LandingTestimonialInlineItem } from '@/components/landing/testimonial/LandingTestimonialInlineItem';\n```\n\n```jsx\n<LandingTestimonialInline>\n  <LandingTestimonialInlineItem\n    imageSrc=\"https://picsum.photos/id/64/100/100\"\n    name=\"<PERSON>\"\n    text=\"I love this app\"\n  />\n\n  <LandingTestimonialInlineItem\n    imageSrc=\"https://picsum.photos/id/65/100/100\"\n    name=\"<PERSON>\"\n    text=\"Best app ever\"\n  />\n\n  <LandingTestimonialInlineItem\n    imageSrc=\"https://picsum.photos/id/669/100/100\"\n    name=\"<PERSON>\"\n    text=\"Fantastic\"\n    suffix=\"CEO of Instagram\"\n  />\n\n  <LandingTestimonialInlineItem\n    imageSrc=\"https://picsum.photos/id/829/100/100\"\n    name=\"<PERSON>\"\n    text=\"Recommended\"\n    suffix=\"DevOps at Meta\"\n  />\n</LandingTestimonialInline>\n```\n</Usage>\n\n<Examples>\n### Background Color\n\n\n\n```jsx\nimport { LandingTestimonialInline } from '@/components/landing/testimonial/LandingTestimonialInline';\nimport { LandingTestimonialInlineItem } from '@/components/landing/testimonial/LandingTestimonialInlineItem';\n\n<LandingTestimonialInline withBackground variant=\"secondary\">\n  <LandingTestimonialInlineItem\n    imageSrc=\"https://picsum.photos/id/64/100/100\"\n    name=\"John Doe\"\n    text=\"I love this app\"\n  />\n\n  <LandingTestimonialInlineItem\n    imageSrc=\"https://picsum.photos/id/65/100/100\"\n    name=\"Jane Doe\"\n    text=\"Best app on the market\"\n  />\n\n  <LandingTestimonialInlineItem\n    imageSrc=\"https://picsum.photos/id/669/100/100\"\n    name=\"Alice Doe\"\n    text=\"Never seen anything like it\"\n    suffix=\"CEO of Instagram\"\n  />\n\n  <LandingTestimonialInlineItem\n    imageSrc=\"https://picsum.photos/id/829/100/100\"\n    name=\"Guido Ross\"\n    text=\"Nothing comes close to it\"\n    suffix=\"DevOps at Meta\"\n  />\n</LandingTestimonialInline>;\n```\n\n\n\n### With Primary CTA\n\n\n\n```jsx\nimport { LandingTestimonialInline } from '@/components/landing/testimonial/LandingTestimonialInline';\nimport { LandingTestimonialInlineItem } from '@/components/landing/testimonial/LandingTestimonialInlineItem';\nimport { LandingPrimaryImageCtaSection } from '@/components/landing/cta/LandingPrimaryCta';\n\n<LandingPrimaryImageCtaSection\n  title=\"Beautiful landing pages in minutes\"\n  description=\"Get your landing page up and running with a few clicks.\"\n  imageSrc=\"/static/images/product-sample.webp\"\n  imageAlt=\"Sample image\"\n  withBackground\n  footerComponent={\n    <LandingTestimonialInline>\n      <LandingTestimonialInlineItem\n        imageSrc=\"https://picsum.photos/id/64/100/100\"\n        name=\"John Doe\"\n        text=\"I love this app\"\n      />\n\n      <LandingTestimonialInlineItem\n        imageSrc=\"https://picsum.photos/id/65/100/100\"\n        name=\"Jane Doe\"\n        text=\"Best app on the market\"\n      />\n\n      <LandingTestimonialInlineItem\n        imageSrc=\"https://picsum.photos/id/669/100/100\"\n        name=\"Alice Doe\"\n        text=\"Never seen anything like it\"\n        suffix=\"CEO of Instagram\"\n      />\n\n      <LandingTestimonialInlineItem\n        imageSrc=\"https://picsum.photos/id/829/100/100\"\n        name=\"Guido Ross\"\n        text=\"Nothing comes close to it\"\n        suffix=\"DevOps at Meta\"\n      />\n    </LandingTestimonialInline>\n  }\n>\n  <Button size=\"xl\" asChild>\n    <a href=\"#\">Sign up</a>\n  </Button>\n\n  <Button size=\"xl\" variant=\"outlinePrimary\">\n    <a href=\"#\">See demo</a>\n  </Button>\n</LandingPrimaryImageCtaSection>;\n```\n\n\n</Examples>\n\n<PropsReference>\n| Prop Name                                                                                                                             | Prop Type                             | Required | Default       |\n| ------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------- | -------- | ------------- |\n| **children** <Tippy>React nodes to be rendered within the component (below the text on smaller screen, right side on larger).</Tippy> | `React.ReactNode` ǀ `string`          | No       | -             |\n| **withBackground** <Tippy>Boolean indicating whether to display with a background or not.</Tippy>                                     | `boolean`                             | No       | `false`       |\n| **variant** <Tippy>String defining the variant of the component. Possible values are 'primary' or 'secondary'.</Tippy>                | `'primary'` ǀ `'secondary'`           | No       | `'primary'`   |\n| **containerType** <Tippy>String defining the type of container. Possible values are 'narrow', 'wide', or 'ultrawide'.</Tippy>         | `'narrow'` ǀ `'wide'` ǀ `'ultrawide'` | No       | `'ultrawide'` |\n</PropsReference>"}, "TestimonialList": {"description": "Use this component to display a list of testimonials. Each testimonial has text, a name, and a picture of the person. Testimonials are a great way to show that other people have used your product and are happy with it. Consider adding it high up on your landing page. Shorter testimonials can be made smaller by setting the `size` prop to `half` or `third`. The default size is `full`.", "api": "<Usage>\n```jsx\nimport { LandingTestimonialListSection } from '@/components/landing/testimonial/LandingTestimonialList';\n```\n\n```jsx\nconst testimonialItems = [\n  {\n    name: '<PERSON><PERSON>',\n    text: 'This is the best thing since sliced bread. I cannot believe I did not think of it myself.',\n    handle: '@coppalipse',\n    imageSrc: 'https://picsum.photos/100/100.webp?random=1',\n    size: 'half',\n  },\n  {\n    name: '<PERSON><PERSON>',\n    text: 'After using this, I cannot imagine going back to the old way of doing things.',\n    handle: '@heymatt_oo',\n    imageSrc: 'https://picsum.photos/100/100.webp?random=2',\n    size: 'half',\n  },\n  {\n    name: '<PERSON>',\n    text: 'Perfect for my use case',\n    handle: '@joshua',\n    imageSrc: 'https://picsum.photos/100/100.webp?random=3',\n    size: 'third',\n  },\n  {\n    name: '<PERSON>',\n    text: 'Excellent product!',\n    handle: '@mandy',\n    imageSrc: 'https://picsum.photos/100/100.webp?random=4',\n    size: 'third',\n  },\n  {\n    name: '<PERSON>',\n    text: 'Can easily recommend!',\n    handle: '@alex',\n    imageSrc: 'https://picsum.photos/100/100.webp?random=5',\n    size: 'third',\n  },\n];\n```\n\n```jsx\n<LandingTestimonialListSection\n  title=\"Don't take it from us\"\n  description=\"See what other people have to say.\"\n  testimonialItems={testimonialItems}\n/>\n```\n</Usage>\n\n<Examples>\n### Background, links and features\n\nThis component supports different background colors.\n\nHere we set <b>variant</b> to <b>secondary</b>. <br />\nTestimonials can also be linked + be featured and you can mix and match to\nsend the desired message.\n\n\n\n```jsx\nimport { LandingTestimonialListSection } from '@/components/landing/testimonial/LandingTestimonialList';\n\nconst testimonialItems = [\n  {\n    name: 'Parl Coppa',\n    text: 'This is the best thing since sliced bread. I cannot believe I did not think of it myself.',\n    handle: '@coppalipse',\n    imageSrc: 'https://picsum.photos/100/100.webp?random=1',\n    featured: true, // Feature this testimonial\n    url: 'https://example.com', // Link the testimonial\n  },\n  {\n    name: 'Mathew',\n    text: 'After using this, I cannot imagine going back to the old way of doing things.',\n    handle: '@heymatt_oo',\n    imageSrc: 'https://picsum.photos/100/100.webp?random=2',\n    size: 'half',\n    url: 'https://example.com',\n  },\n  {\n    name: 'Joshua',\n    text: 'Perfect for my use case',\n    handle: '@joshua',\n    imageSrc: 'https://picsum.photos/100/100.webp?random=3',\n    size: 'half',\n  },\n]\n\n<LandingTestimonialListSection\n  withBackground\n  variant=\"secondary\"\n  title=\"Don't take it from us\"\n  description=\"See what other people have to say.\"\n  testimonialItems={testimonialItems}\n/>\n```\n\n\n\n### With Background Glow\n\n\n\n```jsx\nimport { LandingTestimonialListSection } from '@/components/landing/testimonial/LandingTestimonialList';\n\nconst testimonialItems = [\n  {\n    name: 'Parl Coppa',\n    text: 'This is the best thing since sliced bread. I cannot believe I did not think of it myself.',\n    handle: '@coppalipse',\n    imageSrc: 'https://picsum.photos/100/100.webp?random=1',\n    featured: true, // Feature this testimonial\n    url: 'https://example.com', // Link the testimonial\n  },\n  {\n    name: 'Mathew',\n    text: 'After using this, I cannot imagine going back to the old way of doing things.',\n    handle: '@heymatt_oo',\n    imageSrc: 'https://picsum.photos/100/100.webp?random=2',\n    size: 'half',\n    url: 'https://example.com',\n  },\n  {\n    name: 'Joshua',\n    text: 'Perfect for my use case',\n    handle: '@joshua',\n    imageSrc: 'https://picsum.photos/100/100.webp?random=3',\n    size: 'half',\n  },\n]\n\n<LandingTestimonialListSection\n  withBackgroundGlow\n  backgroundGlowVariant=\"secondary\"\n  title=\"Don't take it from us\"\n  description=\"See what other people have to say.\"\n  testimonialItems={testimonialItems}\n/>\n```\n\n\n\n### With Read More Wrapper\n\n\n\n```jsx\nimport { LandingTestimonialListSection } from '@/components/landing/testimonial/LandingTestimonialList';\nimport { LandingTestimonialReadMoreWrapper } from '@/components/landing/testimonial/LandingTestimonialReadMoreWrapper';\n\nconst testimonialItems = [\n  {\n    name: 'Parl Coppa',\n    text: 'This is the best thing since sliced bread. I cannot believe I did not think of it myself.',\n    handle: '@coppalipse',\n    imageSrc: 'https://picsum.photos/100/100.webp?random=1',\n    featured: true, // Feature this testimonial\n    url: 'https://example.com', // Link the testimonial\n  },\n  {\n    name: 'Mathew',\n    text: 'After using this, I cannot imagine going back to the old way of doing things.',\n    handle: '@heymatt_oo',\n    imageSrc: 'https://picsum.photos/100/100.webp?random=2',\n    size: 'half',\n    url: 'https://example.com',\n  },\n  {\n    name: 'Joshua',\n    text: 'Perfect for my use case',\n    handle: '@joshua',\n    imageSrc: 'https://picsum.photos/100/100.webp?random=3',\n    size: 'half',\n  },\n]\n\n<LandingTestimonialReadMoreWrapper size=\"md\">\n  <LandingTestimonialListSection\n    withBackgroundGlow\n    backgroundGlowVariant=\"secondary\"\n    title=\"Don't take it from us\"\n    description=\"See what other people have to say.\"\n    testimonialItems={testimonialItems}\n  />\n</LandingTestimonialReadMoreWrapper>\n```\n\n\n</Examples>\n\n<PropsReference>\n| Prop Name                                                                                                    | Prop Type                    | Required | Default     |\n| ------------------------------------------------------------------------------------------------------------ | ---------------------------- | -------- | ----------- |\n| **title** <Tippy>React nodes or string for the section title.</Tippy>                                        | `string` ǀ `React.ReactNode` | No       | -           |\n| **titleComponent** <Tippy>Custom React component for the section title.</Tippy>                              | `React.ReactNode`            | No       | -           |\n| **description** <Tippy>React nodes or string for the section description.</Tippy>                            | `string` ǀ `React.ReactNode` | No       | -           |\n| **descriptionComponent** <Tippy>Custom React component for the section description.</Tippy>                  | `React.ReactNode`            | No       | -           |\n| **testimonialItems** <Tippy>An array of objects representing testimonial items.</Tippy>                      | `TestimonialItem[]`          | Yes      | -           |\n| **withBackground** <Tippy>Boolean to determine whether to display section background or not.</Tippy>         | `boolean`                    | No       | `false`     |\n| **variant** <Tippy>String defining the variant of the section background.</Tippy>                            | `'primary'` ǀ `'secondary'`  | No       | `'primary'` |\n| **withBackgroundGlow** <Tippy>Boolean to determine whether to display background glow effect or not.</Tippy> | `boolean`                    | No       | `false`     |\n| **backgroundGlowVariant** <Tippy>String defining the variant of the background glow effect.</Tippy>          | `'primary'` ǀ `'secondary'`  | No       | `'primary'` |\n\n```ts\nexport interface TestimonialItem {\n  className?: string;\n  url?: string;\n  text: string;\n  imageSrc: string;\n  name: string;\n  handle: string;\n  featured?: boolean;\n  verified?: boolean;\n  size?: 'full' | 'half' | 'third'; // NB: Only applies to testimonials in a list, not grid.\n}\n```\n</PropsReference>"}, "Testimonial": {"description": "Use this component to display a single testimonial. It has text, a name, and a picture of the person. This is used as part of [Testimonial Lists](/boilerplate-documentation/landing-page-components/testimonial-list) and [Testimonial Grids](/boilerplate-documentation/landing-page-components/testimonial-grid).", "api": "<Usage>\n```jsx\nimport { LandingTestimonial } from '@/components/landing/testimonial/LandingTestimonial';\n```\n\n```\n<LandingTestimonial\n  name={'<PERSON>'}\n  text={'Excellent product. I love it!'}\n  handle={'@mandy'}\n  imageSrc={'https://picsum.photos/100/100.webp?random=3'}\n  hideFooter\n/>\n```\n</Usage>\n\n<Examples>\n### With Testimonial Grid\n\n\n\n```jsx\nimport { LandingTestimonialGrid } from '@/components/landing/testimonial/LandingTestimonialGrid';\n\nconst testimonialItems = [\n  {\n    name: '<PERSON><PERSON>',\n    text: 'After using this, I cannot imagine going back to the old way of doing things.',\n    handle: '@heymatt_oo',\n    imageSrc: 'https://picsum.photos/100/100.webp?random=2',\n  },\n  {\n    name: '<PERSON>',\n    text: 'Perfect for my use case',\n    handle: '@joshua',\n    imageSrc: 'https://picsum.photos/100/100.webp?random=3',\n  },\n  {\n    name: '<PERSON><PERSON>',\n    text: 'This is the best thing since sliced bread. I cannot believe I did not think of it myself.',\n    handle: '@coppalipse',\n    imageSrc: 'https://picsum.photos/100/100.webp?random=1',\n    featured: true, // Feature this testimonial\n  },\n  {\n    name: 'Mandy',\n    text: 'Excellent product!',\n    handle: '@mandy',\n    imageSrc: 'https://picsum.photos/100/100.webp?random=4',\n  },\n  {\n    name: 'Alex',\n    text: 'Can easily recommend!',\n    handle: '@alex',\n    imageSrc: 'https://picsum.photos/100/100.webp?random=5',\n  },\n  {\n    name: 'Sam',\n    text: 'I am very happy with the results.',\n    handle: '@sama',\n    imageSrc: 'https://picsum.photos/100/100.webp?random=6',\n  },\n];\n\n<LandingTestimonialGrid\n  title=\"Don't take it from us\"\n  description=\"See what other people have to say.\"\n  testimonialItems={testimonialItems}\n/>;\n```\n\n\n\n### With Testimonial List\n\n\n\n```jsx\nimport { LandingTestimonialListSection } from '@/components/landing/testimonial/LandingTestimonialList';\n\nconst testimonialItems = [\n  {\n    name: 'Parl Coppa',\n    text: 'This is the best thing since sliced bread. I cannot believe I did not think of it myself.',\n    handle: '@coppalipse',\n    imageSrc: 'https://picsum.photos/100/100.webp?random=1',\n    size: 'half',\n  },\n  {\n    name: 'Mathew',\n    text: 'After using this, I cannot imagine going back to the old way of doing things.',\n    handle: '@heymatt_oo',\n    imageSrc: 'https://picsum.photos/100/100.webp?random=2',\n    size: 'half',\n  },\n  {\n    name: 'Joshua',\n    text: 'Perfect for my use case',\n    handle: '@joshua',\n    imageSrc: 'https://picsum.photos/100/100.webp?random=3',\n    size: 'third',\n  },\n  {\n    name: 'Mandy',\n    text: 'Excellent product!',\n    handle: '@mandy',\n    imageSrc: 'https://picsum.photos/100/100.webp?random=4',\n    size: 'third',\n  },\n  {\n    name: 'Alex',\n    text: 'Can easily recommend!',\n    handle: '@alex',\n    imageSrc: 'https://picsum.photos/100/100.webp?random=5',\n    size: 'third',\n  },\n]\n\n<LandingTestimonialListSection\n  title=\"Don't take it from us\"\n  description=\"See what other people have to say.\"\n  testimonialItems={testimonialItems}\n/>\n```\n\n\n</Examples>\n\n<PropsReference>\n| Prop Name                                                                                                                               | Prop Type                       | Required | Default |\n| --------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------- | -------- | ------- |\n| **url** <Tippy>The URL to navigate to when the testimonial is clicked.</Tippy>                                                          | `string`                        | Yes      | -       |\n| **text** <Tippy>The main text content of the testimonial.</Tippy>                                                                       | `string`                        | Yes      | -       |\n| **imageSrc** <Tippy>The URL of the image to be displayed alongside the testimonial.</Tippy>                                             | `string`                        | Yes      | -       |\n| **name** <Tippy>The name of the person providing the testimonial.</Tippy>                                                               | `string`                        | Yes      | -       |\n| **handle** <Tippy>The handle or username associated with the person providing the testimonial.</Tippy>                                  | `string`                        | Yes      | -       |\n| **featured** <Tippy>Whether the testimonial is featured or not.</Tippy>                                                                 | `boolean`                       | No       | -       |\n| **verified** <Tippy>Whether the testimonial is verified or not.</Tippy>                                                                 | `boolean`                       | No       | `true`  |\n| **size** <Tippy>The size of the testimonial (`full`, `half`, or `third`). NB: Only applies to testimonials in a list, not grid.</Tippy> | `'full'   ǀ 'half'   ǀ 'third'` | No       | -       |\n</PropsReference>"}}